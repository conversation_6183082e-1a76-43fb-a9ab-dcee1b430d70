\documentclass[12pt,a4paper]{article}
\usepackage[utf8]{inputenc}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{graphicx}
\usepackage{algorithm}
\usepackage{algorithmic}
\usepackage{geometry}
\usepackage{hyperref}
\usepackage{listings}
\usepackage{xcolor}

\geometry{margin=1in}

\title{Enhanced Door State Detection Algorithm for VisionGuard System}
\author{VisionGuard Development Team}
\date{\today}

\begin{document}

\maketitle

\section{Abstract}
This document presents an enhanced multi-modal door state detection algorithm that accurately distinguishes between open and closed doors using computer vision techniques. The algorithm combines reference image comparison, depth analysis, texture complexity, pattern recognition, and color distribution analysis to achieve high accuracy in real-world scenarios.

\section{Problem Statement}
The original VisionGuard system could detect doors but failed to accurately determine their state (open vs. closed). This limitation significantly impacted navigation guidance for blind users, as the system would incorrectly classify open doors as closed, leading to confusion and potential safety issues.

\section{Enhanced Algorithm Overview}

\subsection{Multi-Modal Approach}
The enhanced algorithm employs five complementary detection methods:

\begin{enumerate}
    \item \textbf{Enhanced Reference Image Comparison} - Primary method using 166 reference images
    \item \textbf{Depth Discontinuity Analysis} - Detects depth variations indicating openings
    \item \textbf{Texture Complexity Analysis} - Measures texture patterns
    \item \textbf{Opening Pattern Detection} - Identifies geometric patterns of door openings
    \item \textbf{Color Distribution Analysis} - Analyzes color variance patterns
\end{enumerate}

\subsection{Mathematical Framework}

Let $I$ be the input door region image, and let $S_i$ represent the score from method $i$. The final confidence scores are calculated as:

\begin{align}
S_{open} &= 0.45 \cdot S_{ref} + 0.25 \cdot S_{depth} + 0.15 \cdot S_{texture} + 0.10 \cdot S_{pattern} + 0.05 \cdot S_{color} \\
S_{closed} &= 0.40 \cdot (1-S_{ref}) + 0.30 \cdot (1-S_{depth}) + 0.20 \cdot (1-S_{texture}) + 0.10 \cdot (1-S_{pattern})
\end{align}

\section{Method 1: Enhanced Reference Image Comparison}

\subsection{Multi-Scale Histogram Analysis}
For a door image $I$ and reference image set $\mathcal{R} = \{R_1, R_2, ..., R_{166}\}$:

\begin{algorithm}
\caption{Enhanced Reference Comparison}
\begin{algorithmic}[1]
\STATE Convert $I$ to grayscale $I_g$, HSV $I_{hsv}$
\STATE Calculate histograms: $H_g$, $H_h$, $H_s$
\FOR{each reference image $R_i \in \mathcal{R}$}
    \STATE Convert $R_i$ to $R_{i,g}$, $R_{i,hsv}$
    \STATE Calculate histograms: $H_{i,g}$, $H_{i,h}$, $H_{i,s}$
    \STATE $corr_g = \text{correlation}(H_g, H_{i,g})$
    \STATE $corr_h = \text{correlation}(H_h, H_{i,h})$
    \STATE $corr_s = \text{correlation}(H_s, H_{i,s})$
    \STATE $similarity_i = 0.5 \cdot corr_g + 0.3 \cdot corr_h + 0.2 \cdot corr_s$
\ENDFOR
\STATE Sort similarities in descending order
\STATE $S_{ref} = \frac{1}{1 + e^{-10(\bar{S}_{top10} - 0.5)}}$ \COMMENT{Sigmoid enhancement}
\end{algorithmic}
\end{algorithm}

\subsection{Sigmoid Enhancement Function}
The sigmoid transformation enhances discrimination:
$$S_{enhanced} = \frac{1}{1 + e^{-10(S_{raw} - 0.5)}}$$

This function amplifies differences around the decision boundary (0.5), making good matches closer to 1.0 and poor matches closer to 0.0.

\section{Method 2: Depth Discontinuity Analysis}

Open doors create depth discontinuities that can be detected using gradient analysis:

\begin{align}
G_x &= \nabla_x I_g = I_g * K_x \\
G_y &= \nabla_y I_g = I_g * K_y \\
|\nabla I_g| &= \sqrt{G_x^2 + G_y^2} \\
S_{depth} &= \min(2 \cdot \text{mean}(|\nabla I_g|), 1.0)
\end{align}

where $K_x$ and $K_y$ are Sobel kernels for horizontal and vertical edge detection.

\section{Method 3: Texture Complexity Analysis}

Using Local Binary Patterns (LBP) to measure texture complexity:

\begin{algorithm}
\caption{Texture Complexity Calculation}
\begin{algorithmic}[1]
\STATE Initialize $lbp\_score = 0$
\FOR{each pixel $(i,j)$ in interior of image}
    \STATE $center = I_g(i,j)$
    \STATE $pattern = 0$
    \FOR{each of 8 neighbors $(i',j')$}
        \IF{$I_g(i',j') \geq center$}
            \STATE $pattern = pattern + 2^k$ where $k$ is neighbor index
        \ENDIF
    \ENDFOR
    \STATE $lbp\_score = lbp\_score + pattern$
\ENDFOR
\STATE $S_{texture} = \frac{lbp\_score}{(H-2)(W-2) \cdot 255}$
\end{algorithmic}
\end{algorithm}

\section{Method 4: Opening Pattern Detection}

Detects L-shaped patterns characteristic of door openings using morphological operations:

\begin{align}
E &= \text{Canny}(I_g, 30, 100) \\
L_h &= E \circ K_h \quad \text{(horizontal lines)} \\
L_v &= E \circ K_v \quad \text{(vertical lines)} \\
L_{combined} &= 0.5 \cdot L_h + 0.5 \cdot L_v \\
S_{pattern} &= \min(5 \cdot \frac{\sum L_{combined} > 0}{|L_{combined}|}, 1.0)
\end{align}

where $K_h$ and $K_v$ are rectangular morphological kernels for horizontal and vertical line detection.

\section{Method 5: Color Distribution Analysis}

Analyzes color variance in HSV space:

\begin{align}
\sigma_H^2 &= \text{Var}(I_{hsv}[:,:,0]) \quad \text{(Hue variance)} \\
\sigma_S^2 &= \text{Var}(I_{hsv}[:,:,1]) \quad \text{(Saturation variance)} \\
\sigma_V^2 &= \text{Var}(I_{hsv}[:,:,2]) \quad \text{(Value variance)} \\
S_{color} &= \min\left(\frac{\sigma_H^2 + \sigma_S^2 + \sigma_V^2}{30000}, 1.0\right)
\end{align}

\section{Decision Logic}

The final classification uses a hierarchical decision tree:

\begin{algorithm}
\caption{Door State Classification}
\begin{algorithmic}[1]
\IF{$S_{ref} > 0.7$}
    \RETURN ("open", $\min(S_{open}, 0.95)$)
\ELSIF{$S_{ref} < 0.3$ AND $S_{closed} > 0.65$}
    \RETURN ("closed", $\min(S_{closed}, 0.95)$)
\ELSIF{$S_{open} > 0.55$ AND $S_{open} > S_{closed}$}
    \RETURN ("open", $\min(S_{open}, 0.90)$)
\ELSIF{$S_{closed} > 0.65$ AND $S_{closed} > S_{open}$}
    \RETURN ("closed", $\min(S_{closed}, 0.90)$)
\ELSE
    \IF{$S_{ref} > 0.4$}
        \RETURN ("open", $\max(S_{open}, 0.6)$)
    \ELSE
        \RETURN ("unknown", $\max(S_{open}, S_{closed})$)
    \ENDIF
\ENDIF
\end{algorithmic}
\end{algorithm}

\section{Key Improvements}

\subsection{Reference Image Priority}
The algorithm prioritizes reference image comparison ($S_{ref}$) as the primary indicator, with a weight of 0.45 in the open door calculation. This ensures that doors matching the 166 reference images are correctly classified as open.

\subsection{Bias Toward Open Detection}
- Lower threshold for open detection (0.55 vs 0.65 for closed)
- When uncertain, bias toward "open" if reference similarity > 0.4
- This reduces false negatives for open door detection

\subsection{Robust Feature Fusion}
Multiple complementary features ensure robustness:
- Histogram correlation handles lighting variations
- Depth analysis detects 3D structure changes
- Texture analysis captures surface complexity
- Pattern detection identifies geometric features
- Color analysis provides additional discrimination

\section{Performance Characteristics}

\subsection{Computational Complexity}
- Reference comparison: $O(n \cdot h \cdot w)$ where $n$ is number of reference images
- Depth analysis: $O(h \cdot w)$
- Texture analysis: $O(h \cdot w)$
- Pattern detection: $O(h \cdot w \log(h \cdot w))$
- Color analysis: $O(h \cdot w)$

Total complexity: $O(n \cdot h \cdot w + h \cdot w \log(h \cdot w))$

\subsection{Accuracy Improvements}
- Reduced false positive rate for closed door detection
- Improved sensitivity to open door patterns
- Better handling of lighting and perspective variations
- Robust performance across different door types and environments

\section{Implementation Notes}

The algorithm is implemented in Python using OpenCV and NumPy. Key implementation details:

\begin{itemize}
    \item Reference images are loaded once at initialization
    \item Histogram calculations use optimized OpenCV functions
    \item Sigmoid enhancement improves discrimination
    \item Error handling ensures graceful degradation
    \item State history (5 frames) provides temporal stability
\end{itemize}

\section{Conclusion}

The enhanced door state detection algorithm significantly improves accuracy by combining multiple complementary analysis methods with proper weighting and decision logic. The reference image comparison serves as the primary indicator, while additional methods provide robustness and handle edge cases. This multi-modal approach ensures reliable door state detection for the VisionGuard navigation system.

\end{document}
