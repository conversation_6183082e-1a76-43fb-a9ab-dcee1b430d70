import cv2
import numpy as np
import os
import pickle
from pathlib import Path
import json
from datetime import datetime
from sklearn.ensemble import RandomForestClassifier
from sklearn.svm import SVC
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report
import joblib

class EnhancedDoorClassifier:
    def __init__(self, model_dir="door_models"):
        """
        Enhanced Door State Classifier using traditional computer vision + ML
        """
        self.model_dir = Path(model_dir)
        self.model_dir.mkdir(exist_ok=True)
        
        # Feature extraction parameters
        self.img_size = (224, 224)
        self.feature_dim = 512  # Total feature vector size
        
        # Models
        self.rf_model = None
        self.svm_model = None
        self.best_model = None
        self.model_type = None
        
        # Reference images for comparison
        self.open_door_features = []
        self.closed_door_features = []
        
    def extract_comprehensive_features(self, image):
        """
        Extract comprehensive features from door image
        """
        if image is None:
            return np.zeros(self.feature_dim)
        
        # Resize image
        img_resized = cv2.resize(image, self.img_size)
        gray = cv2.cvtColor(img_resized, cv2.COLOR_BGR2GRAY)
        
        features = []
        
        # 1. Histogram features (64 features)
        hist = cv2.calcHist([gray], [0], None, [64], [0, 256])
        hist = hist.flatten() / (self.img_size[0] * self.img_size[1])  # Normalize
        features.extend(hist)
        
        # 2. Edge density features (16 features)
        edges = cv2.Canny(gray, 50, 150)
        
        # Edge density in different regions
        h, w = edges.shape
        regions = [
            edges[:h//2, :w//2],      # Top-left
            edges[:h//2, w//2:],      # Top-right
            edges[h//2:, :w//2],      # Bottom-left
            edges[h//2:, w//2:],      # Bottom-right
            edges[:, w//4:3*w//4],    # Center vertical
            edges[h//4:3*h//4, :],    # Center horizontal
            edges[h//4:3*h//4, w//4:3*w//4],  # Center
            edges[:h//4, :],          # Top strip
            edges[3*h//4:, :],        # Bottom strip
            edges[:, :w//4],          # Left strip
            edges[:, 3*w//4:],        # Right strip
        ]
        
        for region in regions:
            if region.size > 0:
                edge_density = np.sum(region > 0) / region.size
                features.append(edge_density)
        
        # Pad to 16 features if needed
        while len(features) < 64 + 16:
            features.append(0.0)
        
        # 3. Texture features using Local Binary Pattern (32 features)
        def local_binary_pattern(image, radius=1, n_points=8):
            """Simple LBP implementation"""
            h, w = image.shape
            lbp = np.zeros((h, w), dtype=np.uint8)
            
            for i in range(radius, h - radius):
                for j in range(radius, w - radius):
                    center = image[i, j]
                    code = 0
                    for k in range(n_points):
                        angle = 2 * np.pi * k / n_points
                        x = int(i + radius * np.cos(angle))
                        y = int(j + radius * np.sin(angle))
                        if 0 <= x < h and 0 <= y < w:
                            if image[x, y] >= center:
                                code |= (1 << k)
                    lbp[i, j] = code
            return lbp
        
        lbp = local_binary_pattern(gray)
        lbp_hist, _ = np.histogram(lbp.ravel(), bins=32, range=(0, 256))
        lbp_hist = lbp_hist / lbp.size  # Normalize
        features.extend(lbp_hist)
        
        # 4. Geometric features (32 features)
        # Contour analysis
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if contours:
            # Largest contour features
            largest_contour = max(contours, key=cv2.contourArea)
            area = cv2.contourArea(largest_contour)
            perimeter = cv2.arcLength(largest_contour, True)
            
            # Geometric properties
            if perimeter > 0:
                circularity = 4 * np.pi * area / (perimeter * perimeter)
            else:
                circularity = 0
            
            # Bounding rectangle
            x, y, w_rect, h_rect = cv2.boundingRect(largest_contour)
            aspect_ratio = w_rect / h_rect if h_rect > 0 else 0
            extent = area / (w_rect * h_rect) if (w_rect * h_rect) > 0 else 0
            
            # Convex hull
            hull = cv2.convexHull(largest_contour)
            hull_area = cv2.contourArea(hull)
            solidity = area / hull_area if hull_area > 0 else 0
            
            geom_features = [
                area / (self.img_size[0] * self.img_size[1]),  # Normalized area
                perimeter / (2 * (self.img_size[0] + self.img_size[1])),  # Normalized perimeter
                circularity,
                aspect_ratio,
                extent,
                solidity,
                len(contours),  # Number of contours
                x / self.img_size[1],  # Normalized x
                y / self.img_size[0],  # Normalized y
                w_rect / self.img_size[1],  # Normalized width
                h_rect / self.img_size[0],  # Normalized height
            ]
        else:
            geom_features = [0.0] * 11
        
        # Pad to 32 geometric features
        while len(geom_features) < 32:
            geom_features.append(0.0)
        
        features.extend(geom_features[:32])
        
        # 5. Color distribution features (64 features)
        # HSV color features
        hsv = cv2.cvtColor(img_resized, cv2.COLOR_BGR2HSV)
        
        # Hue histogram
        h_hist = cv2.calcHist([hsv], [0], None, [32], [0, 180])
        h_hist = h_hist.flatten() / (self.img_size[0] * self.img_size[1])
        features.extend(h_hist)
        
        # Saturation histogram
        s_hist = cv2.calcHist([hsv], [1], None, [32], [0, 256])
        s_hist = s_hist.flatten() / (self.img_size[0] * self.img_size[1])
        features.extend(s_hist)
        
        # 6. Statistical features (64 features)
        # Mean, std, skewness, kurtosis for different channels
        channels = [gray, hsv[:,:,0], hsv[:,:,1], hsv[:,:,2]]
        
        for channel in channels:
            mean_val = np.mean(channel)
            std_val = np.std(channel)
            
            # Simple skewness and kurtosis approximation
            centered = channel - mean_val
            if std_val > 0:
                skew = np.mean((centered / std_val) ** 3)
                kurt = np.mean((centered / std_val) ** 4) - 3
            else:
                skew = 0
                kurt = 0
            
            # Percentiles
            p25 = np.percentile(channel, 25)
            p50 = np.percentile(channel, 50)
            p75 = np.percentile(channel, 75)
            
            # Min, max
            min_val = np.min(channel)
            max_val = np.max(channel)
            
            # Range and IQR
            range_val = max_val - min_val
            iqr = p75 - p25
            
            # Energy and entropy approximation
            hist_norm = cv2.calcHist([channel], [0], None, [256], [0, 256]).flatten()
            hist_norm = hist_norm / np.sum(hist_norm)
            hist_norm = hist_norm[hist_norm > 0]  # Remove zeros for log
            
            energy = np.sum(hist_norm ** 2)
            entropy = -np.sum(hist_norm * np.log2(hist_norm)) if len(hist_norm) > 0 else 0
            
            channel_features = [
                mean_val / 255.0,  # Normalized
                std_val / 255.0,   # Normalized
                skew,
                kurt,
                p25 / 255.0,
                p50 / 255.0,
                p75 / 255.0,
                min_val / 255.0,
                max_val / 255.0,
                range_val / 255.0,
                iqr / 255.0,
                energy,
                entropy / 8.0,  # Normalized (max entropy for 256 bins is 8)
                0, 0, 0  # Padding to make 16 features per channel
            ]
            
            features.extend(channel_features)
        
        # Ensure we have exactly the expected number of features
        features = features[:self.feature_dim]
        while len(features) < self.feature_dim:
            features.append(0.0)
        
        return np.array(features, dtype=np.float32)
    
    def prepare_training_data(self, open_door_dir="Open Door", closed_door_images=None):
        """
        Prepare training data from open door images and closed door images
        """
        print("Preparing training data...")
        
        X = []
        y = []
        
        # Process open door images
        open_door_path = Path(open_door_dir)
        if open_door_path.exists():
            open_images = list(open_door_path.glob("*.jpg")) + list(open_door_path.glob("*.png"))
            print(f"Processing {len(open_images)} open door images...")
            
            for img_path in open_images:
                img = cv2.imread(str(img_path))
                if img is not None:
                    features = self.extract_comprehensive_features(img)
                    X.append(features)
                    y.append(1)  # 1 = open
        
        # Process closed door images (from existing training data)
        if closed_door_images is None:
            closed_door_images = self.extract_closed_door_images()
        
        print(f"Processing {len(closed_door_images)} closed door images...")
        for img_path in closed_door_images:
            img = cv2.imread(str(img_path))
            if img is not None:
                features = self.extract_comprehensive_features(img)
                X.append(features)
                y.append(0)  # 0 = closed
        
        X = np.array(X)
        y = np.array(y)
        
        print(f"Dataset prepared: {len(X)} samples, {np.sum(y)} open, {len(y) - np.sum(y)} closed")
        
        return X, y

    def extract_closed_door_images(self):
        """
        Extract closed door images from existing training data
        """
        closed_images = []
        train_images_path = Path("train/images")
        train_labels_path = Path("train/labels")

        if not train_images_path.exists():
            print("Training images directory not found")
            return closed_images

        image_files = list(train_images_path.glob("*.jpg")) + list(train_images_path.glob("*.png"))

        for img_file in image_files:
            label_file = train_labels_path / f"{img_file.stem}.txt"

            if label_file.exists():
                with open(label_file, 'r') as f:
                    labels = f.readlines()

                # Check if image contains doors
                has_door = any(int(label.split()[0]) == 0 for label in labels if label.strip())

                if has_door:
                    img = cv2.imread(str(img_file))
                    if img is not None and self._looks_like_closed_door(img):
                        closed_images.append(str(img_file))

        return closed_images

    def _looks_like_closed_door(self, image):
        """
        Heuristic to determine if image likely contains a closed door
        """
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        edges = cv2.Canny(gray, 50, 150)

        # Calculate edge density
        edge_density = np.sum(edges > 0) / edges.size

        # Calculate color uniformity
        color_std = np.std(gray)

        # Heuristics for closed door (more uniform, moderate edges)
        return 0.05 < edge_density < 0.15 and 20 < color_std < 80

    def train_models(self, X, y):
        """
        Train multiple models and select the best one
        """
        print("Training models...")

        # Split data
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)

        # Train Random Forest
        print("Training Random Forest...")
        self.rf_model = RandomForestClassifier(
            n_estimators=100,
            max_depth=10,
            random_state=42,
            n_jobs=-1
        )
        self.rf_model.fit(X_train, y_train)
        rf_pred = self.rf_model.predict(X_test)
        rf_accuracy = accuracy_score(y_test, rf_pred)

        # Train SVM
        print("Training SVM...")
        self.svm_model = SVC(
            kernel='rbf',
            C=1.0,
            gamma='scale',
            probability=True,
            random_state=42
        )
        self.svm_model.fit(X_train, y_train)
        svm_pred = self.svm_model.predict(X_test)
        svm_accuracy = accuracy_score(y_test, svm_pred)

        # Select best model
        if rf_accuracy >= svm_accuracy:
            self.best_model = self.rf_model
            self.model_type = "RandomForest"
            best_accuracy = rf_accuracy
            best_pred = rf_pred
        else:
            self.best_model = self.svm_model
            self.model_type = "SVM"
            best_accuracy = svm_accuracy
            best_pred = svm_pred

        print(f"\nModel Performance:")
        print(f"Random Forest Accuracy: {rf_accuracy:.4f}")
        print(f"SVM Accuracy: {svm_accuracy:.4f}")
        print(f"Best Model: {self.model_type} (Accuracy: {best_accuracy:.4f})")

        # Detailed classification report
        print(f"\nClassification Report for {self.model_type}:")
        print(classification_report(y_test, best_pred, target_names=['Closed', 'Open']))

        # Save models
        self.save_models()

        return {
            'rf_accuracy': rf_accuracy,
            'svm_accuracy': svm_accuracy,
            'best_model': self.model_type,
            'best_accuracy': best_accuracy,
            'test_accuracy': best_accuracy
        }

    def save_models(self):
        """
        Save trained models
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Save models
        if self.rf_model:
            joblib.dump(self.rf_model, self.model_dir / f"rf_model_{timestamp}.pkl")

        if self.svm_model:
            joblib.dump(self.svm_model, self.model_dir / f"svm_model_{timestamp}.pkl")

        # Save best model separately
        if self.best_model:
            joblib.dump(self.best_model, self.model_dir / "best_door_classifier.pkl")

        # Save metadata
        metadata = {
            'timestamp': timestamp,
            'model_type': self.model_type,
            'feature_dim': self.feature_dim,
            'img_size': self.img_size
        }

        with open(self.model_dir / "model_metadata.json", 'w') as f:
            json.dump(metadata, f, indent=2)

        print(f"Models saved to {self.model_dir}")

    def load_model(self, model_path=None):
        """
        Load trained model
        """
        if model_path is None:
            model_path = self.model_dir / "best_door_classifier.pkl"

        if Path(model_path).exists():
            self.best_model = joblib.load(model_path)

            # Load metadata
            metadata_path = self.model_dir / "model_metadata.json"
            if metadata_path.exists():
                with open(metadata_path, 'r') as f:
                    metadata = json.load(f)
                self.model_type = metadata.get('model_type', 'Unknown')

            print(f"Model loaded: {self.model_type}")
            return True
        else:
            print(f"Model not found at {model_path}")
            return False

    def predict_door_state(self, image, yolo_detections=None):
        """
        Predict door state for a single image with enhanced rules:
        1. Use trained model prediction
        2. Check similarity with open door reference images
        3. Check for two knob detection rule
        """
        if self.best_model is None:
            if not self.load_model():
                raise ValueError("No trained model available")

        # Rule 1: Check for two knobs = open door
        if yolo_detections is not None:
            knob_count = self._count_knobs_in_detections(yolo_detections)
            if knob_count >= 2:
                print(f"Two knobs detected ({knob_count}) - Door is OPEN")
                return "open", 0.95

        # Rule 2: Check similarity with open door reference images
        similarity_score = self._check_open_door_similarity(image)
        if similarity_score > 0.75:  # High similarity threshold
            print(f"High similarity to open door images ({similarity_score:.3f}) - Door is OPEN")
            return "open", similarity_score

        # Rule 3: Use trained model prediction
        features = self.extract_comprehensive_features(image)
        features = features.reshape(1, -1)

        prediction = self.best_model.predict(features)[0]

        # Get confidence if available
        if hasattr(self.best_model, 'predict_proba'):
            probabilities = self.best_model.predict_proba(features)[0]
            model_confidence = max(probabilities)
        else:
            model_confidence = 0.90  # Default confidence for models without probability

        door_state = "open" if prediction == 1 else "closed"

        # Combine model prediction with similarity score for final confidence
        if door_state == "open" and similarity_score > 0.5:
            # Boost confidence if model says open AND similarity is decent
            final_confidence = min(model_confidence + (similarity_score * 0.1), 0.98)
        else:
            final_confidence = model_confidence

        return door_state, final_confidence

    def _count_knobs_in_detections(self, detections):
        """
        Count number of knobs in YOLO detections
        """
        knob_count = 0
        for detection in detections:
            if hasattr(detection, 'cls'):
                class_id = int(detection.cls)
                # Assuming knob class IDs (you may need to adjust based on your model)
                if class_id in [2, 3]:  # knob=2, lever=3 based on your data.yaml
                    knob_count += 1
            elif isinstance(detection, dict) and 'class' in detection:
                if detection['class'].lower() in ['knob', 'lever']:
                    knob_count += 1

        return knob_count

    def _check_open_door_similarity(self, image):
        """
        Check similarity with open door reference images using multiple methods
        """
        try:
            # Load open door reference images
            open_door_path = Path("Open Door")
            if not open_door_path.exists():
                return 0.0

            # Resize input image for comparison
            image_resized = cv2.resize(image, (224, 224))
            image_gray = cv2.cvtColor(image_resized, cv2.COLOR_BGR2GRAY)
            image_hsv = cv2.cvtColor(image_resized, cv2.COLOR_BGR2HSV)

            # Calculate histograms for input image
            hist_gray = cv2.calcHist([image_gray], [0], None, [64], [0, 256])
            hist_hue = cv2.calcHist([image_hsv], [0], None, [32], [0, 180])
            hist_sat = cv2.calcHist([image_hsv], [1], None, [32], [0, 256])

            cv2.normalize(hist_gray, hist_gray)
            cv2.normalize(hist_hue, hist_hue)
            cv2.normalize(hist_sat, hist_sat)

            similarities = []

            # Compare with reference images
            image_files = list(open_door_path.glob("*.jpg")) + list(open_door_path.glob("*.png"))
            sample_size = min(30, len(image_files))  # Use up to 30 reference images

            for i, img_file in enumerate(image_files[:sample_size]):
                ref_img = cv2.imread(str(img_file))
                if ref_img is None:
                    continue

                # Resize and convert reference image
                ref_resized = cv2.resize(ref_img, (224, 224))
                ref_gray = cv2.cvtColor(ref_resized, cv2.COLOR_BGR2GRAY)
                ref_hsv = cv2.cvtColor(ref_resized, cv2.COLOR_BGR2HSV)

                # Calculate reference histograms
                ref_hist_gray = cv2.calcHist([ref_gray], [0], None, [64], [0, 256])
                ref_hist_hue = cv2.calcHist([ref_hsv], [0], None, [32], [0, 180])
                ref_hist_sat = cv2.calcHist([ref_hsv], [1], None, [32], [0, 256])

                cv2.normalize(ref_hist_gray, ref_hist_gray)
                cv2.normalize(ref_hist_hue, ref_hist_hue)
                cv2.normalize(ref_hist_sat, ref_hist_sat)

                # Calculate correlations
                corr_gray = cv2.compareHist(hist_gray, ref_hist_gray, cv2.HISTCMP_CORREL)
                corr_hue = cv2.compareHist(hist_hue, ref_hist_hue, cv2.HISTCMP_CORREL)
                corr_sat = cv2.compareHist(hist_sat, ref_hist_sat, cv2.HISTCMP_CORREL)

                # Weighted combination
                combined_similarity = (corr_gray * 0.5 + corr_hue * 0.3 + corr_sat * 0.2)
                similarities.append(max(0, combined_similarity))  # Ensure non-negative

            if similarities:
                # Use the maximum similarity (best match)
                max_similarity = max(similarities)
                # Also consider average of top 3 matches for robustness
                top_similarities = sorted(similarities, reverse=True)[:3]
                avg_top_similarity = sum(top_similarities) / len(top_similarities)

                # Final similarity score (weighted combination)
                final_similarity = (max_similarity * 0.7 + avg_top_similarity * 0.3)
                return final_similarity

            return 0.0

        except Exception as e:
            print(f"Error in similarity check: {e}")
            return 0.0


def main():
    """
    Main training pipeline
    """
    print("=== Enhanced Door State Classification Training ===")

    # Initialize classifier
    classifier = EnhancedDoorClassifier()

    # Prepare training data
    X, y = classifier.prepare_training_data()

    if len(X) == 0:
        print("No training data found!")
        return

    # Train models
    results = classifier.train_models(X, y)

    print(f"\n=== Training Complete ===")
    print(f"Best Model: {results['best_model']}")
    print(f"Test Accuracy: {results['test_accuracy']:.4f}")

    # Test prediction on sample image
    open_door_path = Path("Open Door")
    sample_images = list(open_door_path.glob("*.jpg"))
    if sample_images:
        sample_image = cv2.imread(str(sample_images[0]))
        if sample_image is not None:
            prediction, confidence = classifier.predict_door_state(sample_image)
            print(f"\nSample prediction: {prediction} (confidence: {confidence:.4f})")

    return classifier, results


if __name__ == "__main__":
    classifier, results = main()
