"""
Analyze existing door photos to see what types you need more of
"""

import cv2
import os
import numpy as np

def analyze_photo_diversity(folder_path="Open Door"):
    """Analyze the diversity of photos in the folder"""
    if not os.path.exists(folder_path):
        print(f"Folder '{folder_path}' not found!")
        return
    
    image_files = [f for f in os.listdir(folder_path) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
    
    if not image_files:
        print(f"No images found in '{folder_path}'")
        return
    
    print(f"\n📊 ANALYZING {len(image_files)} PHOTOS IN '{folder_path}'")
    print("="*60)
    
    # Analyze each photo
    brightness_levels = []
    edge_densities = []
    sizes = []
    
    for i, img_file in enumerate(image_files[:20]):  # Analyze first 20 photos
        img_path = os.path.join(folder_path, img_file)
        img = cv2.imread(img_path)
        
        if img is None:
            continue
        
        # Analyze brightness
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        brightness = np.mean(gray)
        brightness_levels.append(brightness)
        
        # Analyze edge density (complexity)
        edges = cv2.Canny(gray, 50, 150)
        edge_density = np.sum(edges > 0) / (edges.shape[0] * edges.shape[1])
        edge_densities.append(edge_density)
        
        # Image size
        sizes.append(img.shape[:2])
        
        print(f"Photo {i+1:2d}: {img_file[:30]:<30} | Brightness: {brightness:6.1f} | Complexity: {edge_density:.3f}")
    
    # Analysis summary
    print("\n📈 DIVERSITY ANALYSIS:")
    print("-" * 40)
    
    if brightness_levels:
        avg_brightness = np.mean(brightness_levels)
        brightness_std = np.std(brightness_levels)
        print(f"Brightness - Avg: {avg_brightness:.1f}, Variation: {brightness_std:.1f}")
        
        if brightness_std < 20:
            print("⚠️  LOW brightness diversity - try different lighting conditions")
        else:
            print("✅ Good brightness diversity")
    
    if edge_densities:
        avg_complexity = np.mean(edge_densities)
        complexity_std = np.std(edge_densities)
        print(f"Complexity - Avg: {avg_complexity:.3f}, Variation: {complexity_std:.3f}")
        
        if complexity_std < 0.01:
            print("⚠️  LOW complexity diversity - try different angles/distances")
        else:
            print("✅ Good complexity diversity")
    
    # Recommendations
    print("\n💡 RECOMMENDATIONS:")
    print("-" * 40)
    
    if len(image_files) < 50:
        print(f"📸 Take {50 - len(image_files)} more photos for better accuracy")
    
    if brightness_levels and np.std(brightness_levels) < 20:
        print("🌞 Take photos in different lighting conditions:")
        print("   • Bright sunlight")
        print("   • Indoor lighting")
        print("   • Dim/evening lighting")
    
    if edge_densities and np.std(edge_densities) < 0.01:
        print("📐 Take photos from different perspectives:")
        print("   • Different angles (left, center, right)")
        print("   • Different distances (close, medium, far)")
        print("   • Different door opening amounts")
    
    print(f"\n🎯 GOAL: Aim for 50-100 diverse photos for best results")

def preview_photos(folder_path="Open Door"):
    """Preview photos in the folder"""
    if not os.path.exists(folder_path):
        print(f"Folder '{folder_path}' not found!")
        return
    
    image_files = [f for f in os.listdir(folder_path) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
    
    if not image_files:
        print(f"No images found in '{folder_path}'")
        return
    
    print(f"\n👀 PREVIEWING PHOTOS (Press any key for next, 'q' to quit)")
    print("="*60)
    
    for i, img_file in enumerate(image_files):
        img_path = os.path.join(folder_path, img_file)
        img = cv2.imread(img_path)
        
        if img is None:
            continue
        
        # Resize for display
        height, width = img.shape[:2]
        if width > 800:
            scale = 800 / width
            new_width = int(width * scale)
            new_height = int(height * scale)
            img = cv2.resize(img, (new_width, new_height))
        
        # Add info overlay
        info_text = f"Photo {i+1}/{len(image_files)}: {img_file}"
        cv2.putText(img, info_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        cv2.imshow("Photo Preview", img)
        
        key = cv2.waitKey(0) & 0xFF
        if key == ord('q'):
            break
    
    cv2.destroyAllWindows()

def main():
    print("🔍 Door Photo Analysis Tool")
    print("This tool analyzes your existing photos and suggests improvements")
    
    folder_name = input("\nEnter folder name to analyze (default: 'Open Door'): ").strip()
    if not folder_name:
        folder_name = "Open Door"
    
    print("\nChoose an option:")
    print("1. Analyze photo diversity")
    print("2. Preview photos")
    print("3. Both")
    
    choice = input("Enter choice (1-3): ").strip()
    
    if choice in ['1', '3']:
        analyze_photo_diversity(folder_name)
    
    if choice in ['2', '3']:
        preview_photos(folder_name)
    
    print(f"\n✨ Use 'python photo_collection_helper.py' to collect more photos!")

if __name__ == "__main__":
    main()
