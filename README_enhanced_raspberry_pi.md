# Enhanced Raspberry Pi Door Navigation Assistant

A voice-guided navigation assistant for Raspberry Pi that helps users find and navigate to doors while detecting and avoiding obstacles.

## Features

- **Door Detection**: Uses YOLOv8 to detect doors and door components
- **Obstacle Detection**: Identifies objects in the path and provides warnings
- **Depth Estimation**: Estimates distance to doors and obstacles
- **Directional Guidance**: Provides clear left/right/forward voice commands
- **Visual Feedback**: Shows navigation arrows and distance information
- **Smooth Guidance**: Uses direction history to prevent rapid changes in guidance
- **Path Analysis**: Checks if obstacles are blocking the path to the door
- **Priority Announcements**: Critical messages interrupt less important ones

## Requirements

- Raspberry Pi 4 (2GB RAM minimum, 4GB recommended)
- Raspberry Pi Camera Module or USB camera
- Speakers or headphones
- Python 3.7+
- OpenCV
- NumPy
- PyTorch (for YOLOv8)
- Ultralytics YOLOv8
- espeak (for text-to-speech on Raspberry Pi)

## Installation

1. Install system dependencies:

```bash
sudo apt update
sudo apt install -y python3-pip python3-opencv espeak
```

2. Install Python dependencies:

```bash
pip3 install ultralytics numpy
```

3. Clone this repository:

```bash
git clone https://github.com/yourusername/enhanced-door-assistant.git
cd enhanced-door-assistant
```

## Usage

Run the enhanced door navigation assistant:

```bash
python3 enhanced_raspberry_pi_door_assistant.py
```

### Controls

- Press 'n' to start navigation
- Press 's' to stop navigation
- Press 'q' to quit

## How It Works

### Door Detection

The system uses YOLOv8 to detect doors in the camera feed. When a door is detected, the system:

1. Estimates the distance to the door based on its apparent size
2. Determines the direction to the door (left, right, or forward)
3. Provides voice guidance to help the user navigate to the door

### Obstacle Detection

The system also detects obstacles in the path to the door:

1. Identifies objects that could be obstacles (people, furniture, etc.)
2. Estimates the distance to these obstacles
3. Checks if any obstacles are blocking the path to the door
4. Provides warnings when obstacles are detected in the path

### Depth Estimation

The system uses two methods for depth estimation:

1. **Monocular depth estimation**: Uses object size to estimate distance
2. **Edge-based depth map**: Creates a simple depth map based on image gradients

### Directional Guidance

The system provides clear directional guidance:

1. "Door is to your left, turn left"
2. "Door is to your right, turn right"
3. "Door is straight ahead, X meters away"
4. "Caution! [Obstacle] in your path, X meters ahead. Stop."

## Customization

You can customize the following parameters in the code:

- `frame_width` and `frame_height`: Camera resolution
- `confidence`: Detection confidence threshold
- `door_class_names`: List of class names that represent doors
- `obstacle_class_names`: List of class names that represent obstacles
- `guidance_interval`: Time between voice guidance announcements
- `close_door_threshold`: Distance at which to announce arrival at the door
- `obstacle_warning_threshold`: Distance at which to warn about obstacles
- `process_every_n_frames`: Process every n-th frame for better performance

## Improvements Over Original Version

1. **Obstacle Detection**: Now detects and warns about obstacles in the path
2. **Improved Directional Guidance**: Clearer left/right/forward commands
3. **Depth Visualization**: Shows a depth map overlay
4. **Smoother Guidance**: Uses direction history to prevent rapid changes
5. **Path Analysis**: Checks if obstacles are blocking the path to the door
6. **Priority Announcements**: Critical messages interrupt less important ones
7. **Visual Feedback**: Shows navigation arrows with text labels
8. **Enhanced UI**: Better status display and visualization

## Limitations and Future Work

- The monocular depth estimation is approximate and works best with known object sizes
- For better depth estimation, consider using:
  - Stereo cameras
  - Depth cameras (e.g., Intel RealSense)
  - Dedicated depth estimation neural networks
- The obstacle detection could be improved with:
  - Instance segmentation for better object boundaries
  - Tracking to maintain obstacle identity over time
  - Path planning algorithms to navigate around obstacles

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Ultralytics for the YOLOv8 framework
- OpenCV for computer vision capabilities
- The original Raspberry Pi Door Assistant code
