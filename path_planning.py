import numpy as np
import cv2
from scipy.ndimage import distance_transform_edt

class PathPlanner:
    """
    Class for planning a safe path to the door while avoiding obstacles.
    Uses a simplified potential field approach for path planning.
    """
    
    def __init__(self, frame_width, frame_height, safety_margin=30):
        """
        Initialize the path planner.
        
        Args:
            frame_width (int): Width of the camera frame.
            frame_height (int): Height of the camera frame.
            safety_margin (int): Safety margin around obstacles in pixels.
        """
        self.frame_width = frame_width
        self.frame_height = frame_height
        self.safety_margin = safety_margin
        self.path = []
        
    def create_obstacle_map(self, obstacles):
        """
        Create a binary map of obstacles.
        
        Args:
            obstacles (list): List of obstacle bounding boxes (x1, y1, x2, y2).
            
        Returns:
            numpy.ndarray: Binary map with obstacles marked as 1.
        """
        obstacle_map = np.zeros((self.frame_height, self.frame_width), dtype=np.uint8)
        
        # Mark obstacles on the map
        for bbox in obstacles:
            x1, y1, x2, y2 = bbox
            # Add safety margin around obstacles
            x1 = max(0, x1 - self.safety_margin)
            y1 = max(0, y1 - self.safety_margin)
            x2 = min(self.frame_width, x2 + self.safety_margin)
            y2 = min(self.frame_height, y2 + self.safety_margin)
            
            obstacle_map[y1:y2, x1:x2] = 1
            
        return obstacle_map
    
    def find_path(self, start_point, goal_point, obstacle_map):
        """
        Find a path from start to goal avoiding obstacles.
        Uses a simplified potential field approach.
        
        Args:
            start_point (tuple): Starting point (x, y).
            goal_point (tuple): Goal point (x, y).
            obstacle_map (numpy.ndarray): Binary map with obstacles marked as 1.
            
        Returns:
            list: List of points forming the path.
        """
        # Create distance transform from obstacles
        # This gives each pixel its distance to the nearest obstacle
        dist_transform = distance_transform_edt(1 - obstacle_map)
        
        # Create attractive potential field (goal)
        y, x = np.indices((self.frame_height, self.frame_width))
        goal_x, goal_y = goal_point
        attractive = np.sqrt((x - goal_x)**2 + (y - goal_y)**2)
        
        # Combine potential fields
        # Higher values of dist_transform mean safer areas
        # Lower values of attractive mean closer to goal
        # We want to maximize safety while minimizing distance to goal
        potential = attractive - 5.0 * dist_transform
        
        # Find path using gradient descent
        path = []
        current = np.array(start_point)
        path.append(current.copy())
        
        max_iterations = 1000
        step_size = 5
        goal_threshold = 20
        
        for _ in range(max_iterations):
            # Check if we're close enough to the goal
            if np.linalg.norm(current - np.array(goal_point)) < goal_threshold:
                break
                
            # Get current position (rounded to integers)
            cx, cy = np.round(current).astype(int)
            cx = np.clip(cx, 0, self.frame_width - 1)
            cy = np.clip(cy, 0, self.frame_height - 1)
            
            # Sample potential field in neighborhood
            window_size = 5
            x_min = max(0, cx - window_size)
            x_max = min(self.frame_width, cx + window_size + 1)
            y_min = max(0, cy - window_size)
            y_max = min(self.frame_height, cy + window_size + 1)
            
            window = potential[y_min:y_max, x_min:x_max]
            min_idx = np.unravel_index(np.argmin(window), window.shape)
            
            # Move towards minimum potential
            next_y, next_x = min_idx
            next_point = np.array([x_min + next_x, y_min + next_y])
            
            # Ensure we're not stuck at the same point
            if np.array_equal(next_point, np.round(current)):
                # If stuck, take a random step
                angle = np.random.uniform(0, 2 * np.pi)
                next_point = current + step_size * np.array([np.cos(angle), np.sin(angle)])
                next_point = np.clip(next_point, [0, 0], [self.frame_width - 1, self.frame_height - 1])
            
            # Update current position
            direction = next_point - current
            direction_norm = np.linalg.norm(direction)
            if direction_norm > 0:
                direction = direction / direction_norm
            
            current = current + step_size * direction
            current = np.clip(current, [0, 0], [self.frame_width - 1, self.frame_height - 1])
            
            # Add to path
            path.append(current.copy())
            
            # Check if we're stuck in an obstacle
            cx, cy = np.round(current).astype(int)
            if obstacle_map[cy, cx] == 1:
                # If in obstacle, backtrack and try again
                if len(path) > 1:
                    current = path[-2]
                    path.pop()
        
        self.path = path
        return path
    
    def draw_path(self, frame, path=None):
        """
        Draw the path on the frame.
        
        Args:
            frame (numpy.ndarray): The image frame.
            path (list, optional): List of points forming the path. If None, use the stored path.
            
        Returns:
            numpy.ndarray: Frame with path drawn.
        """
        if path is None:
            path = self.path
            
        if not path:
            return frame
            
        # Draw path as a line
        points = np.array([point for point in path], dtype=np.int32)
        cv2.polylines(frame, [points], False, (0, 255, 255), 2)
        
        # Draw start and end points
        if len(path) > 0:
            cv2.circle(frame, tuple(points[0]), 5, (0, 255, 0), -1)
            cv2.circle(frame, tuple(points[-1]), 5, (0, 0, 255), -1)
            
        return frame
    
    def get_navigation_command(self, current_position, path=None, lookahead=3):
        """
        Get navigation command based on the path.
        
        Args:
            current_position (tuple): Current position (x, y).
            path (list, optional): List of points forming the path. If None, use the stored path.
            lookahead (int): Number of points to look ahead for direction.
            
        Returns:
            str: Navigation command ('left', 'right', 'forward', or 'stop').
        """
        if path is None:
            path = self.path
            
        if not path or len(path) < lookahead + 1:
            return "stop"
            
        # Get current position and target position
        current = np.array(current_position)
        target = np.array(path[min(lookahead, len(path) - 1)])
        
        # Calculate direction vector
        direction = target - current
        
        # Calculate angle
        angle = np.arctan2(direction[1], direction[0]) * 180 / np.pi
        
        # Convert angle to navigation command
        # Assuming camera is facing forward (positive x-axis)
        if angle < -30:
            return "left"
        elif angle > 30:
            return "right"
        else:
            return "forward"
