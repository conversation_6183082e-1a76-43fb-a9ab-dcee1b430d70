\documentclass[11pt,a4paper]{article}
\usepackage[utf8]{inputenc}
\usepackage[T1]{fontenc}
\usepackage{cite}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{algpseudocode}
\usepackage{graphicx}
\usepackage{textcomp}
\usepackage{xcolor}
\usepackage{hyperref}
\usepackage{booktabs}
\usepackage{multirow}
\usepackage{algorithm}
\usepackage{algpseudocode}
\usepackage{subfig}
\usepackage{listings}
\usepackage{tikz}
\usepackage{float}
\usepackage[margin=1in]{geometry}
\usepackage{authblk}

% Define colors for hyperlinks
\hypersetup{
    colorlinks=true,
    linkcolor=blue,
    filecolor=magenta,
    urlcolor=cyan,
    pdftitle={Vision Guard: A Computer Vision System for Assisting Visually Impaired People},
    pdfauthor={Author Names},
    pdfsubject={Computer Vision, Assistive Technology},
    pdfkeywords={computer vision, assistive technology, object detection, YOLOv8, depth estimation}
}

% Define code listing style
\lstset{
    basicstyle=\ttfamily\small,
    keywordstyle=\color{blue},
    commentstyle=\color{green!60!black},
    stringstyle=\color{red},
    numbers=left,
    numberstyle=\tiny,
    numbersep=5pt,
    frame=single,
    breaklines=true,
    breakatwhitespace=true,
    showstringspaces=false
}

\def\BibTeX{{\rm B\kern-.05em{\sc i\kern-.025em b}\kern-.08em
    T\kern-.1667em\lower.7ex\hbox{E}\kern-.125emX}}

% Include cover page content
\begin{document}

% Cover page


\title{\LARGE \bf Vision Guard: Computer Vision-Based Door Detection and Navigation Assistance for the Visually Impaired}

\author[1]{Polok Poddar}
\author[2]{Mourika Nigar Mouny}
\author[3]{Labib Hasan Khan}
\author[4]{Md Sabbir Hossain}
\author[5]{Annajiat Alim Rasel}

\affil[1-5]{BRAC University, Dhaka, Bangladesh}

\date{May 15, 2025}

\begin{document}
\maketitle

\begin{center}
    \textbf{Emails:}\\
    \href{mailto:<EMAIL>}{<EMAIL>}, 
    \href{mailto:<EMAIL>}{<EMAIL>}, 
    \href{mailto:<EMAIL>}{<EMAIL>}, 
    \href{mailto:<EMAIL>}{<EMAIL>}, 
    \href{mailto:<EMAIL>}{<EMAIL>}
\end{center}
\begin{abstract}
Navigation in unfamiliar spaces is highly challenging for the visually impaired, especially when door detection and localization become life-critical during earthquake or fire incidents in addition to daily life. Vision Guard is a novel computer vision system introduced in this paper that offers real-time door detection and navigation using just one camera with depth estimation. The system integrates a self-trained YOLOv8 object detection model with monocular depth estimation methods to detect doors and obstacles, measure their distances, and give directions through voice prompts. Our solution achieves 94\% accuracy in detecting doors and its individual components (handle, knob, hinge) and averages less than 10 cm for distance measurements. We examine how the system functions under various indoor conditions and lighting configurations and present it as an effective assistive technology. Vision Guard offers a convenient, affordable and easy-to-use solution to enhance the mobility and autonomy of visually impaired users without requiring special equipment or environmental modification.
\end{abstract}

\textbf{Keywords:} computer vision, assistive technology, object detection, YOLOv8, depth estimation, visually impaired, door detection, navigation assistance


\section{Introduction}
\subsection{Background and Motivation}
As reported by the World Health Organization, 285 million people  are visually impaired, and 39 million are blind \cite{who_vision}. Indoor movement is still a significant challenge for this community, particularly in the detection and location of doorways, which represent a fundamental transition between spatial regions. The ability to recognize and pass through doors independently is an essential aspect of independent mobility and is a very important factor in facilitating the daily activity of visually impaired person.

\subsection{Problem Statement}
There are various drawbacks to the assistive technology used today for door detection and navigation:

\begin{enumerate}
    \item Significant reliance on a number of sensors, including RGB-D cameras, LiDAR, and ultrasonic modules, which raises the cost and complexity of the system's architecture.
    \item Environmental modification requirements, including the placement of RFID tags or Bluetooth beacons, may not be possible in all settings.
    \item Limited real-time processing capability, especially for devices with limited resources. 
    \item Reduced detection accuracy in various interior situations and lighting conditions.
    \item Accessibility and usability are hampered by user interfaces that are frequently complicated and demand extensive training.
\end{enumerate}

Due to these constraints, there is a substantial disconnect between lab-based models and workable, reasonable solutions that the blind and visually handicapped can use on a daily basis.

\subsection{Proposed Solution}
This paper presents Vision Guard, a single-camera computer vision system that overcomes these limitations with door detection and navigation aid in real-time. Vision-Guard combines the latest object detection methods with new depth estimation algorithms to detect obstacles and doors, measure their distance, and provide direction assistance in the form of voice alerts.


\subsection{Key Contributions}
The key contributions of this research include:
\begin{itemize}
    \item A custom-trained YOLOv8 model specifically optimized for door detection, including door components such as handles, knobs, and hinges, achieving 94\% detection accuracy. 
    \item A novel approach to single-camera depth estimation for accurate distance measurement with average error less than 10 cm. 
    \item A comprehensive door dataset with 5,000 images capturing diverse door types, lighting conditions, and perspectives. 
    \item An integrated system architecture that combines detection, depth estimation, and voice guidance in a real-time application running at 10+ FPS on standard hardware. 
\end{itemize}

\subsection{Structure of the paper}
The following sections of this paper are structured as follows: 
Section \ref{sec:related_work} Related work review in assistive technologies for those who are visually impaired. Section \ref{sec:methodology} details the methodology that includes the creation of the dataset, the architecture of the model, and the system implementation. Section \ref{sec:results} presents experimental results and performance evaluation. Section \ref{sec:discussion} discusses the implications and limitations of our approach, and Section \ref{sec:conclusion} concludes with future research directions.


\section{Related Work}
\label{sec:related_work}
\subsection{Assistive Technologies for the Visually Impaired}

In recent decades, traditional aids for the visually impaired, including white canes and guide dogs, have been supplemented with a number of electronic travel aids (ETA) \cite{whitecane, eta_survey}. These are technologies that vary from obstacle detectors to complex systems that give spatial awareness and navigation guidance \cite{manduchi}.

Computer vision-based assistive systems have gained prominence due to their ability to extract rich semantic information from the environment \cite{deep_assist}. These systems can detect certain objects of interest, such as doors, stairs, and pedestrian crossings, which can provide contextual information other than obstacle detection \cite{tian_door}.


\subsection{Door Detection Systems}
Detecting doors is an essential aspect of indoor navigation systems for visually impaired individual. Early approaches relied on simple features such as edge detection and color segmentation \cite{tian_door}. Recent methods leverage deep learning techniques to achieve higher accuracy and robustness.

Chen and Huang \cite{chen2018door} proposed a door detection system that combines RGB and depth information using a Kinect sensor. Liu et al. \cite{liu2019deep} developed a CNN-based approach to door detection in various indoor environments. Lin et al. \cite{lin2022door} used YOLOv5 for door detection in real time, achieving promising results but still requiring specialized hardware for depth perception.

\subsection{Depth Estimation Techniques}
Precise depth estimation is important for navigation assistance. Conventional methods use special hardware, such as stereo cameras \cite{stereo_vision}, RGB-D sensors \cite{kinect}, or LiDAR. Although effective, these solutions add to the cost, size, and power consumption of the system.

Recent progress in monocular depth estimation \cite{mono_depth} has made depth perception possible from a single camera. Eigen et al. \cite{eigen} suggested a multiscale deep network for depth prediction from a single image. Based on this work, Godard et al. \cite{godard} presented an unsupervised method based on the consistency of left-right. 


\subsection{Voice-Based Guidance Systems}
Voice interfaces are very suitable for visually impaired users. Systems such as NavCog \cite{navcog} give turn-by-turn navigation instructions via audio feedback. Ahmetovic et al. \cite{ahmetovic} proved that effective voice commands can boost navigation efficiency for the visually impaired.

Although these previous works have made important contributions, they usually require the use of several sensors, specialized hardware, or environmental changes. Vision Guard overcomes these limitations by combining door detection, depth estimation, and voice guidance into a single-camera system that is designed for real-world applications.

\section{Methodology}
\label{sec:methodology}
\subsection{System Overview}
Vision Guard has four main components, namely vision, vision statement, vision plan, and vision implementation. (1) a door detection module using YOLOv8, (2) a depth estimation module for distance calculation, (3) an obstacle detection module, and (4) a voice guidance system. The architecture of the system is shown in Figure~\ref{fig:system_architecture}.

\begin{figure}[H]
    \centering
    \includegraphics[width=0.30\linewidth]{).png}
    \caption{System Overview}
    \label{fig:system_architecture}
\end{figure}

The system processes video frames from a single camera, detects doors and obstacles, estimates the distances to them, and gives voice commands to instruct the user. The processing pipeline is tuned for real-time processing on portable devices.

\subsection{System Architecture Details}

The Vision Guard system consists of several interconnected modules, each responsible for a specific aspect of the door detection and navigation process:

\begin{enumerate}
    \item \textbf{Input Module:} Captures video frames from a standard RGB camera at 30 frames per second with a resolution of 1280×720 pixels.

    \item \textbf{Pre-processing Module:} Prepares images for the detection model by resizing frames to 640×640 pixels (YOLOv8 input size), normalizing pixel values to the range [0,1], applying color correction to adapt to varying lighting conditions and converting color space from BGR to RGB. 

    \item \textbf{Detection Module:} Implements the YOLOv8 model to detect doors (main target), door components (handles, knobs, hinges, levers) and potential obstacles (people, furniture, walls, etc.)

    \item \textbf{Depth Estimation Module:} Calculates distances to detected objects using monocular depth estimation network, geometric constraints based on known object dimensions, statistical filtering to remove outliers and confidence-weighted fusion of multiple estimates. 
    

    \item \textbf{Navigation Module:} Processes detection and depth information to Determine the optimal path to the nearest door. Identify potential obstacles in the user's path, calculate directional guidance (angle and distance) and prioritize navigation targets based on user preferences. 

    \item \textbf{Voice Guidance Module:} Converts navigation information into audio feedback that generates clear, concise voice commands, prioritizes urgent warnings (e.g., imminent collision), adjusts verbosity based on user preferences, and provides confirmation of successful detections. 
   
\end{enumerate}

The system operates in a continuous loop, processing frames at approximately 15-30 FPS depending on the hardware platform. A thread management system ensures that voice commands do not overlap and that  critical information is delivered to the user without delay.

\subsection{Dataset Creation}
In order to train our door detection model, we have developed a large dataset of 5,000 images of doors in different indoor settings. The dataset includes:
\begin{itemize}
    \item Different types of doors (wooden, glass, metal, automatic).
    \item Different lighting conditions (bright, dim, backlit).
    \item Multiple views (frontal, angled, obscured). 
    \item Different door components (handles, knobs, hinges, levers). 
    \item Different environments (offices, homes, public buildings). 
\end{itemize}

Images were manually labeled with bounding boxes for doors and their parts in the YOLO format. The dataset was partitioned into training (70\%), validation (15\%), and testing (15\%) sets with balanced representation of categories.

\subsection{Door Detection Model}
We used YOLOv8 as our base detection framework because of its good balance of speed and accuracy. The model was initialized with pretrained weights on COCO dataset and fine-tuned on our custom door dataset.

The network architecture includes a CSPDarknet53 backbone, PANet neck for feature aggregation, and detection head. We changed the last layer to recognize our classes: door, handle, knob, hinge and lever.

Some of the critical elements of our implementation are the Base model(YOLOv8n (nano) with 3.2 million parameters), Input resolution(640×640 pixels. Anchor-free detection with direct bounding box coordinates prediction) and Multi-class classification for 5 classes(door, knob, lever, hinge and handle)

The model was trained in the following configuration:
\begin{itemize}
    \item Epochs: 100. 
    \item Batch size: 16. 
    \item Optimizer: SGD with momentum (0.937) and weight decay (0.0005). 
    \item Learning rate: Cosine annealing schedule from 0.01 to 0.0001. 
    \item Data augmentation: Random scaling, rotation, translation, horizontal flipping, and mosaic augmentation.
\end{itemize}


\subsection{Depth Estimation}
For depth estimation we used a monocular depth estimation algorithm based on the work of Godard et al. \cite{godard}. The model was trained on NYU Depth V2 dataset \cite{silberman2012indoor} and fine-tuned using a subset of our door images with ground truth depth measurements.

In order to enhance distance accuracy for doors in particular, we implemented geometric constraints using standard door sizes. When a door is found, we use the apparent size of the door in the image (with standard door width, 80-90 cm) to improve the depth estimate.

The steps of the depth estimation process includes creating a dense depth map from the input image, extracting depth values in door bounding boxes, statistical filtering to eliminate outliers and Computing the final distance with the help of geometric constraints. 


\subsection{Mathematical Formulation of Depth Estimation}\cite{godard},\cite{silberman2012indoor}

Our depth estimation approach combines monocular depth prediction with geometric constraints specific to doors. The monocular depth estimation network $f_\theta$ predicts a depth map $D$ from a single RGB image $I$:

\begin{equation}
D = f_\theta(I)
\end{equation}

where $\theta$ represents the network parameters learned during training. The network architecture follows an encoder-decoder structure with skip connections, where the encoder $E$ extracts features at multiple scales, and the decoder $G$ reconstructs the depth map:

\begin{equation}
D = G(E(I))
\end{equation}

The network is trained to minimize a combination of reconstruction loss $\mathcal{L}_{rec}$ and smoothness loss $\mathcal{L}_{smooth}$:

\begin{equation}
\mathcal{L} = \mathcal{L}_{rec} + \lambda \mathcal{L}_{smooth}
\end{equation}

where $\lambda$ is a weighting factor. The reconstruction loss is defined as:

\begin{equation}
\mathcal{L}_{rec} = \frac{1}{N} \sum_{i=1}^{N} \left( \alpha \cdot \frac{|D_i - D_i^*|}{D_i^*} + (1-\alpha) \cdot \frac{|D_i - D_i^*|^2}{D_i^*} \right)
\end{equation}

where $D_i$ is the predicted depth in pixel $i$, $D_i^*$ is the depth of the ground truth, $N$ is the number of pixels, and $\alpha$ is a parameter that balances the scale-invariant and scale-dependent terms.

For door detection specifically, we refine the depth estimate using geometric constraints. Given a detected door with a bounding box width $w_{px}$ in pixels and assuming a standard door width $W_{real}$ (typically 0.85m), we can estimate the distance $Z$ using the pinhole camera model:

\begin{equation}
Z = \frac{f \cdot W_{real}}{w_{px}}
\end{equation}

where $f$ is the focal length of the camera in pixels. This geometric estimate $Z_{geo}$ is combined with the monocular depth prediction $Z_{mono}$ using a weighted average:

\begin{equation}
Z_{final} = \beta \cdot Z_{geo} + (1-\beta) \cdot Z_{mono}
\end{equation}

where $\beta$ is a confidence factor that depends on the door detection confidence score $c$:

\begin{equation}
\beta = \min(1, \max(0, \gamma \cdot c - \delta))
\end{equation}

with $\gamma$ and $\delta$ being hyperparameters that control the influence of the detection confidence on the weighting.

To handle potential outliers in the depth map, we apply statistical filtering to the depth values within the door bounding box. Let $\mathcal{D}_{door}$ be the set of depth values within the door region. We compute the median $\tilde{D}$ and the median absolute deviation (MAD):

\begin{equation}
\text{MAD} = \text{median}(|D_i - \tilde{D}|) \quad \forall D_i \in \mathcal{D}_{door}
\end{equation}

We then filter out values that deviate significantly from the median.

\begin{equation}
\mathcal{D}_{filtered} = \{D_i \in \mathcal{D}_{door} : |D_i - \tilde{D}| < k \cdot \text{MAD}\}
\end{equation}

where $k$ is a threshold parameter (typically set to 2.5). The final monocular depth estimate $Z_{mono}$ is computed as the mean of the filtered values:

\begin{equation}
Z_{mono} = \frac{1}{|\mathcal{D}_{filtered}|} \sum_{D_i \in \mathcal{D}_{filtered}} D_i
\end{equation}


\subsection{Depth Estimation and Obstacle Avoidance Algorithms}

\noindent
\begin{minipage}[t]{0.48\linewidth}
\begin{algorithm}[H]
\caption{Vision Guard Depth Estimation Algorithm}\cite{godard} \cite{mono_depth}
\label{alg:depth_estimation}
\begin{algorithmic}[1]
\Require RGB image $I$, door detection bounding box $B = (x_1, y_1, x_2, y_2)$, detection confidence $c$
\Ensure Estimated distance $Z_{\text{final}}$ to the door
\State $D \gets \text{MonocularDepthModel}(I)$ \Comment{Godard et al. [13]}
\State $\mathcal{D}_{\text{door}} \gets \{D(i,j) \mid (i,j) \in B\}$ \Comment{Extract door region depth}
\State $\tilde{D} \gets \text{median}(\mathcal{D}_{\text{door}})$
\State $\text{MAD} \gets \text{median}(|D_i - \tilde{D}|)$
\State $k \gets 2.5$ \Comment{Outlier threshold}
\State $\mathcal{D}_{\text{filtered}} \gets \{D_i \in \mathcal{D}_{\text{door}} : |D_i - \tilde{D}| < k \cdot \text{MAD}\}$
\State $Z_{\text{mono}} \gets \frac{1}{|\mathcal{D}_{\text{filtered}}|} \sum D_i$ \Comment{Liu et al. [11]}
\State $W_{\text{real}} \gets 0.85$ \Comment{Chen et al. [6]}
\State $w_{\text{px}} \gets x_2 - x_1$
\State $f \gets \text{GetCameraFocalLength}()$
\State $Z_{\text{geo}} \gets \frac{f \cdot W_{\text{real}}}{w_{\text{px}}}$ \Comment{Khoshelham et al. [10]}
\State $\gamma \gets 1.5$, $\delta \gets 0.3$
\State $\beta \gets \min(1, \max(0, \gamma \cdot c - \delta))$
\State $Z_{\text{final}} \gets \beta \cdot Z_{\text{geo}} + (1 - \beta) \cdot Z_{\text{mono}}$
\State \Return $Z_{\text{final}}$
\end{algorithmic}
\end{algorithm}
\end{minipage}
\hfill
\begin{minipage}[t]{0.48\linewidth}
\begin{algorithm}[H]
\caption{Vision Guard Obstacle Avoidance Algorithm}
\label{alg:obstacle_avoidance}
\begin{algorithmic}[1]
\Require Detected obstacles $O = \{o_1, o_2, ..., o_n\}$ with positions, dimensions, confidences
\Require Target door position $\vec{d}$, user position $\vec{u}$, orientation $\theta$
\Ensure Safe navigation path and warnings
\State $\vec{p} \gets \vec{d} - \vec{u}$ \Comment{Path vector}
\State $\text{obstacles\_in\_path} \gets \emptyset$
\For{each obstacle $o_i \in O$}
    \State $\vec{o}_i \gets \text{position}(o_i) - \vec{u}$
    \State $\text{proj} \gets \frac{\vec{o}_i \cdot \vec{p}}{|\vec{p}|^2} \vec{p}$
    \State $d_\perp \gets |\vec{o}_i - \text{proj}|$
    \State $t \gets \frac{\vec{o}_i \cdot \vec{p}}{|\vec{p}|^2}$
    \State $d_{\text{safe}} \gets \text{radius}(o_i) + 0.8$ \Comment{Yang et al. [19]}
    \If{$d_\perp < d_{\text{safe}}$ \textbf{and} $0 \leq t \leq 1$}
        \State Add $o_i$ to $\text{obstacles\_in\_path}$
    \EndIf
\EndFor
\If{$\text{obstacles\_in\_path} \neq \emptyset$}
    \State Sort $\text{obstacles\_in\_path}$ by distance
    \State $o_{\text{nearest}} \gets$ nearest obstacle
    \State $\text{distance} \gets |\text{position}(o_{\text{nearest}}) - \vec{u}|$
    \If{$\text{distance} < 1.0$}
        \State High-priority warning \Comment{Lee and Medioni [20]}
    \ElsIf{$\text{distance} < 3.0$}
        \State Medium-priority warning
    \Else
        \State Low-priority notification
    \EndIf
    \State Calculate alternative path
\Else
    \State Provide direct guidance toward door
\EndIf
\State \Return Navigation instructions and warnings
\end{algorithmic}
\end{algorithm}
\end{minipage}



\subsection{Obstacle Detection and Avoidance}
Apart from door detection, Vision Guard detects possible obstacles on the user’s way. We utilize the YOLOv8 model’s capability to recognize common objects (people, furniture, etc.) and utilize this in conjunction with depth information to determine the level of collision risk.

The system categorizes obstacles are Proximity(Distance to the obstacle), Position(Relative to the user’s path or location), Size(Physical dimensions of the obstacle) and Movement(When the obstacle is static or dynamic). 

This information is used to create appropriate warnings and navigation instructions with immediate safety concerns being given priority. 

\subsubsection{Mathematical Formulation of Obstacle Avoidance}

We model the user's path as a vector $\vec{p}$ pointing from the user's current position to the target door. For each detected obstacle with position vector $\vec{o}$ relative to the user, we calculate the projection of the obstacle onto the path vector:\cite{yang2020obstacle}

\begin{equation}
\text{proj}_{\vec{p}}(\vec{o}) = \frac{\vec{o} \cdot \vec{p}}{|\vec{p}|^2} \vec{p}
\end{equation}

The perpendicular distance from the obstacle to the path is then:\cite{chen2020obstaclemath}

\begin{equation}
d_{\perp} = |\vec{o} - \text{proj}_{\vec{p}}(\vec{o})|
\end{equation}

We define a safety threshold $d_{\text{safe}}$ based on the physical dimensions of the obstacle and a safety margin:

\begin{equation}
d_{\text{safe}} = r_{\text{obstacle}} + r_{\text{user}} + \text{margin}
\end{equation}

where $r_{\text{obstacle}}$ is the estimated radius of the obstacle, $r_{\text{user}}$ is the user's personal space radius (typically 0.5m), and margin is an additional safety buffer (typically 0.3m).

An obstacle is considered to be in the user's path if:
\begin{equation}
d_{\perp} < d_{\text{safe}} \quad \text{and} \quad 0 \leq \frac{\vec{o} \cdot \vec{p}}{|\vec{p}|^2} \leq 1
\end{equation}

For dynamic obstacles, we incorporate a velocity vector $\vec{v}_{\text{obstacle}}$ and predict the future position of the obstacle at time $t$:

\begin{equation}
\vec{o}(t) = \vec{o}(0) + \vec{v}_{\text{obstacle}} \cdot t
\end{equation}

\subsubsection{Obstacle Avoidance Algorithm} \cite{whitecane},\cite{manduchi},\cite{eta_survey}


The obstacle avoidance system continuously updates at a rate of 10Hz, ensuring that the user receives timely warnings about potential collisions while maintaining a smooth navigation experience without information overload.

\subsection{Voice Guidance System}
The voice guidance module converts detection and depth information into simple and straightforward audio instructions. We developed the voice commands based on the guidelines for non-visual interfaces, concentrating on Directional guidance (Door at 2 o’clock, 3 meters ahead), Obstacle warnings with urgency levels, Confirmation of successful detections and User-controlled verbosity levels. \cite{whitecane}

The system has text-to-speech technology with adjustable speaking rate and volume. Voice commands are ordered in terms of urgency, with obstacle warnings being given priority over door notifications.\cite{navcog},\cite{ahmetovic}

\subsubsection{Voice Command Generation}

The voice guidance system transforms spatial information into intuitive directional commands based on a clock face model, which has been proven to be useful for visually impaired users. The direction to a target is assigned to a clock position (1-12) depending on the angle with respect to the current orientation of the user:\cite{navcog},\cite{whitecane}

\begin{equation}
\text{clock\_position} = \text{round}\left(\frac{\theta \cdot 6}{\pi} + 12\right) \bmod 12
\end{equation}

where $\theta$ is the angle in radians between the user's forward direction and the vector to the target, with $\theta = 0$ corresponding to straight ahead. If the result is 0, it is converted to 12.

Distance information is communicated using appropriate units and precision based on the distance range:
\begin{itemize}
    \item Close range (< 2 m): Reported in centimeters (e.g., "80 centimeters")
    \item Medium range (2-10 m): Reported in meters with one decimal place (e.g., "3.5 meters")
    \item Long range (> 10 m): Reported in meters with no decimal places (e.g., "15 meters")
\end{itemize}

\subsubsection{Command Prioritization Algorithm}

\begin{algorithm}
\caption{Voice Command Prioritization Algorithm}\cite{navcog},\cite{whitecane}
\label{alg:voice_prioritization}
\begin{algorithmic}[1]
\Require Set of potential voice commands $C = \{c_1, c_2, ..., c_n\}$ with types and timestamps
\Require User verbosity preference $v \in \{1, 2, 3\}$ (1=minimal, 2=normal, 3=detailed)
\Ensure Prioritized sequence of voice commands

\State $\text{priority\_queue} \gets \emptyset$ \Comment{Initialize empty priority queue}

\For{each command $c_i \in C$}
    \State $p_i \gets 0$ \Comment{Initialize priority score}

    \If{$\text{type}(c_i) = \text{"collision\_warning"}$}
        \State $p_i \gets 100 - \text{distance\_to\_obstacle}(c_i)$ \Comment{Higher priority for closer obstacles}
    \ElsIf{$\text{type}(c_i) = \text{"door\_detection"}$}
        \State $p_i \gets 50 - \text{distance\_to\_door}(c_i) / 2$ \Comment{Medium priority}
    \ElsIf{$\text{type}(c_i) = \text{"navigation\_update"}$}
        \State $p_i \gets 30$ \Comment{Lower base priority}
    \ElsIf{$\text{type}(c_i) = \text{"system\_status"}$}
        \State $p_i \gets 10$ \Comment{Lowest priority}
    \EndIf

    \State $p_i \gets p_i + \text{confidence}(c_i) * 10$ \Comment{Adjust by detection confidence}

    \If{$\text{time\_since\_last\_similar}(c_i) < 2.0$ AND $\text{type}(c_i) \neq \text{"collision\_warning"}$}
        \State $p_i \gets p_i - 40$ \Comment{Reduce priority of repetitive messages}
    \EndIf

    \If{$v = 1$ AND $\text{type}(c_i) \in \{\text{"system\_status"}, \text{"detailed\_info"}\}$}
        \State $p_i \gets -1$ \Comment{Filter out low-priority messages in minimal verbosity mode}
    \EndIf

    \If{$p_i > 0$}
        \State Add $c_i$ to $\text{priority\_queue}$ with priority $p_i$
    \EndIf
\EndFor

\State Sort $\text{priority\_queue}$ by priority in descending order
\State $\text{selected\_commands} \gets \emptyset$
\State $\text{total\_duration} \gets 0$

\For{each command $c_i$ in sorted $\text{priority\_queue}$}
    \If{$\text{total\_duration} + \text{estimated\_duration}(c_i) \leq 5.0$} \Comment{Limit to 5 seconds of speech}
        \State Add $c_i$ to $\text{selected\_commands}$
        \State $\text{total\_duration} \gets \text{total\_duration} + \text{estimated\_duration}(c_i)$
    \EndIf
\EndFor

\State \Return $\text{selected\_commands}$
\end{algorithmic}
\end{algorithm}

The voice guidance system runs on a separate thread to make sure that the speech generation does not affect the real-time operation of the detection and navigation systems. A speech queue manager manages the timing and interruption of voice commands to allow the interruption of critical warnings by less important messages.

\subsection{System Implementation}
Vision Guard was developed in Python with OpenCV for image processing, PyTorch for the deep learning models, and pyttsx3 for text-to-speech. The system is implemented on the standard laptop hardware and is portable to embedded platforms.

\section{Results}
\label{sec:results}

\subsection{Overall Performance}

Our model achieved strong overall performance on the test dataset, as summarized in Table~\ref{tab:overall_performance}.

\begin{figure}[htbp]
    \centering
    \includegraphics[width=0.9\linewidth]{figures/results.png}
    \caption{Overall Performance Metrics}
    \label{fig:overall_performance_figure}
\end{figure}

\begin{table}[htbp]
    \centering
    \caption{Overall Performance Metrics}
    \label{tab:overall_performance}
    \begin{tabular}{lr}
        \toprule
        \textbf{Metric} & \textbf{Value} \\
        \midrule
        Precision & 93.3\% \\
        Recall & 82.2\% \\
        F1-score & 87.4\% \\
        mAP@0.5 & 86.7\% \\
        mAP@0.5-0.95 & 57.5\% \\
        Inference time (ms) & 8.2 \\
        \bottomrule
    \end{tabular}
\end{table}

\subsection{Detection Performance by Class \& Depth Estimation Accuracy} 

The model's performance varied across different classes, as shown in Table~\ref{tab:class_performance}. The \texttt{door} class achieved the highest precision and recall, while smaller components like \texttt{hinge} were more challenging to detect consistently. 
We evaluated the accuracy of our depth estimation approach by comparing predicted distances with ground truth measurements. Table~\ref{tab:depth_accuracy} presents the results for different distance ranges.

\begin{figure}[htbp]
    \centering
    \includegraphics[width=0.7\linewidth]{Screenshot 2025-05-14 030931.png}
    \caption{Detections and Depth Estimation}
    \label{fig:detection_depth}
\end{figure}

\begin{table}[htbp]
    \centering
    \caption{Performance Metrics by Class}
    \label{tab:class_performance}
    \begin{tabular}{lrrr}
        \toprule
        \textbf{Class} & \textbf{Precision} & \textbf{Recall} & \textbf{F1-score} \\
        \midrule
        Door   & 96.5\% & 91.2\% & 93.8\% \\
        Handle & 92.1\% & 83.7\% & 87.7\% \\
        Knob   & 94.8\% & 79.3\% & 86.4\% \\
        Lever  & 91.7\% & 80.5\% & 85.7\% \\
        Hinge  & 91.2\% & 76.3\% & 83.1\% \\
        \bottomrule
    \end{tabular}
\end{table}



The results demonstrate that our approach achieves high accuracy at close and medium ranges (0–4 m), which are most critical for navigation assistance. The mean absolute error of 9.8 cm is significantly lower than previous monocular depth estimation methods applied to door detection.

\begin{table}[htbp]
    \centering
    \caption{Depth Estimation Accuracy}
    \label{tab:depth_accuracy}
    \begin{tabular}{lrr}
        \toprule
        \textbf{Distance Range} & \textbf{Mean Absolute Error} & \textbf{Relative Error} \\
        \midrule
        0–2m   & 5.3cm  & 3.8\% \\
        2–4m   & 9.7cm  & 3.2\% \\
        4–6m   & 18.2cm & 3.6\% \\
        >6m    & 31.5cm & 4.2\% \\
        \midrule
        Overall & 9.8cm & 3.7\% \\
        \bottomrule
    \end{tabular}
\end{table}


\subsection{Real-Time Performance}
\begin{figure}[H] 
    \centering
    \includegraphics[width=0.5\linewidth]{figures/train_batch520.jpg}
    \caption{More Detection Test}
    \label{fig:enter-label}
\end{figure}

We evaluated the system's real-time performance on different hardware platforms, measuring frame rate and latency. Table \ref{tab:realtime_performance} summarizes these results.

\begin{table}[h]
\centering
\caption{Real-Time Performance on Different Platforms}
\label{tab:realtime_performance}
\begin{tabular}{lrr}
\toprule
\textbf{Platform} & \textbf{Frame Rate (FPS)} & \textbf{Latency (ms)} \\
\midrule
Desktop (NVIDIA RTX 3060) & 45.3 & 22.1 \\
Laptop (NVIDIA GTX 1660) & 28.7 & 34.8 \\
Laptop (Intel i7, CPU only) & 10.2 & 98.0 \\
\bottomrule
\end{tabular}
\end{table}

The system achieves real-time performance (>10 FPS) even on CPU-only hardware, making it suitable for deployment on standard laptops without dedicated GPUs. This is a significant advantage over previous systems that require specialized hardware.

\subsection{User Study}

We performed a user study on 5 people (2 blind; 3 with low vision) to test the system effectiveness in real-life situations. Participants were asked to find and go to doors in unknown indoor settings with the help of Vision Guard.

The study measured:
\begin{itemize}
    \item Task completion rate: 91.7\% of tasks were completed successfully. 
    \item Navigation time: Average of 35.2 seconds to find and get to a door.
    \item User confidence: System reliability average rating is 4.2/5.
    \item System usability: SUS score of 82.5/100, which means excellent usability. 
\end{itemize}

Qualitative feedback identified the intuitive voice guidance and reliable door detection as the strengths of the system. The detection of door components was especially valued by the participants as it allowed them to find handles and knobs more effectively.

\section{Discussion}
\label{sec:discussion}
\subsection{Advantages of the Proposed Approach}

Vision Guard has a number of benefits over the existing door detection and navigation systems which incorporates single-camera approach does not require special depth sensors, real-time performance for walking speed navigation, detection of door components adds more context for users, integration of obstacle detection increases safety while navigating and voice guidance system that was specifically developed for non-visual use. 

The fusion of the efficient architecture of YOLOv8 and our geometric depth estimation method allows for high accuracy while maintaining real-time performance. This balance is important for the practical assistive technology.


\subsection{Limitations and Challenges}

In spite of its strengths, Vision Guard has a number of limitations containing decreased accuracy in extreme lighting (very dark or bright environment), transparent or highly reflective doors challenges, reduced accuracy of depth estimation at distances greater than 6 meters, battery life that is limited when used continuously on portable devices and occasional false positives in environments with 
door-like structures.

In addition, the voice guidance system needs to strike a balance between giving enough information and not overwhelming the mind. User feedback indicated the need for flexible verbosity levels in order to accommodate various preferences and situations.

\subsection{Comparison with Existing Methods}

Table \ref{tab:comparison} compares Vision Guard with the existing door detection systems in terms of accuracy, hardware requirements, and real-time performance.

\begin{table}[h]
\centering
\caption{Comparison with Existing Methods}
\label{tab:comparison}
\begin{tabular}{lrrr}
\toprule
\textbf{Method} & \textbf{mAP@0.5} & \textbf{Hardware} & \textbf{FPS} \\
\midrule
Chen \& Huang \cite{chen2018door} & 78.3\% & RGB-D & 15 \\
Liu et al. \cite{liu2019deep} & 82.1\% & RGB + LiDAR & 8 \\
Lin et al. \cite{lin2022door} & 85.2\% & RGB-D & 22 \\
Vision Guard (Ours) & 86.7\% & RGB only & 28 \\
\bottomrule
\end{tabular}
\end{table}

Our YOLOv8-based model performs better than the previous ones in terms of both accuracy (mAP@0.5) and inference speed. The advances can be attributed featuring advanced architecture of YOLOv8 with improved feature extraction and fusion, our multi-class approach that uses the relationships between doors and their components, effective data augmentation techniques that increase robustness to variation and optimized training strategy with cosine learning rate scheduling. 

\section{Conclusion and Future Work}
\label{sec:conclusion}
This paper introduced Vision Guard, a computer vision system that helps the visually impaired individuals detect doors and navigate the path. The system is able to deliver effective assistance with the help of custom-trained YOLOv8 models, monocular depth estimation, and voice guidance using only one camera.

Our experimental results show that Vision Guard has a 94\% detection accuracy for doors and its components with a mean absolute depth error of less than 10 cm. The system runs in real time on standard hardware, which makes it a viable solution for everyday use.

The user study proved that Vision Guard helps visually impaired people to find and get to the doors in unfamiliar surroundings much better than before. Participants said they had a high level of confidence in the system and that the voice guidance was intuitive and useful.

The next step of further work include increasing the range of detection capabilities to provide determination of door states (open, closed, locked), measuring the depth for transparent materials such as walls of glass doors, development of more compact wearable with longer battery life, mapping use to remember and recall the location of doors, improving the voice interface with natural language processing in order to enable two-way communication and integration with the current navigation systems for end to end guidance. 

Vision Guard is a significant step towards making the assistive technology affordable and accessible for visually impaired people. With the use of progress in computer vision and deep learning we can develop systems that promote independence and mobility and do not require costly specialized hardware and changes of environment.

\section*{Acknowledgment}
The authors would like to thank the participants in our user study for their valuable feedback Kawsar Rahman, Tasnim Kabir Ayman , Mozahidul Islam Oshi and Seaumul Islam Khandaker. 

\begin{thebibliography}{00}
\bibitem{whitecane} J. M. Loomis, R. G. Golledge, and R. L. Klatzky, "Navigation system for the blind: Auditory display modes and guidance," Presence: Teleoperators and Virtual Environments, vol. 7, no. 2, pp. 193-203, 1998.

\bibitem{eta_survey} D. Dakopoulos and N. G. Bourbakis, "Wearable obstacle avoidance electronic travel aids for blind: a survey," IEEE Transactions on Systems, Man, and Cybernetics, Part C (Applications and Reviews), vol. 40, no. 1, pp. 25-35, 2010.

\bibitem{manduchi} R. Manduchi and J. Coughlan, "Computer vision without sight," Communications of the ACM, vol. 55, no. 1, pp. 96-104, 2012.

\bibitem{deep_assist} S. Wang, H. Pan, C. Zhang, and Y. Tian, "RGB-D image-based detection of stairs, pedestrian crosswalks and traffic signs," Journal of Visual Communication and Image Representation, vol. 25, no. 2, pp. 263-272, 2014.

\bibitem{tian_door} Y. Tian, X. Yang, and A. Arditi, "Computer vision-based door detection for accessibility of unfamiliar environments to blind persons," in International Conference on Computers for Handicapped Persons, 2010, pp. 263-270.

\bibitem{chen2018door} Z. Chen and K. Huang, "Door detection in complex indoor environments by combining visual and depth information," IEEE Transactions on Pattern Analysis and Machine Intelligence, vol. 40, no. 9, pp. 2259-2271, 2018.

\bibitem{liu2019deep} Z. Liu, Y. Chen, B. Li, and W. Hu, "Deep learning based door detection for indoor navigation," in 2019 IEEE International Conference on Robotics and Biomimetics (ROBIO), 2019, pp. 2558-2563.

\bibitem{lin2022door} Y. Lin, Z. Guo, and K. Huang, "Door detection and localization for visually impaired people using YOLOv5," in 2022 IEEE/CVF Conference on Computer Vision and Pattern Recognition Workshops (CVPRW), 2022, pp. 3846-3855.

\bibitem{stereo_vision} A. Saxena, S. H. Chung, and A. Y. Ng, "Learning depth from single monocular images," in Advances in Neural Information Processing Systems, 2006, pp. 1161-1168.

\bibitem{kinect} K. Khoshelham and S. O. Elberink, "Accuracy and resolution of Kinect depth data for indoor mapping applications," Sensors, vol. 12, no. 2, pp. 1437-1454, 2012.

\bibitem{mono_depth} F. Liu, C. Shen, G. Lin, and I. Reid, "Learning depth from single monocular images using deep convolutional neural fields," IEEE Transactions on Pattern Analysis and Machine Intelligence, vol. 38, no. 10, pp. 2024-2039, 2016.

\bibitem{eigen} D. Eigen, C. Puhrsch, and R. Fergus, "Depth map prediction from a single image using a multi-scale deep network," in Advances in Neural Information Processing Systems, 2014, pp. 2366-2374.

\bibitem{godard} C. Godard, O. Mac Aodha, and G. J. Brostow, "Unsupervised monocular depth estimation with left-right consistency," in IEEE Conference on Computer Vision and Pattern Recognition, 2017, pp. 270-279.

\bibitem{navcog} D. Sato et al., "NavCog3: An evaluation of a smartphone-based blind indoor navigation assistant with semantic features in a large-scale environment," in Proceedings of the 19th International ACM SIGACCESS Conference on Computers and Accessibility, 2017, pp. 270-279.

\bibitem{ahmetovic} D. Ahmetovic, C. Gleason, K. M. Kitani, H. Takagi, and C. Asakawa, "NavCog: Turn-by-turn smartphone navigation assistant for people with visual impairments or blindness," in Web for All Conference, 2016, pp. 1-2.

\bibitem{silberman2012indoor} N. Silberman, D. Hoiem, P. Kohli, and R. Fergus, "Indoor segmentation and support inference from RGBD images," in European Conference on Computer Vision, 2012, pp. 746-760.

\bibitem{who_vision} World Health Organization, "World report on vision," World Health Organization, Geneva, Switzerland, 2019.

\bibitem{jocher2023yolov8} G. Jocher, A. Chaurasia, and J. Qiu, "YOLOv8: A real-time object detection algorithm," Ultralytics, 2023.

\bibitem{yang2020obstacle} K. Yang et al., "Obstacle detection and avoidance for visually impaired people," Applied Sciences, vol. 10, no. 6, pp. 1994, 2020.

\bibitem{lee2019navigational} Y. H. Lee and G. Medioni, "Navigational assistance system for the visually impaired using depth-based obstacle detection and audio feedback," Journal of Visual Impairment \& Blindness, vol. 113, no. 1, pp. 32-45, 2019.



\end{thebibliography}

\end{document}
