import tensorflow as tf

# Print TensorFlow version
print("TensorFlow version:", tf.__version__)

# List available GPUs
gpus = tf.config.list_physical_devices('GPU')
if gpus:
    print("Num GPUs Available:", len(gpus))
    for gpu in gpus:
        print("GPU:", gpu)
else:
    print("No GPU detected. TensorFlow is using CPU.")

# Optional: Run a small matrix multiplication to confirm usage
@tf.function
def test_matrix_multiplication():
    with tf.device('/GPU:0' if gpus else '/CPU:0'):
        a = tf.random.normal([1000, 1000])
        b = tf.random.normal([1000, 1000])
        c = tf.matmul(a, b)
    return c

# Run the test
print("Running a test computation...")
result = test_matrix_multiplication()
print("Test completed.")
