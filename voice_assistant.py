import pyttsx3
import speech_recognition as sr
import threading
import time
import queue

class VoiceAssistant:
    """
    Class for providing voice guidance and processing voice commands.
    Uses pyttsx3 for text-to-speech and speech_recognition for speech-to-text.
    """
    
    def __init__(self, rate=150, volume=0.8):
        """
        Initialize the voice assistant.
        
        Args:
            rate (int): Speech rate (words per minute).
            volume (float): Volume level (0.0 to 1.0).
        """
        # Initialize text-to-speech engine
        self.engine = pyttsx3.init()
        self.engine.setProperty('rate', rate)
        self.engine.setProperty('volume', volume)
        
        # Get available voices
        voices = self.engine.getProperty('voices')
        if voices:
            # Try to set a female voice if available
            for voice in voices:
                if 'female' in voice.name.lower():
                    self.engine.setProperty('voice', voice.id)
                    break
        
        # Initialize speech recognition
        self.recognizer = sr.Recognizer()
        self.microphone = sr.Microphone()
        
        # Adjust for ambient noise
        with self.microphone as source:
            self.recognizer.adjust_for_ambient_noise(source)
        
        # Message queue for TTS
        self.message_queue = queue.Queue()
        self.speaking = False
        self.last_command_time = 0
        self.command_cooldown = 2.0  # seconds
        
        # Start the TTS thread
        self.tts_thread = threading.Thread(target=self._process_tts_queue, daemon=True)
        self.tts_thread.start()
        
        # Command callback
        self.command_callback = None
        
        # Flag to control listening thread
        self.listening = False
        self.listen_thread = None
    
    def _process_tts_queue(self):
        """Process messages in the TTS queue."""
        while True:
            try:
                message = self.message_queue.get(timeout=0.1)
                self.speaking = True
                self.engine.say(message)
                self.engine.runAndWait()
                self.speaking = False
                self.message_queue.task_done()
            except queue.Empty:
                time.sleep(0.1)
            except Exception as e:
                print(f"TTS error: {e}")
                self.speaking = False
    
    def speak(self, message, priority=False):
        """
        Speak a message.
        
        Args:
            message (str): Message to speak.
            priority (bool): If True, clear the queue and speak immediately.
        """
        if priority:
            # Clear the queue
            while not self.message_queue.empty():
                try:
                    self.message_queue.get_nowait()
                    self.message_queue.task_done()
                except queue.Empty:
                    break
        
        self.message_queue.put(message)
    
    def _listen_for_commands(self):
        """Listen for voice commands in a loop."""
        self.listening = True
        
        while self.listening:
            try:
                # Check if enough time has passed since the last command
                if time.time() - self.last_command_time < self.command_cooldown:
                    time.sleep(0.1)
                    continue
                
                with self.microphone as source:
                    print("Listening for commands...")
                    audio = self.recognizer.listen(source, timeout=1, phrase_time_limit=5)
                
                try:
                    command = self.recognizer.recognize_google(audio).lower()
                    print(f"Recognized: {command}")
                    
                    # Update last command time
                    self.last_command_time = time.time()
                    
                    # Process command
                    if self.command_callback:
                        self.command_callback(command)
                    
                except sr.UnknownValueError:
                    # Speech was unintelligible
                    pass
                except sr.RequestError as e:
                    print(f"Could not request results; {e}")
                
            except Exception as e:
                print(f"Listening error: {e}")
                time.sleep(0.1)
    
    def start_listening(self, command_callback=None):
        """
        Start listening for voice commands.
        
        Args:
            command_callback (callable): Callback function to process recognized commands.
        """
        if self.listening:
            return
            
        self.command_callback = command_callback
        self.listen_thread = threading.Thread(target=self._listen_for_commands, daemon=True)
        self.listen_thread.start()
        
        # Announce that the system is ready
        self.speak("Voice assistant is ready. Say 'take me to the door' to start navigation.")
    
    def stop_listening(self):
        """Stop listening for voice commands."""
        self.listening = False
        if self.listen_thread:
            self.listen_thread.join(timeout=1.0)
            self.listen_thread = None
    
    def provide_navigation_guidance(self, command, distance=None):
        """
        Provide navigation guidance based on the command.
        
        Args:
            command (str): Navigation command ('left', 'right', 'forward', or 'stop').
            distance (float, optional): Distance to the door in meters.
        """
        if command == "left":
            self.speak("Turn left")
        elif command == "right":
            self.speak("Turn right")
        elif command == "forward":
            self.speak("Go forward")
        elif command == "stop":
            self.speak("Stop")
        
        # Provide distance information if available
        if distance is not None:
            if distance < 1.0:
                self.speak(f"Door is {distance:.1f} meters away. You are close to the door.", priority=True)
            elif distance < 3.0:
                self.speak(f"Door is {distance:.1f} meters away")
    
    def announce_door_detected(self, distance=None):
        """
        Announce that a door has been detected.
        
        Args:
            distance (float, optional): Distance to the door in meters.
        """
        if distance is not None:
            self.speak(f"Door detected {distance:.1f} meters away", priority=True)
        else:
            self.speak("Door detected", priority=True)
    
    def announce_no_door(self):
        """Announce that no door is detected."""
        self.speak("No door detected. Please look around.")
    
    def announce_obstacle(self, direction=None):
        """
        Announce that an obstacle has been detected.
        
        Args:
            direction (str, optional): Direction of the obstacle ('left', 'right', or 'front').
        """
        if direction:
            self.speak(f"Obstacle detected on your {direction}", priority=True)
        else:
            self.speak("Obstacle detected", priority=True)
