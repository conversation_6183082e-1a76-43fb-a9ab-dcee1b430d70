import cv2
import numpy as np
import time
import threading
from ultralytics import YOL<PERSON>
from distance_estimation import DistanceEstimator
from path_planning import PathPlanner
from voice_assistant import VoiceAssistant

class DoorNavigationAssistant:
    """
    Main class for the door navigation assistant system.
    Integrates door detection, distance estimation, path planning, and voice guidance.
    """
    
    def __init__(self, 
                 model_path='runs/train/yolov8_door_detection/weights/best.pt',
                 camera_id=0,
                 confidence=0.4,
                 door_class_names=None,
                 frame_width=640,
                 frame_height=480):
        """
        Initialize the door navigation assistant.
        
        Args:
            model_path (str): Path to the trained YOLOv8 model.
            camera_id (int): Camera device ID.
            confidence (float): Confidence threshold for detections.
            door_class_names (list): List of class names that represent doors.
            frame_width (int): Width of the camera frame.
            frame_height (int): Height of the camera frame.
        """
        # Initialize YOLO model
        print(f"Loading model from {model_path}...")
        self.model = YOLO(model_path)
        
        # Set default door class names if not provided
        if door_class_names is None:
            self.door_class_names = ['door', 'Door']
        else:
            self.door_class_names = door_class_names
            
        # Initialize camera
        self.camera_id = camera_id
        self.cap = None
        self.frame_width = frame_width
        self.frame_height = frame_height
        
        # Detection parameters
        self.confidence = confidence
        
        # Initialize components
        self.distance_estimator = DistanceEstimator()
        self.path_planner = PathPlanner(frame_width, frame_height)
        self.voice_assistant = VoiceAssistant()
        
        # State variables
        self.running = False
        self.navigating = False
        self.door_detected = False
        self.door_distance = None
        self.door_bbox = None
        self.obstacles = []
        
        # Navigation parameters
        self.last_guidance_time = 0
        self.guidance_interval = 2.0  # seconds
        self.close_door_threshold = 1.0  # meters
        self.door_announced = False
        
        # Processing thread
        self.process_thread = None
        
    def initialize_camera(self):
        """Initialize the camera."""
        if self.cap is not None and self.cap.isOpened():
            self.cap.release()
            
        self.cap = cv2.VideoCapture(self.camera_id)
        self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.frame_width)
        self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.frame_height)
        
        if not self.cap.isOpened():
            raise RuntimeError(f"Could not open camera {self.camera_id}")
            
        # Get actual camera properties
        self.frame_width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        self.frame_height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        print(f"Camera initialized with resolution: {self.frame_width}x{self.frame_height}")
        
    def process_frame(self, frame):
        """
        Process a frame to detect doors and obstacles.
        
        Args:
            frame (numpy.ndarray): The image frame.
            
        Returns:
            tuple: Processed frame, door detected flag, door distance, door bounding box, obstacles.
        """
        # Perform detection
        results = self.model(frame, conf=self.confidence)
        
        # Process results
        door_detected = False
        door_distance = None
        door_bbox = None
        obstacles = []
        
        # Get detections
        for result in results:
            boxes = result.boxes
            
            for box in boxes:
                # Get class and confidence
                cls = int(box.cls[0])
                cls_name = self.model.names[cls]
                conf = float(box.conf[0])
                
                # Get bounding box
                x1, y1, x2, y2 = box.xyxy[0].cpu().numpy().astype(int)
                bbox = (x1, y1, x2, y2)
                
                # Check if it's a door
                if cls_name.lower() in [name.lower() for name in self.door_class_names]:
                    # If multiple doors, choose the closest/largest one
                    if not door_detected or (x2 - x1) > (door_bbox[2] - door_bbox[0]):
                        door_detected = True
                        door_bbox = bbox
                        door_distance = self.distance_estimator.estimate_distance_from_bbox(bbox)
                else:
                    # It's an obstacle
                    obstacles.append(bbox)
        
        # Draw detections on frame
        annotated_frame = results[0].plot()
        
        # Draw distance if door detected
        if door_detected and door_distance is not None:
            annotated_frame = self.distance_estimator.draw_distance(annotated_frame, door_bbox, door_distance)
        
        return annotated_frame, door_detected, door_distance, door_bbox, obstacles
    
    def process_navigation(self, frame, door_detected, door_bbox, door_distance, obstacles):
        """
        Process navigation based on detections.
        
        Args:
            frame (numpy.ndarray): The image frame.
            door_detected (bool): Whether a door was detected.
            door_bbox (tuple): Door bounding box (x1, y1, x2, y2).
            door_distance (float): Distance to the door in meters.
            obstacles (list): List of obstacle bounding boxes.
            
        Returns:
            numpy.ndarray: Frame with navigation information drawn.
        """
        if not self.navigating:
            return frame
            
        # Create obstacle map
        obstacle_map = self.path_planner.create_obstacle_map(obstacles)
        
        # Define start and goal points
        start_point = (self.frame_width // 2, self.frame_height - 50)  # Bottom center of frame
        
        if door_detected and door_bbox is not None:
            # Goal is the bottom center of the door
            x1, y1, x2, y2 = door_bbox
            goal_point = ((x1 + x2) // 2, y2)
            
            # Find path
            path = self.path_planner.find_path(start_point, goal_point, obstacle_map)
            
            # Draw path
            frame = self.path_planner.draw_path(frame, path)
            
            # Get navigation command
            command = self.path_planner.get_navigation_command(start_point, path)
            
            # Provide voice guidance
            current_time = time.time()
            if current_time - self.last_guidance_time >= self.guidance_interval:
                self.voice_assistant.provide_navigation_guidance(command, door_distance)
                self.last_guidance_time = current_time
                
            # Check if close to door
            if door_distance is not None and door_distance < self.close_door_threshold and not self.door_announced:
                self.voice_assistant.speak("You have reached the door. Stop.", priority=True)
                self.door_announced = True
                
        elif self.door_detected and not door_detected:
            # Door was detected before but not now
            self.voice_assistant.speak("Door lost. Please look around.", priority=True)
            self.door_announced = False
            
        return frame
    
    def process_loop(self):
        """Main processing loop."""
        try:
            self.initialize_camera()
            
            while self.running:
                # Read frame
                ret, frame = self.cap.read()
                
                if not ret:
                    print("Error: Failed to capture frame")
                    time.sleep(0.1)
                    continue
                
                # Process frame
                processed_frame, door_detected, door_distance, door_bbox, obstacles = self.process_frame(frame)
                
                # Update state
                self.door_detected = door_detected
                self.door_distance = door_distance
                self.door_bbox = door_bbox
                self.obstacles = obstacles
                
                # Process navigation
                if self.navigating:
                    processed_frame = self.process_navigation(
                        processed_frame, door_detected, door_bbox, door_distance, obstacles
                    )
                
                # Display frame
                cv2.imshow("Door Navigation Assistant", processed_frame)
                
                # Check for key press
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    break
                elif key == ord('n'):
                    self.start_navigation()
                elif key == ord('s'):
                    self.stop_navigation()
                
        except Exception as e:
            print(f"Error in processing loop: {e}")
        finally:
            if self.cap is not None:
                self.cap.release()
            cv2.destroyAllWindows()
    
    def process_voice_command(self, command):
        """
        Process a voice command.
        
        Args:
            command (str): Voice command.
        """
        if "take me to the door" in command or "find door" in command or "find the door" in command:
            self.start_navigation()
        elif "stop" in command or "halt" in command:
            self.stop_navigation()
    
    def start_navigation(self):
        """Start navigation to the door."""
        if not self.navigating:
            self.navigating = True
            self.door_announced = False
            self.voice_assistant.speak("Starting navigation to the door. Please move slowly.", priority=True)
            
            if self.door_detected and self.door_distance is not None:
                self.voice_assistant.announce_door_detected(self.door_distance)
            else:
                self.voice_assistant.announce_no_door()
    
    def stop_navigation(self):
        """Stop navigation."""
        if self.navigating:
            self.navigating = False
            self.voice_assistant.speak("Navigation stopped.", priority=True)
    
    def start(self):
        """Start the door navigation assistant."""
        if self.running:
            return
            
        self.running = True
        
        # Start processing thread
        self.process_thread = threading.Thread(target=self.process_loop)
        self.process_thread.daemon = True
        self.process_thread.start()
        
        # Start voice assistant
        self.voice_assistant.start_listening(self.process_voice_command)
        
        print("Door Navigation Assistant started")
        print("Press 'q' to quit, 'n' to start navigation, 's' to stop navigation")
    
    def stop(self):
        """Stop the door navigation assistant."""
        self.running = False
        self.navigating = False
        
        # Stop voice assistant
        self.voice_assistant.stop_listening()
        
        # Wait for processing thread to finish
        if self.process_thread:
            self.process_thread.join(timeout=1.0)
            
        print("Door Navigation Assistant stopped")

def main():
    """Main function."""
    # Create door navigation assistant
    assistant = DoorNavigationAssistant(
        model_path='runs/train/yolov8_door_detection/weights/best.pt',
        camera_id=0,
        confidence=0.4,
        door_class_names=['door', 'Door', 'knob', 'lever', 'hinge'],
        frame_width=640,
        frame_height=480
    )
    
    try:
        # Start assistant
        assistant.start()
        
        # Keep main thread alive
        while True:
            time.sleep(0.1)
            
    except KeyboardInterrupt:
        print("Interrupted by user")
    finally:
        # Stop assistant
        assistant.stop()

if __name__ == "__main__":
    main()
