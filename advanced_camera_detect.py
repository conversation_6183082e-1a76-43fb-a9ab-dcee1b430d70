import cv2
import time
import os
import numpy as np
from ultralytics import YOLO
from datetime import datetime
import argparse
from collections import Counter

def parse_arguments():
    parser = argparse.ArgumentParser(description='Advanced YOLOv8 Door Detection with Camera')
    parser.add_argument('--model', type=str, default='runs/train/yolov8_door_detection/weights/best.pt',
                        help='Path to the trained YOLOv8 model')
    parser.add_argument('--conf', type=float, default=0.25,
                        help='Confidence threshold for detections')
    parser.add_argument('--camera', type=int, default=0,
                        help='Camera device ID (default: 0)')
    parser.add_argument('--width', type=int, default=640,
                        help='Width of the camera feed')
    parser.add_argument('--height', type=int, default=480,
                        help='Height of the camera feed')
    parser.add_argument('--output_dir', type=str, default='camera_detections',
                        help='Directory to save detection images')
    return parser.parse_args()

def main():
    # Parse command-line arguments
    args = parse_arguments()
    
    # Create output directory if it doesn't exist
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load the YOLOv8 model
    print(f"Loading model from {args.model}...")
    model = YOLO(args.model)
    
    # Initialize the camera
    print(f"Opening camera device {args.camera}...")
    cap = cv2.VideoCapture(args.camera)
    
    # Set camera properties
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, args.width)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, args.height)
    
    # Check if the camera opened successfully
    if not cap.isOpened():
        print("Error: Could not open camera.")
        return
    
    # Get actual camera properties
    actual_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    actual_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    
    print(f"Camera initialized with resolution: {actual_width}x{actual_height}")
    
    # Create a window for display
    window_name = "YOLOv8 Door Detection"
    cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
    cv2.resizeWindow(window_name, actual_width, actual_height)
    
    # Variables for FPS calculation
    fps = 0
    frame_count = 0
    start_time = time.time()
    
    # Variables for recording
    recording = False
    video_writer = None
    video_path = None
    
    # Variables for detection statistics
    total_detections = 0
    class_counts = Counter()
    
    print("Controls:")
    print("  'q' - Quit")
    print("  's' - Save current frame with detections")
    print("  'r' - Start/stop recording")
    
    # Main loop
    while True:
        # Read a frame from the camera
        ret, frame = cap.read()
        
        if not ret:
            print("Error: Failed to capture frame from camera.")
            break
        
        # Perform detection
        results = model(frame, conf=args.conf)
        
        # Process the results
        annotated_frame = results[0].plot()
        
        # Update detection statistics
        detections = results[0].boxes
        num_detections = len(detections)
        total_detections += num_detections
        
        # Update class counts
        for box in detections:
            cls = int(box.cls[0])
            cls_name = model.names[cls]
            class_counts[cls_name] += 1
        
        # Calculate and display FPS
        frame_count += 1
        elapsed_time = time.time() - start_time
        if elapsed_time >= 1.0:
            fps = frame_count / elapsed_time
            frame_count = 0
            start_time = time.time()
        
        # Display FPS on the frame
        cv2.putText(annotated_frame, f"FPS: {fps:.1f}", (10, 30), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
        
        # Display the detection count
        cv2.putText(annotated_frame, f"Detections: {num_detections}", (10, 60), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
        
        # Display recording status
        if recording:
            cv2.putText(annotated_frame, "REC", (actual_width - 70, 30), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
            
            # Add recording to video file
            if video_writer is not None:
                video_writer.write(annotated_frame)
        
        # Display detection information
        if num_detections > 0:
            y_offset = 90
            for i, box in enumerate(detections):
                cls = int(box.cls[0])
                cls_name = model.names[cls]
                conf = float(box.conf[0])
                
                # Display class name and confidence
                text = f"{cls_name}: {conf:.2f}"
                cv2.putText(annotated_frame, text, (10, y_offset), 
                            cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                y_offset += 25
        
        # Display class statistics
        y_offset = 90
        cv2.putText(annotated_frame, "Class Statistics:", (actual_width - 200, y_offset), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)
        y_offset += 25
        
        for cls_name, count in class_counts.most_common(4):
            cv2.putText(annotated_frame, f"{cls_name}: {count}", (actual_width - 200, y_offset), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)
            y_offset += 25
        
        # Show the frame
        cv2.imshow(window_name, annotated_frame)
        
        # Check for key press
        key = cv2.waitKey(1) & 0xFF
        
        if key == ord('q'):
            print("Quitting...")
            break
        elif key == ord('s'):
            # Save the current frame with detections
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            save_path = os.path.join(args.output_dir, f"detection_{timestamp}.jpg")
            cv2.imwrite(save_path, annotated_frame)
            print(f"Saved detection to {save_path}")
        elif key == ord('r'):
            # Toggle recording
            recording = not recording
            
            if recording:
                # Start recording
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                video_path = os.path.join(args.output_dir, f"recording_{timestamp}.mp4")
                fourcc = cv2.VideoWriter_fourcc(*'mp4v')
                video_writer = cv2.VideoWriter(video_path, fourcc, 20.0, (actual_width, actual_height))
                print(f"Started recording to {video_path}")
            else:
                # Stop recording
                if video_writer is not None:
                    video_writer.release()
                    print(f"Stopped recording. Saved to {video_path}")
                    video_writer = None
    
    # Release resources
    cap.release()
    if video_writer is not None:
        video_writer.release()
    cv2.destroyAllWindows()
    
    # Print final statistics
    print("\nDetection Statistics:")
    print(f"Total detections: {total_detections}")
    print("Class counts:")
    for cls_name, count in class_counts.most_common():
        print(f"  {cls_name}: {count}")
    
    print("Camera detection stopped.")

if __name__ == "__main__":
    main()
