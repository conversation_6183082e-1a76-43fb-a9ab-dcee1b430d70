import cv2
import numpy as np
import time
import threading
import subprocess
import platform
import os
from ultralytics import YOLO
import pyttsx3
import speech_recognition as sr
import queue

class VisionGuard:
    def __init__(self,
                 model_path='runs/train/yolov8_door_detection/weights/best.pt',
                 camera_id=0,
                 confidence=0.4,
                 door_class_names=None,
                 obstacle_class_names=None,
                 frame_width=800,  # Increased resolution for better visibility
                 frame_height=600,
                 focal_length=800,
                 known_door_width=0.9):  # meters
        # Initialize YOLO model
        print(f"Loading model from {model_path}...")
        self.model = YOLO(model_path)

        # Set default door class names if not provided
        if door_class_names is None:
            self.door_class_names = ['door', 'Door', 'hinged', 'knob', 'lever']
        else:
            self.door_class_names = door_class_names

        # Set default obstacle class names if not provided
        if obstacle_class_names is None:
            # Include all class names except door-related ones as potential obstacles
            # This ensures we detect any object that's not a door as an obstacle
            self.obstacle_class_names = []
            if hasattr(self.model, 'names'):
                for _, name in self.model.names.items():  # Use _ for unused index
                    if name.lower() not in [door_name.lower() for door_name in self.door_class_names]:
                        self.obstacle_class_names.append(name)

            # If model doesn't have names or no names were found, use COCO class names
            if not self.obstacle_class_names:
                self.obstacle_class_names = [
                    'person', 'bicycle', 'car', 'motorcycle', 'chair', 'couch',
                    'potted plant', 'bed', 'dining table', 'toilet', 'tv', 'laptop',
                    'mouse', 'remote', 'keyboard', 'cell phone', 'microwave',
                    'oven', 'toaster', 'sink', 'refrigerator', 'book', 'clock',
                    'vase', 'scissors', 'teddy bear', 'hair drier', 'toothbrush',
                    # Add more generic obstacle names
                    'wall', 'furniture', 'object', 'obstacle' # Added for completeness, might not be in YOLO default
                ]
        else:
            self.obstacle_class_names = obstacle_class_names

        print(f"Door class names: {self.door_class_names}")
        print(f"Obstacle class names: {self.obstacle_class_names}")

        # Initialize camera
        self.camera_id = camera_id
        self.cap = None
        self.frame_width = frame_width
        self.frame_height = frame_height

        # Detection parameters
        self.confidence = confidence

        # Distance estimation parameters
        self.focal_length = focal_length
        self.known_door_width = known_door_width

        # State variables
        self.running = False
        self.navigating = False
        self.door_detected = False
        self.door_distance = None
        self.door_bbox = None
        self.door_center_x = None
        self.obstacles = []
        self.door_state = "unknown" # NEW: State of the door (open, closed, locked, unknown)

        # Navigation parameters
        self.last_guidance_time = 0
        self.guidance_interval = 2.0  # seconds
        self.close_door_threshold = 1.0  # meters
        self.obstacle_warning_threshold = 1.5  # meters
        self.door_announced = False
        self.obstacle_announced = False
        self.door_state_announced = False # NEW: Flag to prevent repetitive door state announcements

        # Frame processing rate control
        self.process_every_n_frames = 2  # Process every 2nd frame for better performance
        self.frame_count = 0

        # Processing thread
        self.process_thread = None

        # For depth estimation
        self.depth_map = None

        # For smoother guidance
        self.direction_history = []
        self.direction_history_max = 5
        self.last_direction = None

        # For path planning
        self.path = []
        self.safety_margin = 30  # pixels

        # For perspective views
        self.left_view = None
        self.right_view = None

        # For decision making
        self.decision = None
        self.decision_confidence = 0.0

        # Initialize TTS engine
        self.is_windows = platform.system() == 'Windows'
        if self.is_windows:
            self.tts_engine = pyttsx3.init()
            self.tts_engine.setProperty('rate', 150)
            self.tts_engine.setProperty('volume', 0.8)
        else:
            self.tts_engine = None

        # Initialize speech recognition
        self.recognizer = sr.Recognizer()
        try:
            self.microphone = sr.Microphone()
            with self.microphone as source:
                self.recognizer.adjust_for_ambient_noise(source)
        except Exception as e:
            print(f"Microphone initialization error: {e}")
            print("Voice commands will be disabled. You can still use keyboard controls.")
            print("To enable voice commands, install PyAudio: pip install pyaudio")
            self.microphone = None

        # Message queue for TTS
        self.message_queue = queue.Queue()
        self.speaking = False

        # Start the TTS thread
        self.tts_thread = threading.Thread(target=self._process_tts_queue, daemon=True)
        self.tts_thread.start()

        # Voice command thread
        self.listening = False
        self.listen_thread = None

        # Display frames
        self.display_frames = {}

    def _process_tts_queue(self):
        """Process messages in the TTS queue."""
        while True:
            try:
                message = self.message_queue.get(timeout=0.1)
                self.speaking = True
                if self.is_windows and self.tts_engine:
                    self.tts_engine.say(message)
                    self.tts_engine.runAndWait()
                else:
                    # Use subprocess for non-Windows platforms
                    # Check if 'espeak' is available
                    try:
                        subprocess.run(['which', 'espeak'], check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                        subprocess.Popen(['espeak', '-s', '150', '-a', '200', message])
                        time.sleep(len(message) * 0.07) # Adjust sleep for espeak speed
                    except (subprocess.CalledProcessError, FileNotFoundError):
                        print(f"espeak not found or error. Cannot speak: {message}")
                self.speaking = False
                self.message_queue.task_done()
            except queue.Empty:
                time.sleep(0.1)
            except Exception as e:
                print(f"TTS error: {e}")
                self.speaking = False

    def speak(self, text, priority=False):
        print(f"Speaking: {text}")

        if priority:
            # Clear the queue
            while not self.message_queue.empty():
                try:
                    self.message_queue.get_nowait()
                    self.message_queue.task_done()
                except queue.Empty:
                    break

            # Stop current speech
            if self.is_windows and self.tts_engine:
                self.tts_engine.stop()

        self.message_queue.put(text)

    def initialize_camera(self):
        """Initialize the camera or use a sample image if camera is not available."""
        if self.cap is not None and self.cap.isOpened():
            self.cap.release()
        # Try to open the camera
        try:
            self.cap = cv2.VideoCapture(self.camera_id)
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.frame_width)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.frame_height)

            # Try to read a frame to confirm camera is working
            ret, _ = self.cap.read()
            if not ret:
                raise RuntimeError("Camera opened but failed to read frame")

        except Exception as e:
            print(f"Camera error: {e}")
            if self.cap is not None:
                self.cap.release()
                self.cap = None

        # Check if camera opened successfully
        if self.cap is None or not self.cap.isOpened():
            print(f"Warning: Could not open camera {self.camera_id}")
            print("Using sample images instead...")

            # Create a list of sample images to simulate a video feed
            self.sample_images = []

            # Try to find sample images in the dataset
            # Paths to look for sample images. Adjusted for common YOLOv8 dataset structures.
            sample_paths_root = [
                ".",
                "dataset",
                "data",
                "yolov8_door_detection_dataset" # Assuming a specific dataset folder name
            ]

            image_subfolders = [
                "images/train",
                "images/val",
                "images/test",
                "images",
                "results" # if results images are to be used
            ]

            found_any_images = False
            for root_path in sample_paths_root:
                for subfolder in image_subfolders:
                    full_path = os.path.join(root_path, subfolder)
                    if os.path.exists(full_path):
                        image_files = [os.path.join(full_path, f) for f in os.listdir(full_path) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
                        if image_files:
                            print(f"Found {len(image_files)} sample images in {full_path}")
                            self.sample_images.extend(image_files[:20])  # Limit to 20 images per folder
                            found_any_images = True
                            if len(self.sample_images) >= 20: # Aim for a decent number of samples
                                break
                if found_any_images and len(self.sample_images) >= 20:
                    break

            # If no images found, create a blank image
            if not self.sample_images:
                print("No sample images found. Creating a blank image.")
                blank_image = np.zeros((self.frame_height, self.frame_width, 3), dtype=np.uint8)
                cv2.putText(blank_image, "No camera or sample images available", (50, self.frame_height // 2),
                            cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
                self.sample_images = [blank_image]
                self.current_sample_index = 0
                self.using_sample_images = True
                self.using_sample_image_paths = False
            else:
                self.current_sample_index = 0
                self.using_sample_images = True
                self.using_sample_image_paths = True

            print(f"Using {len(self.sample_images)} sample images for simulation")
        else:
            # Get actual camera properties
            self.frame_width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            self.frame_height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            self.using_sample_images = False
            print(f"Camera initialized with resolution: {self.frame_width}x{self.frame_height}")

    def estimate_distance(self, bbox_width):
        if bbox_width == 0:
            return float('inf')

        distance = (self.known_door_width * self.focal_length) / bbox_width
        return distance

    def estimate_depth_map(self, frame):
        # Convert to grayscale
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

        # Apply Sobel filter to get gradients
        sobelx = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
        sobely = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)

        # Calculate gradient magnitude
        magnitude = np.sqrt(sobelx**2 + sobely**2)

        # Normalize to 0-255
        magnitude = cv2.normalize(magnitude, None, 0, 255, cv2.NORM_MINMAX)

        # Invert (stronger edges are closer)
        depth_map = 255 - magnitude.astype(np.uint8)

        # Apply Gaussian blur to smooth the depth map
        depth_map = cv2.GaussianBlur(depth_map, (15, 15), 0)

        return depth_map

    def create_perspective_views(self, frame):
        # Get frame dimensions
        height, width = frame.shape[:2]

        # Split the frame into left and right halves
        mid = width // 2
        left_view = frame[:, :mid].copy()
        right_view = frame[:, mid:].copy()

        # Add a vertical line to show the split
        cv2.line(left_view, (mid-1, 0), (mid-1, height), (0, 255, 255), 2)
        cv2.line(right_view, (0, 0), (0, height), (0, 255, 255), 2)

        # Add labels to the views
        cv2.putText(left_view, "LEFT VIEW", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
        cv2.putText(right_view, "RIGHT VIEW", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)

        return left_view, right_view

    def create_obstacle_map(self, obstacles):
        obstacle_map = np.zeros((self.frame_height, self.frame_width), dtype=np.uint8)

        # Mark obstacles on the map
        for obstacle in obstacles:
            x1, y1, x2, y2 = obstacle['bbox']
            # Add safety margin around obstacles
            x1 = max(0, x1 - self.safety_margin)
            y1 = max(0, y1 - self.safety_margin)
            x2 = min(self.frame_width, x2 + self.safety_margin)
            y2 = min(self.frame_height, y2 + self.safety_margin)

            obstacle_map[y1:y2, x1:x2] = 1

        return obstacle_map

    def find_path(self, start_point, goal_point, obstacle_map):
        from scipy.ndimage import distance_transform_edt

        # Create distance transform from obstacles
        # This gives each pixel its distance to the nearest obstacle
        dist_transform = distance_transform_edt(1 - obstacle_map)

        # Create attractive potential field (goal)
        y, x = np.indices((self.frame_height, self.frame_width))
        goal_x, goal_y = goal_point
        attractive = np.sqrt((x - goal_x)**2 + (y - goal_y)**2)

        # Combine potential fields
        # Higher values of dist_transform mean safer areas
        # Lower values of attractive mean closer to goal
        # We want to maximize safety while minimizing distance to goal
        potential = attractive - 5.0 * dist_transform # Increased influence of distance transform for safety

        # Find path using gradient descent
        path = []
        current = np.array(start_point).astype(float) # Ensure float for continuous movement
        path.append(current.copy())

        max_iterations = 200 # Increased for potentially longer paths
        step_size = 5.0
        goal_threshold = 20

        for _ in range(max_iterations):
            # Check if we're close enough to the goal
            if np.linalg.norm(current - np.array(goal_point)) < goal_threshold:
                break

            # Get current position (rounded to integers)
            cx, cy = np.round(current).astype(int)
            cx = np.clip(cx, 0, self.frame_width - 1)
            cy = np.clip(cy, 0, self.frame_height - 1)

            # Sample potential field in neighborhood for gradient
            # This is a simple numerical gradient approximation
            grad_x = potential[np.clip(cy, 0, self.frame_height-1), np.clip(cx+1, 0, self.frame_width-1)] - potential[cy, np.clip(cx-1, 0, self.frame_width-1)]
            grad_y = potential[np.clip(cy+1, 0, self.frame_height-1), cx] - potential[np.clip(cy-1, 0, self.frame_height-1), cx]

            gradient = np.array([grad_x, grad_y])
            grad_norm = np.linalg.norm(gradient)

            if grad_norm > 0:
                direction = -gradient / grad_norm # Move against the gradient (towards minimum potential)
            else:
                # If no gradient (e.g., flat area, or stuck), try a small random perturbation
                angle = np.random.uniform(0, 2 * np.pi)
                direction = np.array([np.cos(angle), np.sin(angle)])

            current = current + step_size * direction
            current = np.clip(current, [0, 0], [self.frame_width - 1, self.frame_height - 1])

            # Add to path (only if it's moved sufficiently or at interval)
            if len(path) == 0 or np.linalg.norm(current - path[-1]) > step_size / 2:
                path.append(current.copy())

            # Check if we're stuck in an obstacle - this should ideally be handled by potential field
            if obstacle_map[int(current[1]), int(current[0])] == 1:
                # If in obstacle, backtrack slightly and try new direction
                if len(path) > 1:
                    current = path[-2]
                    path.pop()
                else: # If stuck at start, just stop or try larger perturbation
                    break

        self.path = path
        return path

    def draw_path(self, frame, path=None):
        if path is None:
            path = self.path

        if not path:
            return frame

        # Draw path as a line
        points = np.array([point for point in path], dtype=np.int32)
        cv2.polylines(frame, [points], False, (0, 255, 255), 2)

        # Draw start and end points
        if len(path) > 0:
            cv2.circle(frame, tuple(points[0]), 5, (0, 255, 0), -1)
            cv2.circle(frame, tuple(points[-1]), 5, (0, 0, 255), -1)

        return frame

    def get_direction(self, door_center_x):
        frame_center_x = self.frame_width // 2
        threshold = 50  # Threshold for considering door as centered

        # Determine raw direction
        if door_center_x < frame_center_x - threshold:
            raw_direction = "left"
        elif door_center_x > frame_center_x + threshold:
            raw_direction = "right"
        else:
            raw_direction = "forward"

        # Add to history
        self.direction_history.append(raw_direction)
        if len(self.direction_history) > self.direction_history_max:
            self.direction_history.pop(0)

        # Count occurrences
        left_count = self.direction_history.count("left")
        right_count = self.direction_history.count("right")
        forward_count = self.direction_history.count("forward")

        # Determine smoothed direction
        if left_count > right_count and left_count > forward_count:
            smoothed_direction = "left"
        elif right_count > left_count and right_count > forward_count:
            smoothed_direction = "right"
        else:
            smoothed_direction = "forward"

        # Only update if direction has changed or it's the first time
        if self.last_direction != smoothed_direction or self.last_direction is None:
            self.last_direction = smoothed_direction

        return self.last_direction

    def draw_navigation_arrow(self, frame, door_center_x):
        frame_center_x = self.frame_width // 2
        arrow_length = 50
        arrow_color = (0, 255, 255)
        arrow_thickness = 2

        # Determine direction
        direction = self.get_direction(door_center_x)

        # Draw appropriate arrow
        if direction == "left":
            # Door is to the left
            cv2.arrowedLine(
                frame,
                (frame_center_x, self.frame_height - 30),
                (frame_center_x - arrow_length, self.frame_height - 30),
                arrow_color,
                arrow_thickness
            )
            # Add text
            cv2.putText(
                frame,
                "LEFT",
                (frame_center_x - arrow_length - 40, self.frame_height - 30),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.7,
                arrow_color,
                2
            )
        elif direction == "right":
            # Door is to the right
            cv2.arrowedLine(
                frame,
                (frame_center_x, self.frame_height - 30),
                (frame_center_x + arrow_length, self.frame_height - 30),
                arrow_color,
                arrow_thickness
            )
            # Add text
            cv2.putText(
                frame,
                "RIGHT",
                (frame_center_x + arrow_length + 10, self.frame_height - 30),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.7,
                arrow_color,
                2
            )
        else:
            # Door is centered, draw forward arrow
            cv2.arrowedLine(
                frame,
                (frame_center_x, self.frame_height - 30),
                (frame_center_x, self.frame_height - 30 - arrow_length),
                arrow_color,
                arrow_thickness
            )
            # Add text
            cv2.putText(
                frame,
                "FORWARD",
                (frame_center_x + 10, self.frame_height - 30 - arrow_length),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.7,
                arrow_color,
                2
            )

        return frame

    def check_path_obstacles(self, door_center_x, obstacles):
        if not obstacles:
            return False, None

        # Define path corridor width (relative to door, or just center)
        # For simplicity, let's consider obstacles in the general direction of the door.
        # This is a heuristic, real path planning is more robust.
        corridor_width = self.frame_width // 4 # Example, adjust as needed

        # Get direction to door
        direction = self.get_direction(door_center_x)

        # Check each obstacle
        for obstacle in obstacles:
            obstacle_x = obstacle['center_x']
            obstacle_distance = obstacle['distance']

            # Skip obstacles that are too far
            if obstacle_distance > self.obstacle_warning_threshold:
                continue

            # Check if obstacle is roughly in the path's general area
            # This is a simplification; the path planning (find_path) already accounts for this.
            # This check is more for immediate, close obstacle warnings.
            if direction == "forward" and abs(obstacle_x - self.frame_width // 2) < corridor_width:
                return True, obstacle
            elif direction == "left" and obstacle_x < self.frame_width // 2 and (door_center_x - obstacle_x) > 0: # obstacle is to the right of door, still left
                return True, obstacle
            elif direction == "right" and obstacle_x > self.frame_width // 2 and (obstacle_x - door_center_x) > 0: # obstacle is to the left of door, still right
                return True, obstacle

        return False, None

    def _determine_door_state(self, door_bbox, all_other_objects_in_frame, current_frame):
        # NEW METHOD: Determine if the door is open, closed, or potentially locked.
        x1, y1, x2, y2 = door_bbox
        
        # Define a central region within the door's bounding box
        # This is where we expect to "see through" if the door is open.
        # Adjust these ratios as needed based on your door detections.
        inner_x1 = int(x1 + (x2 - x1) * 0.1)
        inner_y1 = int(y1 + (y2 - y1) * 0.1)
        inner_x2 = int(x2 - (x2 - x1) * 0.1)
        inner_y2 = int(y2 - (y2 - y1) * 0.1)

        # Ensure valid coordinates
        inner_x1 = max(0, inner_x1)
        inner_y1 = max(0, inner_y1)
        inner_x2 = min(self.frame_width, inner_x2)
        inner_y2 = min(self.frame_height, inner_y2)

        if inner_x2 <= inner_x1 or inner_y2 <= inner_y1:
            return "unknown" # Bounding box too small or invalid for inner region

        # Heuristic 1: Check if other objects are detected *inside* the door's opening
        # This is the most reliable visual cue for "open" using existing YOLO model.
        for obj in all_other_objects_in_frame:
            obj_x1, obj_y1, obj_x2, obj_y2 = obj['bbox']
            
            # Check for significant overlap with the inner region of the door
            # Intersection over Union (IoU) could be used here for more robustness
            
            # Simple overlap check: Is the object mostly within the inner door region?
            if (obj_x1 < inner_x2 and obj_x2 > inner_x1 and
                obj_y1 < inner_y2 and obj_y2 > inner_y1):
                # Calculate intersection area
                inter_x1 = max(obj_x1, inner_x1)
                inter_y1 = max(obj_y1, inner_y1)
                inter_x2 = min(obj_x2, inner_x2)
                inter_y2 = min(obj_y2, inner_y2)

                inter_area = max(0, inter_x2 - inter_x1) * max(0, inter_y2 - inter_y1)
                obj_area = (obj_x2 - obj_x1) * (obj_y2 - obj_y1)
                
                # If a significant part of the object is inside the door's inner region
                if obj_area > 0 and (inter_area / obj_area) > 0.3: # e.g., 30% overlap
                    return "open"

        # Heuristic 2 (less reliable but can be combined): Check for visual "busyness" behind the door.
        # If the inner region is visually uniform (low standard deviation), it suggests a closed door.
        # If it's very textured or busy, it might be open or a very textured closed door.
        try:
            inner_region_pixels = current_frame[inner_y1:inner_y2, inner_x1:inner_x2]
            if inner_region_pixels.size == 0: # Avoid error if region is too small
                return "unknown"

            # Convert to grayscale for standard deviation
            gray_inner_region = cv2.cvtColor(inner_region_pixels, cv2.COLOR_BGR2GRAY)
            
            # Calculate standard deviation of pixel intensities
            std_dev = np.std(gray_inner_region)

            # Adjust threshold based on your dataset and testing
            # Low std_dev suggests uniformity (closed), high suggests variance (open or textured surface)
            if std_dev < 20: # This threshold needs careful tuning
                 # Only consider closed if no objects inside AND low variance
                return "closed"
            elif std_dev >= 20 and len(all_other_objects_in_frame) > 0: # If high std dev AND other objects, implies open
                return "open" # This might be redundant with H1, but can act as backup
            else:
                return "unknown" # Could be a textured closed door, or something else. More data needed.

        except Exception as e:
            print(f"Error determining door state from pixels: {e}")
            return "unknown"

        return "closed" # Default to closed if no strong open indicators

    def provide_guidance(self, door_detected, door_distance, door_center_x, obstacles, door_state): # NEW: door_state parameter
        if not self.navigating:
            return

        current_time = time.time()
        if current_time - self.last_guidance_time < self.guidance_interval:
            return

        self.last_guidance_time = current_time

        # Check for obstacles in the path first
        if door_detected:
            path_blocked, blocking_obstacle = self.check_path_obstacles(door_center_x, obstacles)

            if path_blocked and not self.obstacle_announced:
                obstacle_distance = blocking_obstacle['distance']
                obstacle_class = blocking_obstacle['class']
                self.speak(f"Caution! {obstacle_class} in your path, {obstacle_distance:.1f} meters ahead. Stop.", priority=True)
                self.obstacle_announced = True
                self.door_announced = False # Reset door announcement if obstacle is blocking
                self.door_state_announced = False # Reset door state announcement
                return
            elif not path_blocked:
                self.obstacle_announced = False

        if door_detected:
            # Door is detected
            if not self.door_announced:
                self.speak("Door detected.")
                self.door_announced = True
                self.door_state_announced = False # Ensure state is announced after initial detection

            # Announce door state if it changed or hasn't been announced for this door detection cycle
            if not self.door_state_announced or self.door_state != door_state:
                if door_state == "open":
                    self.speak(f"The door is open, {door_distance:.1f} meters ahead.")
                elif door_state == "closed":
                    self.speak(f"The door is closed, {door_distance:.1f} meters ahead.")
                elif door_state == "locked": # Placeholder for future implementation
                    self.speak(f"The door appears to be locked, {door_distance:.1f} meters ahead.")
                else: # "unknown"
                    self.speak(f"Door found, {door_distance:.1f} meters ahead.")
                self.door_state_announced = True
                self.last_guidance_time = current_time # Reset guidance time after state announcement

            # Check distance to door
            if door_distance < self.close_door_threshold:
                if self.door_state == "open":
                    self.speak("You have reached the open door. Proceed carefully.")
                elif self.door_state == "closed":
                    self.speak("You have reached the closed door. Consider opening it.")
                elif self.door_state == "locked":
                    self.speak("You have reached the locked door. It cannot be opened.")
                else:
                    self.speak("You have reached the door. Stop.")
                self.stop_navigation() # Automatically stop when reaching the door
                return

            # Provide directional guidance if not too close
            if door_distance >= self.close_door_threshold:
                direction = self.get_direction(door_center_x)
                if direction == "left":
                    self.speak("Turn left towards the door.")
                elif direction == "right":
                    self.speak("Turn right towards the door.")
                else:
                    self.speak("Move straight ahead towards the door.")

        elif self.door_detected: # Door was detected, now lost
            self.speak("Door lost. Please look around.")
            self.door_announced = False
            self.door_state_announced = False
        else: # No door detected at all
            self.speak("No door detected. Please look around.")
            self.door_announced = False
            self.door_state_announced = False


    def process_frame(self, frame):
        # Estimate depth map
        self.depth_map = self.estimate_depth_map(frame)

        # Create perspective views
        self.left_view, self.right_view = self.create_perspective_views(frame)

        # Perform detection
        results = self.model(frame, conf=self.confidence)

        # Process results
        door_detected = False
        door_distance = None
        door_bbox = None
        door_center_x = None
        all_detections = [] # Collect all detections for door state analysis
        obstacles = []

        # Get detections
        for result in results:
            boxes = result.boxes

            for box in boxes:
                cls = int(box.cls[0])
                cls_name = self.model.names[cls]
                conf = float(box.conf[0])
                x1, y1, x2, y2 = box.xyxy[0].cpu().numpy().astype(int)
                bbox = (x1, y1, x2, y2)

                det_info = {
                    'bbox': bbox,
                    'class': cls_name,
                    'confidence': conf,
                    'center_x': (x1 + x2) // 2,
                    'center_y': (y1 + y2) // 2
                }
                all_detections.append(det_info)

                # Check if it's a door
                if cls_name.lower() in [name.lower() for name in self.door_class_names]:
                    if not door_detected or (x2 - x1) > (door_bbox[2] - door_bbox[0]): # Choose largest door
                        door_detected = True
                        door_bbox = bbox
                        door_distance = self.estimate_distance(x2 - x1)
                        door_center_x = (x1 + x2) // 2

                # Check if it's an obstacle - any object that's not a door
                if cls_name.lower() not in [name.lower() for name in self.door_class_names]:
                    obstacle_width = x2 - x1
                    obstacle_distance = self.estimate_distance(obstacle_width) # Use estimated distance for obstacles too
                    det_info['distance'] = obstacle_distance
                    obstacles.append(det_info)

        # Determine door state after all detections are gathered
        if door_detected:
            # Filter out the door itself from 'all_other_objects_in_frame'
            other_objects_for_state_check = [
                d for d in all_detections
                if d['class'].lower() not in [name.lower() for name in self.door_class_names]
            ]
            self.door_state = self._determine_door_state(door_bbox, other_objects_for_state_check, frame)
        else:
            self.door_state = "unknown" # Reset state if no door is detected

        # Draw detections on frame
        annotated_frame = results[0].plot()

        # Create obstacle map and find path if door detected
        if door_detected and obstacles:
            obstacle_map = self.create_obstacle_map(obstacles)

            # Find path from bottom center to door
            start_point = (self.frame_width // 2, self.frame_height - 10)
            goal_point = (door_center_x, door_bbox[1] + (door_bbox[3] - door_bbox[1]) // 2)

            self.find_path(start_point, goal_point, obstacle_map)

            # Draw path on frame
            annotated_frame = self.draw_path(annotated_frame)

        # Draw depth map (small overlay)
        depth_small = cv2.resize(self.depth_map, (self.frame_width // 4, self.frame_height // 4))
        depth_color = cv2.applyColorMap(depth_small, cv2.COLORMAP_JET)

        # Place depth map in top-right corner
        h, w = depth_color.shape[:2]
        annotated_frame[10:10+h, self.frame_width-10-w:self.frame_width-10] = depth_color

        # Draw distance and state if door detected
        if door_detected and door_distance is not None:
            x1, y1, x2, y2 = door_bbox
            center_x = (x1 + x2) // 2
            center_y = (y1 + y2) // 2

            # Draw distance and door state text
            door_info_text = f"Door: {door_distance:.2f}m ({self.door_state.upper()})" # NEW
            cv2.putText(
                annotated_frame,
                door_info_text,
                (center_x - 70, y1 - 20), # Adjust position
                cv2.FONT_HERSHEY_SIMPLEX,
                0.7,
                (0, 255, 0),
                2
            )

            # Draw navigation arrow
            annotated_frame = self.draw_navigation_arrow(annotated_frame, door_center_x)

        # Draw obstacles
        for obstacle in obstacles:
            x1, y1, x2, y2 = obstacle['bbox']

            # Draw distance text for close obstacles
            if obstacle['distance'] < self.obstacle_warning_threshold:
                cv2.putText(
                    annotated_frame,
                    f"{obstacle['class']}: {obstacle['distance']:.2f}m",
                    (x1, y1 - 10),
                    cv2.FONT_HERSHEY_SIMPLEX,
                    0.5,
                    (0, 0, 255),
                    2
                )

                # Highlight dangerous obstacles
                cv2.rectangle(
                    annotated_frame,
                    (x1, y1),
                    (x2, y2),
                    (0, 0, 255),
                    2
                )

        # Draw navigation status
        status_text = "Navigating" if self.navigating else "Standby"
        cv2.putText(
            annotated_frame,
            status_text,
            (10, 30),
            cv2.FONT_HERSHEY_SIMPLEX,
            0.7,
            (255, 255, 255),
            2
        )

        # Store frames for display
        self.display_frames['main'] = annotated_frame
        self.display_frames['depth'] = cv2.applyColorMap(self.depth_map, cv2.COLORMAP_JET)
        self.display_frames['left'] = self.left_view
        self.display_frames['right'] = self.right_view

        # Create obstacle-only view
        obstacle_view = frame.copy()
        overlay = np.zeros_like(frame, dtype=np.uint8)

        # Draw obstacles and info on obstacle_view
        if not obstacles:
            cv2.putText(
                obstacle_view,
                "NO OBSTACLES DETECTED",
                (self.frame_width // 2 - 150, self.frame_height // 2),
                cv2.FONT_HERSHEY_SIMPLEX,
                1.0,
                (0, 255, 0),
                2
            )
        else:
            for obstacle in obstacles:
                x1, y1, x2, y2 = obstacle['bbox']
                cv2.rectangle(overlay, (x1, y1), (x2, y2), (0, 0, 255), -1) # Red overlay
                cv2.rectangle(obstacle_view, (x1, y1), (x2, y2), (0, 0, 255), 2) # Red outline
                cv2.putText(
                    obstacle_view,
                    f"{obstacle['class']}: {obstacle['distance']:.2f}m",
                    (x1, y1 - 10),
                    cv2.FONT_HERSHEY_SIMPLEX,
                    0.6,
                    (0, 0, 255),
                    2
                )
                # Draw direction arrow from bottom center to obstacle
                center_x = obstacle['center_x']
                center_y = obstacle['center_y']
                start_point_arrow = (self.frame_width // 2, self.frame_height - 30)
                end_point_arrow = (center_x, center_y)
                cv2.arrowedLine(obstacle_view, start_point_arrow, end_point_arrow, (0, 255, 255), 2)
                mid_point_arrow = ((start_point_arrow[0] + end_point_arrow[0]) // 2, (start_point_arrow[1] + end_point_arrow[1]) // 2)
                cv2.putText(
                    obstacle_view,
                    f"{obstacle['distance']:.1f}m",
                    mid_point_arrow,
                    cv2.FONT_HERSHEY_SIMPLEX,
                    0.5,
                    (0, 255, 255),
                    2
                )
            cv2.putText(
                obstacle_view,
                "CAUTION: OBSTACLES DETECTED",
                (self.frame_width // 2 - 200, 80),
                cv2.FONT_HERSHEY_SIMPLEX,
                1.0,
                (0, 0, 255),
                2
            )
        alpha = 0.3
        cv2.addWeighted(overlay, alpha, obstacle_view, 1 - alpha, 0, obstacle_view)
        cv2.putText(
            obstacle_view,
            "OBSTACLE DETECTION",
            (10, 30),
            cv2.FONT_HERSHEY_SIMPLEX,
            0.9,
            (0, 0, 255),
            2
        )
        self.display_frames['obstacles'] = obstacle_view

        # Create door-only view
        door_view = frame.copy()
        overlay_door = np.zeros_like(frame, dtype=np.uint8)

        if door_detected:
            x1, y1, x2, y2 = door_bbox
            cv2.rectangle(overlay_door, (x1, y1), (x2, y2), (0, 255, 0), -1) # Green overlay
            cv2.rectangle(door_view, (x1, y1), (x2, y2), (0, 255, 0), 3) # Green outline
            
            # Draw door state prominently
            door_text = f"Door: {door_distance:.2f}m ({self.door_state.upper()})"
            cv2.putText(
                door_view,
                door_text,
                (x1, y1 - 10),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.9,
                (0, 255, 0),
                2
            )
            start_point_arrow = (self.frame_width // 2, self.frame_height - 30)
            end_point_arrow = (door_center_x, (y1 + y2) // 2)
            cv2.arrowedLine(door_view, start_point_arrow, end_point_arrow, (0, 255, 255), 3)
            direction = self.get_direction(door_center_x)
            cv2.putText(
                door_view,
                f"Go {direction.upper()}",
                (self.frame_width // 2 - 80, self.frame_height - 60),
                cv2.FONT_HERSHEY_SIMPLEX,
                1.0,
                (0, 255, 255),
                2
            )
        else:
            cv2.putText(
                door_view,
                "NO DOOR DETECTED",
                (self.frame_width // 2 - 150, self.frame_height // 2),
                cv2.FONT_HERSHEY_SIMPLEX,
                1.0,
                (0, 0, 255),
                2
            )
        alpha = 0.3
        cv2.addWeighted(overlay_door, alpha, door_view, 1 - alpha, 0, door_view)
        cv2.putText(
            door_view,
            "DOOR DETECTION",
            (10, 30),
            cv2.FONT_HERSHEY_SIMPLEX,
            0.9,
            (0, 255, 0),
            2
        )
        self.display_frames['door'] = door_view

        return annotated_frame, door_detected, door_distance, door_bbox, door_center_x, obstacles

    def _listen_for_commands(self):
        """Listen for voice commands in a loop."""
        if not self.microphone:
            print("Microphone not available. Voice commands disabled.")
            return

        self.listening = True

        while self.listening:
            try:
                with self.microphone as source:
                    print("Listening for commands...")
                    # Adjust timeout and phrase_time_limit for responsiveness
                    audio = self.recognizer.listen(source, timeout=3, phrase_time_limit=5) 

                try:
                    command = self.recognizer.recognize_google(audio).lower()
                    print(f"Recognized: {command}")

                    # Process command
                    if "take me to the door" in command or "find door" in command:
                        self.start_navigation()
                    elif "stop" in command:
                        self.stop_navigation()
                    elif "where is the door" in command:
                        if self.door_detected and self.door_distance is not None:
                            direction = self.get_direction(self.door_center_x)
                            # NEW: Include door state in response
                            self.speak(f"Door is {direction}, {self.door_distance:.1f} meters away. It is {self.door_state}.")
                        else:
                            self.speak("No door detected")
                    elif "what's in front of me" in command or "what is in front of me" in command:
                        if self.obstacles:
                            close_obstacles = [o for o in self.obstacles if o['distance'] < self.obstacle_warning_threshold]
                            if close_obstacles:
                                obstacle_names = [o['class'] for o in close_obstacles[:3]]  # Limit to 3 obstacles
                                self.speak(f"I see {', '.join(obstacle_names)} in front of you")
                            else:
                                self.speak("Path is clear")
                        else:
                            self.speak("Path is clear")

                except sr.UnknownValueError:
                    # Speech was unintelligible, do not print to avoid spamming console
                    pass
                except sr.RequestError as e:
                    print(f"Could not request results from Google Speech Recognition service; {e}")

            except sr.WaitTimeoutError:
                # No speech detected within timeout, continue listening
                pass
            except Exception as e:
                print(f"General listening error: {e}")
                time.sleep(0.1)

    def start_voice_recognition(self):
        """Start voice command recognition."""
        if self.listening:
            return

        self.listening = True
        self.listen_thread = threading.Thread(target=self._listen_for_commands, daemon=True)
        self.listen_thread.start()

        # Announce that the system is ready
        self.speak("Voice commands are now active. Say 'take me to the door' to start navigation.")

    def stop_voice_recognition(self):
        """Stop voice command recognition."""
        self.listening = False
        if self.listen_thread and self.listen_thread.is_alive():
            # Give a small buffer for the thread to naturally exit its listen loop
            time.sleep(0.5)
            # It's generally not recommended to forcibly join a daemon thread if it's indefinitely looping
            # A cleaner exit for the thread itself is preferred, like checking self.listening in its loop.
            # self.listen_thread.join(timeout=1.0)
            self.listen_thread = None
        if self.speaking: # Ensure speech stops immediately
            if self.is_windows and self.tts_engine:
                self.tts_engine.stop()
        self.speak("Voice commands deactivated.", priority=True)


    def process_loop(self):
        """Main processing loop."""
        try:
            self.initialize_camera()

            while self.running:
                # Read frame
                if self.using_sample_images:
                    if self.using_sample_image_paths:
                        frame = cv2.imread(self.sample_images[self.current_sample_index])
                        if frame is None:
                            print(f"Error: Failed to load sample image {self.sample_images[self.current_sample_index]}, skipping.")
                            # Remove problematic image from list to avoid endless loop
                            self.sample_images.pop(self.current_sample_index)
                            if not self.sample_images: # If no images left, create blank
                                blank_image = np.zeros((self.frame_height, self.frame_width, 3), dtype=np.uint8)
                                cv2.putText(blank_image, "No sample images available", (50, self.frame_height // 2),
                                            cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
                                self.sample_images = [blank_image]
                                self.using_sample_image_paths = False
                            else:
                                self.current_sample_index = (self.current_sample_index) % len(self.sample_images)
                            time.sleep(0.1)
                            continue
                    else:
                        frame = self.sample_images[self.current_sample_index]

                    frame = cv2.resize(frame, (self.frame_width, self.frame_height))

                    if self.frame_count % 30 == 0:
                        self.current_sample_index = (self.current_sample_index + 1) % len(self.sample_images)

                    ret = True
                else:
                    ret, frame = self.cap.read()

                    if not ret:
                        print("Error: Failed to capture frame. Attempting to reinitialize camera...")
                        # Try to reinitialize camera if it fails
                        self.cap.release()
                        self.cap = cv2.VideoCapture(self.camera_id)
                        self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.frame_width)
                        self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.frame_height)
                        time.sleep(0.5) # Give camera time to warm up
                        continue

                self.frame_count += 1
                if self.frame_count % self.process_every_n_frames != 0:
                    if cv2.waitKey(1) & 0xFF == ord('q'):
                        break
                    continue

                # Process frame
                _, door_detected, door_distance, door_bbox, door_center_x, obstacles = self.process_frame(frame)

                # Update state
                self.door_detected = door_detected
                self.door_distance = door_distance
                self.door_bbox = door_bbox
                self.door_center_x = door_center_x
                self.obstacles = obstacles # This is already updated in process_frame

                # Provide guidance - pass the new door_state
                if self.navigating:
                    self.provide_guidance(door_detected, door_distance, door_center_x, obstacles, self.door_state)

                # Display frames
                cv2.imshow("Vision Guard - Main", self.display_frames['main'])
                display_h, display_w = 300, 400

                depth_view = cv2.resize(self.display_frames['depth'], (display_w, display_h))
                door_view = cv2.resize(self.display_frames['door'], (display_w, display_h))
                obstacle_view = cv2.resize(self.display_frames['obstacles'], (display_w, display_h)) # NEW: show obstacles view
                stereo_view = np.hstack((self.display_frames['left'], self.display_frames['right']))
                stereo_view = cv2.resize(stereo_view, (display_w*2, display_h))

                # Create a 2-row grid: top row (depth, door), bottom row (obstacles, stereo)
                top_row = np.hstack((depth_view, door_view))
                bottom_row = np.hstack((obstacle_view, cv2.resize(stereo_view, (display_w, display_h)))) # Resize stereo to fit

                grid = np.vstack((top_row, bottom_row)) # NEW: 2x2 grid

                # Add labels for analysis view
                cv2.putText(grid, "Depth Map", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
                cv2.putText(grid, "Door Detection", (display_w + 10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
                cv2.putText(grid, "Obstacle Detection", (10, display_h + 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
                cv2.putText(grid, "Left/Right View", (display_w + 10, display_h + 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)

                cv2.imshow("Vision Guard - Analysis", grid)

                # Check for key press
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    break
                elif key == ord('n'):
                    self.start_navigation()
                elif key == ord('s'):
                    self.stop_navigation()
                elif key == ord('v'):
                    if not self.listening:
                        self.start_voice_recognition()
                    else:
                        self.stop_voice_recognition()

        except Exception as e:
            print(f"Error in processing loop: {e}")
        finally:
            if self.cap is not None:
                self.cap.release()
            cv2.destroyAllWindows()

    def start_navigation(self):
        """Start navigation to the door."""
        if not self.navigating:
            self.navigating = True
            self.door_announced = False
            self.obstacle_announced = False
            self.door_state_announced = False # NEW: Reset door state announcement on start
            self.speak("Starting navigation to the door. Please move slowly.", priority=True)

    def stop_navigation(self):
        """Stop navigation."""
        if self.navigating:
            self.navigating = False
            self.speak("Navigation stopped.", priority=True)

    def start(self):
        """Start the Vision Guard system."""
        if self.running:
            return

        self.running = True

        # Start processing thread
        self.process_thread = threading.Thread(target=self.process_loop)
        self.process_thread.daemon = True
        self.process_thread.start()

        print("Vision Guard started")
        print("Press 'q' to quit, 'n' to start navigation, 's' to stop navigation, 'v' to toggle voice commands")

        # Initial announcement
        self.speak("Vision Guard is ready. Press N to start navigation or say take me to the door.")

    def stop(self):
        """Stop the Vision Guard system."""
        self.running = False
        self.navigating = False

        # Stop voice recognition
        self.stop_voice_recognition()

        # Wait for processing thread to finish
        if self.process_thread:
            self.process_thread.join(timeout=1.0)

        # Clear any remaining messages in the queue
        while not self.message_queue.empty():
            try:
                self.message_queue.get_nowait()
                self.message_queue.task_done()
            except queue.Empty:
                break
        
        # Ensure TTS engine is stopped and disposed if pyttsx3 is used
        if self.is_windows and self.tts_engine:
            self.tts_engine.stop()
            self.tts_engine.runAndWait() # Ensure commands are finished
            # pyttsx3.engine.Engine.endLoop() # This can sometimes cause issues, usually not needed if main loop closes

        print("Vision Guard stopped")


def main():
    """Main function."""
    # Create Vision Guard system
    vision_guard = VisionGuard(
        model_path='runs/train/yolov8_door_detection/weights/best.pt',
        camera_id=0,
        confidence=0.4,
        door_class_names=['door', 'Door', 'hinged', 'knob', 'lever'],
        frame_width=800,  # Increased resolution for better visibility
        frame_height=600
    )

    try:
        # Start system
        vision_guard.start()

        # Keep main thread alive
        while True:
            time.sleep(0.1)

    except KeyboardInterrupt:
        print("Interrupted by user")
    finally:
        # Stop system
        vision_guard.stop()
if __name__ == "__main__":
    main()