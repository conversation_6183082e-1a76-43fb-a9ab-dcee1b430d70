#!/usr/bin/env python3
"""
Enhanced VisionGuard Navigation System for Blind Users
======================================================

Features:
- Advanced door detection (open/closed/unknown)
- Timed voice guidance with 3-second intervals
- Comprehensive UI with control buttons
- Corner case handling and safety features
- Emergency stop and obstacle warnings
- Voice command system with improved feedback

Author: Enhanced by AI Assistant
Version: 2.0
"""

import cv2
import numpy as np
import torch
from ultralytics import YOLO
import pyttsx3
import speech_recognition as sr
import threading
import time
import os
import platform
import tkinter as tk
from tkinter import ttk, messagebox
from PIL import Image, ImageTk
import queue
import logging
from datetime import datetime
from collections import deque
import json
import math

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('visionguard.log'),
        logging.StreamHandler()
    ]
)

class EnhancedVisionGuard:
    """Enhanced VisionGuard Navigation System for Blind Users"""
    
    def __init__(self, model_path="yolov8n.pt", confidence=0.5):
        """Initialize the Enhanced VisionGuard system"""
        
        # Core system initialization
        self.model_path = model_path
        self.confidence = confidence
        self.is_windows = platform.system() == "Windows"
        
        # Load YOLO model
        try:
            self.model = YOLO(model_path)
            logging.info(f"YOLO model loaded successfully: {model_path}")
        except Exception as e:
            logging.error(f"Failed to load YOLO model: {e}")
            raise
        
        # Door detection configuration
        self.door_class_names = ["door", "Door", "DOOR"]
        self.close_door_threshold = 1.5  # meters
        self.obstacle_warning_threshold = 2.0  # meters
        
        # Enhanced door state detection
        self.door_state_history = deque(maxlen=10)
        self.door_state_confidence_threshold = 0.7
        self.reference_images = []
        self.load_reference_images()
        
        # Navigation state
        self.navigating = False
        self.door_detected = False
        self.door_distance = None
        self.door_center_x = None
        self.door_state = "unknown"  # open, closed, unknown
        self.obstacles = []
        
        # Voice guidance system
        self.setup_voice_system()
        self.voice_guidance_interval = 3.0  # seconds
        self.last_voice_time = 0
        self.voice_queue = queue.Queue()
        self.speaking = False
        
        # UI and control system
        self.ui_root = None
        self.video_label = None
        self.control_frame = None
        self.status_var = None
        self.setup_ui()
        
        # Camera system
        self.cap = None
        self.frame_width = 640
        self.frame_height = 480
        self.setup_camera()
        
        # Safety and monitoring
        self.emergency_stop = False
        self.last_door_seen_time = time.time()
        self.door_lost_threshold = 5.0  # seconds
        
        # State tracking for announcements
        self.door_announced = False
        self.obstacle_announced = False
        self.door_state_announced = False
        self.last_spoken_direction = None
        self.last_spoken_distance = None
        
        # Voice command system
        self.listening = False
        self.listen_thread = None
        self.setup_voice_commands()
        
        logging.info("Enhanced VisionGuard system initialized successfully")

    def load_reference_images(self):
        """Load reference images for door state comparison"""
        reference_dir = "Open Door"
        if not os.path.exists(reference_dir):
            logging.warning(f"Reference directory '{reference_dir}' not found")
            return
        
        supported_formats = ('.jpg', '.jpeg', '.png', '.bmp')
        loaded_count = 0
        
        for filename in os.listdir(reference_dir):
            if filename.lower().endswith(supported_formats):
                try:
                    img_path = os.path.join(reference_dir, filename)
                    img = cv2.imread(img_path)
                    if img is not None:
                        # Resize to standard size for comparison
                        img_resized = cv2.resize(img, (200, 200))
                        self.reference_images.append(img_resized)
                        loaded_count += 1
                except Exception as e:
                    logging.warning(f"Failed to load reference image {filename}: {e}")
        
        logging.info(f"Loaded {loaded_count} reference images for door state detection")

    def setup_voice_system(self):
        """Initialize text-to-speech system"""
        try:
            self.tts_engine = pyttsx3.init()
            
            # Configure voice settings
            voices = self.tts_engine.getProperty('voices')
            if voices:
                # Prefer female voice for better clarity
                for voice in voices:
                    if 'female' in voice.name.lower() or 'zira' in voice.name.lower():
                        self.tts_engine.setProperty('voice', voice.id)
                        break
            
            # Set speech rate and volume
            self.tts_engine.setProperty('rate', 180)  # Slightly slower for clarity
            self.tts_engine.setProperty('volume', 0.9)
            
            # Start voice processing thread
            self.voice_thread = threading.Thread(target=self._process_voice_queue, daemon=True)
            self.voice_thread.start()
            
            logging.info("Voice system initialized successfully")
            
        except Exception as e:
            logging.error(f"Failed to initialize voice system: {e}")
            self.tts_engine = None

    def setup_voice_commands(self):
        """Initialize speech recognition for voice commands"""
        try:
            self.recognizer = sr.Recognizer()
            self.microphone = sr.Microphone()
            
            # Adjust for ambient noise
            with self.microphone as source:
                self.recognizer.adjust_for_ambient_noise(source, duration=1)
            
            logging.info("Voice command system initialized successfully")
            
        except Exception as e:
            logging.error(f"Failed to initialize voice commands: {e}")
            self.microphone = None

    def setup_camera(self):
        """Initialize camera system"""
        try:
            self.cap = cv2.VideoCapture(0)
            if not self.cap.isOpened():
                raise Exception("Cannot open camera")
            
            # Set camera properties
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.frame_width)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.frame_height)
            self.cap.set(cv2.CAP_PROP_FPS, 30)
            
            logging.info("Camera system initialized successfully")
            
        except Exception as e:
            logging.error(f"Failed to initialize camera: {e}")
            raise

    def setup_ui(self):
        """Initialize the user interface"""
        try:
            self.ui_root = tk.Tk()
            self.ui_root.title("Enhanced VisionGuard - Navigation for Blind Users")
            self.ui_root.geometry("800x600")
            self.ui_root.configure(bg='#2c3e50')

            # Make window accessible
            self.ui_root.focus_set()

            # Status display
            self.status_var = tk.StringVar()
            self.status_var.set("System Ready - Press Start Navigation to begin")

            status_label = tk.Label(
                self.ui_root,
                textvariable=self.status_var,
                font=('Arial', 14, 'bold'),
                bg='#2c3e50',
                fg='#ecf0f1',
                wraplength=750
            )
            status_label.pack(pady=10)

            # Video display
            self.video_label = tk.Label(self.ui_root, bg='black')
            self.video_label.pack(pady=10)

            # Control buttons frame
            self.control_frame = tk.Frame(self.ui_root, bg='#2c3e50')
            self.control_frame.pack(pady=20)

            # Start Navigation Button
            self.start_btn = tk.Button(
                self.control_frame,
                text="🚀 START NAVIGATION",
                command=self.start_navigation,
                font=('Arial', 16, 'bold'),
                bg='#27ae60',
                fg='white',
                width=20,
                height=2,
                relief='raised',
                bd=3
            )
            self.start_btn.grid(row=0, column=0, padx=10, pady=5)

            # Stop Navigation Button
            self.stop_btn = tk.Button(
                self.control_frame,
                text="🛑 STOP NAVIGATION",
                command=self.stop_navigation,
                font=('Arial', 16, 'bold'),
                bg='#e74c3c',
                fg='white',
                width=20,
                height=2,
                relief='raised',
                bd=3
            )
            self.stop_btn.grid(row=0, column=1, padx=10, pady=5)

            # Voice Commands Toggle
            self.voice_btn = tk.Button(
                self.control_frame,
                text="🎤 VOICE COMMANDS ON",
                command=self.toggle_voice_commands,
                font=('Arial', 16, 'bold'),
                bg='#3498db',
                fg='white',
                width=20,
                height=2,
                relief='raised',
                bd=3
            )
            self.voice_btn.grid(row=1, column=0, padx=10, pady=5)

            # Emergency Stop Button
            self.emergency_btn = tk.Button(
                self.control_frame,
                text="⚠️ EMERGENCY STOP",
                command=self.emergency_stop_action,
                font=('Arial', 16, 'bold'),
                bg='#8e44ad',
                fg='white',
                width=20,
                height=2,
                relief='raised',
                bd=3
            )
            self.emergency_btn.grid(row=1, column=1, padx=10, pady=5)

            # Keyboard shortcuts
            self.ui_root.bind('<KeyPress-space>', lambda e: self.start_navigation())
            self.ui_root.bind('<KeyPress-s>', lambda e: self.stop_navigation())
            self.ui_root.bind('<KeyPress-v>', lambda e: self.toggle_voice_commands())
            self.ui_root.bind('<KeyPress-e>', lambda e: self.emergency_stop_action())
            self.ui_root.bind('<KeyPress-q>', lambda e: self.quit_application())

            # Instructions
            instructions = tk.Label(
                self.ui_root,
                text="Keyboard Shortcuts: SPACE=Start, S=Stop, V=Voice, E=Emergency, Q=Quit",
                font=('Arial', 10),
                bg='#2c3e50',
                fg='#bdc3c7'
            )
            instructions.pack(pady=5)

            logging.info("User interface initialized successfully")

        except Exception as e:
            logging.error(f"Failed to initialize UI: {e}")
            raise

    def _process_voice_queue(self):
        """Process voice messages from queue"""
        while True:
            try:
                if not self.voice_queue.empty():
                    message, priority = self.voice_queue.get()

                    if self.tts_engine:
                        self.speaking = True
                        self.tts_engine.say(message)
                        self.tts_engine.runAndWait()
                        self.speaking = False

                        # Log voice message
                        logging.info(f"Voice: {message}")

                time.sleep(0.1)

            except Exception as e:
                logging.error(f"Voice processing error: {e}")
                time.sleep(1)

    def speak(self, message, priority=False):
        """Add message to voice queue"""
        try:
            if priority:
                # Clear queue for priority messages
                while not self.voice_queue.empty():
                    try:
                        self.voice_queue.get_nowait()
                    except queue.Empty:
                        break

            self.voice_queue.put((message, priority))

            # Update UI status
            if self.status_var:
                self.status_var.set(f"🔊 {message}")

        except Exception as e:
            logging.error(f"Failed to queue voice message: {e}")

    def estimate_distance(self, bbox_width):
        """Estimate distance to object based on bounding box width"""
        # Calibrated for typical door width of 0.8 meters
        # This is a simplified estimation - real implementation would use depth cameras
        if bbox_width > 0:
            # Empirical formula based on camera field of view
            distance = max(0.5, (400 / bbox_width) * 2.0)
            return min(distance, 10.0)  # Cap at 10 meters
        return 10.0

    def get_direction(self, center_x):
        """Determine direction based on object center position"""
        frame_center = self.frame_width // 2
        threshold = 50  # pixels

        if center_x < frame_center - threshold:
            return "left"
        elif center_x > frame_center + threshold:
            return "right"
        else:
            return "straight"

    def analyze_door_state(self, door_region):
        """Enhanced door state analysis using multiple methods"""
        if door_region is None or door_region.size == 0:
            return "unknown", 0.0

        try:
            # Resize door region for consistent analysis
            door_resized = cv2.resize(door_region, (200, 200))

            # Method 1: Edge density analysis
            edges = cv2.Canny(door_resized, 50, 150)
            edge_density = np.sum(edges > 0) / (200 * 200)

            # Method 2: Contour analysis
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            significant_contours = [c for c in contours if cv2.contourArea(c) > 100]

            # Method 3: Depth variation analysis
            gray = cv2.cvtColor(door_resized, cv2.COLOR_BGR2GRAY)
            laplacian = cv2.Laplacian(gray, cv2.CV_64F)
            depth_variance = np.var(laplacian)

            # Method 4: Color pattern analysis
            hsv = cv2.cvtColor(door_resized, cv2.COLOR_BGR2HSV)
            h_var = np.var(hsv[:,:,0])
            s_var = np.var(hsv[:,:,1])
            v_var = np.var(hsv[:,:,2])
            color_variance = (h_var + s_var + v_var) / 3

            # Method 5: Reference image comparison
            ref_similarity = 0.0
            if self.reference_images:
                similarities = []
                for ref_img in self.reference_images:
                    # Template matching
                    result = cv2.matchTemplate(door_resized, ref_img, cv2.TM_CCOEFF_NORMED)
                    similarities.append(np.max(result))
                ref_similarity = np.mean(similarities)

            # Method 6: Opening angle detection
            lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=50)
            angle_score = 0
            if lines is not None:
                angles = []
                for line in lines:
                    rho, theta = line[0]
                    angle = theta * 180 / np.pi
                    if 15 < angle < 75 or 105 < angle < 165:  # Angled lines suggest open door
                        angles.append(angle)
                if angles:
                    angle_score = len(angles) / len(lines)

            # Scoring system
            open_score = 0
            closed_score = 0

            # Edge density scoring (open doors have more complex edges)
            if edge_density > 0.15:
                open_score += 2
            else:
                closed_score += 1

            # Contour scoring
            if len(significant_contours) > 3:
                open_score += 2
            else:
                closed_score += 1

            # Depth variance scoring
            if depth_variance > 25:
                open_score += 2
            else:
                closed_score += 1

            # Color variance scoring
            if color_variance > 30:
                open_score += 2
            else:
                closed_score += 1

            # Reference similarity scoring
            if ref_similarity > 0.3:
                open_score += 3
            else:
                closed_score += 1

            # Angle scoring
            if angle_score > 0.2:
                open_score += 2

            # Determine final state
            total_score = open_score + closed_score
            confidence = abs(open_score - closed_score) / total_score if total_score > 0 else 0

            if open_score > closed_score and confidence > 0.3:
                state = "open"
            elif closed_score > open_score and confidence > 0.3:
                state = "closed"
            else:
                state = "unknown"

            # Add to history for smoothing
            self.door_state_history.append(state)

            # Use majority vote from recent history
            if len(self.door_state_history) >= 3:
                state_counts = {
                    'open': self.door_state_history.count('open'),
                    'closed': self.door_state_history.count('closed'),
                    'unknown': self.door_state_history.count('unknown')
                }
                final_state = max(state_counts, key=state_counts.get)

                # Calculate confidence based on consistency
                max_count = max(state_counts.values())
                final_confidence = max_count / len(self.door_state_history)

                return final_state, final_confidence

            return state, confidence

        except Exception as e:
            logging.error(f"Door state analysis error: {e}")
            return "unknown", 0.0

    def detect_objects_and_doors(self, frame):
        """Detect doors and obstacles in the frame"""
        try:
            results = self.model(frame, conf=self.confidence)

            door_detected = False
            door_distance = None
            door_bbox = None
            door_center_x = None
            door_state = "unknown"
            obstacles = []

            for result in results:
                if result.boxes is None:
                    continue

                boxes = result.boxes
                for box in boxes:
                    cls = int(box.cls[0])
                    cls_name = self.model.names[cls]
                    conf = float(box.conf[0])
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy().astype(int)

                    # Ensure coordinates are within frame bounds
                    x1 = max(0, min(x1, self.frame_width))
                    x2 = max(0, min(x2, self.frame_width))
                    y1 = max(0, min(y1, self.frame_height))
                    y2 = max(0, min(y2, self.frame_height))

                    bbox = (x1, y1, x2, y2)
                    center_x = (x1 + x2) // 2
                    distance = self.estimate_distance(x2 - x1)

                    # Check if it's a door
                    if cls_name.lower() in [name.lower() for name in self.door_class_names]:
                        # If multiple doors, choose the closest/largest one
                        if not door_detected or distance < door_distance:
                            door_detected = True
                            door_bbox = bbox
                            door_distance = distance
                            door_center_x = center_x

                            # Analyze door state
                            door_region = frame[y1:y2, x1:x2]
                            door_state, confidence = self.analyze_door_state(door_region)

                    else:
                        # It's an obstacle
                        obstacle_info = {
                            'class': cls_name,
                            'bbox': bbox,
                            'distance': distance,
                            'confidence': conf,
                            'center_x': center_x
                        }
                        obstacles.append(obstacle_info)

            return door_detected, door_distance, door_bbox, door_center_x, door_state, obstacles

        except Exception as e:
            logging.error(f"Object detection error: {e}")
            return False, None, None, None, "unknown", []

    def provide_navigation_guidance(self, door_detected, door_distance, door_center_x, door_state, obstacles):
        """Provide timed voice guidance for navigation"""
        current_time = time.time()

        # Check if it's time for voice guidance (3-second intervals)
        if current_time - self.last_voice_time < self.voice_guidance_interval:
            return

        if not self.navigating:
            return

        # Emergency stop check
        if self.emergency_stop:
            self.speak("Emergency stop activated. Navigation halted.", priority=True)
            return

        # Handle door detection
        if door_detected:
            self.last_door_seen_time = current_time

            # First time detecting door
            if not self.door_detected:
                self.speak("Door detected. Beginning guidance.", priority=True)
                self.door_detected = True
                self.door_announced = False
                self.door_state_announced = False

            # Check if we've reached the door
            if door_distance < self.close_door_threshold:
                if not self.door_announced:
                    if door_state == "open":
                        self.speak("You have reached the open door. You may proceed through.", priority=True)
                    elif door_state == "closed":
                        self.speak("You have reached the closed door. Please open it or ask for assistance.", priority=True)
                    else:
                        self.speak("You have reached the door. Please check if it's open or closed.", priority=True)

                    self.door_announced = True
                    self.stop_navigation()
                return

            # Provide directional guidance
            direction = self.get_direction(door_center_x)

            # Avoid repeating the same direction
            if direction != self.last_spoken_direction or door_distance != self.last_spoken_distance:

                # Create guidance message
                if direction == "left":
                    guidance = f"Turn left. Door is {door_distance:.1f} meters away"
                elif direction == "right":
                    guidance = f"Turn right. Door is {door_distance:.1f} meters away"
                else:
                    guidance = f"Continue straight. Door is {door_distance:.1f} meters ahead"

                # Add door state information if not announced recently
                if not self.door_state_announced and door_state != "unknown":
                    guidance += f". The door appears to be {door_state}"
                    self.door_state_announced = True

                self.speak(guidance)
                self.last_spoken_direction = direction
                self.last_spoken_distance = door_distance
                self.last_voice_time = current_time

        else:
            # No door detected
            if self.door_detected:
                # Door was lost
                time_since_last_door = current_time - self.last_door_seen_time

                if time_since_last_door > self.door_lost_threshold:
                    self.speak("Door lost. Please turn around slowly to relocate it.", priority=True)
                    self.door_detected = False
                    self.door_announced = False
                    self.door_state_announced = False
                    self.last_spoken_direction = None
                    self.last_voice_time = current_time
            else:
                # Still searching for door
                self.speak("Searching for door. Please turn slowly to scan the area.")
                self.last_voice_time = current_time

        # Handle obstacles
        self.handle_obstacles(obstacles, current_time)

    def handle_obstacles(self, obstacles, current_time):
        """Handle obstacle warnings"""
        if not obstacles:
            self.obstacle_announced = False
            return

        # Find closest obstacles
        close_obstacles = [obs for obs in obstacles if obs['distance'] < self.obstacle_warning_threshold]

        if close_obstacles and not self.obstacle_announced:
            # Sort by distance
            close_obstacles.sort(key=lambda x: x['distance'])
            closest = close_obstacles[0]

            obstacle_direction = self.get_direction(closest['center_x'])
            warning = f"Obstacle detected: {closest['class']} {closest['distance']:.1f} meters {obstacle_direction}"

            if len(close_obstacles) > 1:
                warning += f" and {len(close_obstacles)-1} more obstacles nearby"

            self.speak(warning, priority=True)
            self.obstacle_announced = True
            self.last_voice_time = current_time
        elif not close_obstacles:
            self.obstacle_announced = False

    def process_frame(self, frame):
        """Process a single frame for navigation"""
        try:
            # Detect doors and obstacles
            door_detected, door_distance, door_bbox, door_center_x, door_state, obstacles = self.detect_objects_and_doors(frame)

            # Update instance variables
            self.door_distance = door_distance
            self.door_center_x = door_center_x
            self.door_state = door_state
            self.obstacles = obstacles

            # Provide navigation guidance
            if self.navigating:
                self.provide_navigation_guidance(door_detected, door_distance, door_center_x, door_state, obstacles)

            # Draw visualizations
            annotated_frame = self.draw_annotations(frame, door_detected, door_bbox, door_state, obstacles)

            return annotated_frame

        except Exception as e:
            logging.error(f"Frame processing error: {e}")
            return frame

    def draw_annotations(self, frame, door_detected, door_bbox, door_state, obstacles):
        """Draw visual annotations on the frame"""
        try:
            annotated_frame = frame.copy()

            # Draw door detection
            if door_detected and door_bbox:
                x1, y1, x2, y2 = door_bbox

                # Color based on door state
                if door_state == "open":
                    color = (0, 255, 0)  # Green
                    state_text = "OPEN"
                elif door_state == "closed":
                    color = (0, 0, 255)  # Red
                    state_text = "CLOSED"
                else:
                    color = (0, 255, 255)  # Yellow
                    state_text = "UNKNOWN"

                # Draw bounding box
                cv2.rectangle(annotated_frame, (x1, y1), (x2, y2), color, 3)

                # Draw label
                label = f"DOOR ({state_text})"
                if self.door_distance:
                    label += f" - {self.door_distance:.1f}m"

                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2)[0]
                cv2.rectangle(annotated_frame, (x1, y1-30), (x1+label_size[0], y1), color, -1)
                cv2.putText(annotated_frame, label, (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

            # Draw obstacles
            for obstacle in obstacles:
                if obstacle['distance'] < self.obstacle_warning_threshold:
                    x1, y1, x2, y2 = obstacle['bbox']
                    cv2.rectangle(annotated_frame, (x1, y1), (x2, y2), (255, 0, 0), 2)

                    label = f"{obstacle['class']} - {obstacle['distance']:.1f}m"
                    cv2.putText(annotated_frame, label, (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 2)

            # Draw navigation status
            status_text = "NAVIGATING" if self.navigating else "STANDBY"
            status_color = (0, 255, 0) if self.navigating else (128, 128, 128)
            cv2.putText(annotated_frame, status_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, status_color, 2)

            # Draw center line for direction reference
            center_x = self.frame_width // 2
            cv2.line(annotated_frame, (center_x, 0), (center_x, self.frame_height), (255, 255, 255), 1)

            return annotated_frame

        except Exception as e:
            logging.error(f"Annotation error: {e}")
            return frame

    def start_navigation(self):
        """Start navigation to the door"""
        try:
            if not self.navigating:
                self.navigating = True
                self.emergency_stop = False

                # Reset state
                self.door_announced = False
                self.obstacle_announced = False
                self.door_state_announced = False
                self.last_spoken_direction = None
                self.last_spoken_distance = None
                self.last_voice_time = 0

                # Update UI
                if self.start_btn:
                    self.start_btn.config(state='disabled', bg='#95a5a6')
                if self.stop_btn:
                    self.stop_btn.config(state='normal', bg='#e74c3c')

                self.speak("Navigation started. Please move slowly and listen for guidance.", priority=True)
                logging.info("Navigation started")

        except Exception as e:
            logging.error(f"Failed to start navigation: {e}")

    def stop_navigation(self):
        """Stop navigation"""
        try:
            if self.navigating:
                self.navigating = False

                # Update UI
                if self.start_btn:
                    self.start_btn.config(state='normal', bg='#27ae60')
                if self.stop_btn:
                    self.stop_btn.config(state='disabled', bg='#95a5a6')

                self.speak("Navigation stopped.", priority=True)
                logging.info("Navigation stopped")

        except Exception as e:
            logging.error(f"Failed to stop navigation: {e}")

    def emergency_stop_action(self):
        """Emergency stop action"""
        try:
            self.emergency_stop = True
            self.navigating = False

            # Update UI
            if self.start_btn:
                self.start_btn.config(state='normal', bg='#27ae60')
            if self.stop_btn:
                self.stop_btn.config(state='disabled', bg='#95a5a6')

            self.speak("EMERGENCY STOP ACTIVATED. All navigation halted.", priority=True)
            logging.warning("Emergency stop activated")

        except Exception as e:
            logging.error(f"Emergency stop error: {e}")

    def toggle_voice_commands(self):
        """Toggle voice command recognition"""
        try:
            if not self.listening:
                self.start_voice_recognition()
            else:
                self.stop_voice_recognition()

        except Exception as e:
            logging.error(f"Voice command toggle error: {e}")

    def start_voice_recognition(self):
        """Start voice command recognition"""
        try:
            if self.listening or not self.microphone:
                return

            self.listening = True
            self.listen_thread = threading.Thread(target=self._listen_for_commands, daemon=True)
            self.listen_thread.start()

            # Update UI
            if self.voice_btn:
                self.voice_btn.config(text="🎤 VOICE COMMANDS OFF", bg='#e67e22')

            self.speak("Voice commands activated. Say 'start navigation' to begin.", priority=True)
            logging.info("Voice recognition started")

        except Exception as e:
            logging.error(f"Failed to start voice recognition: {e}")

    def stop_voice_recognition(self):
        """Stop voice command recognition"""
        try:
            if not self.listening:
                return

            self.listening = False

            # Update UI
            if self.voice_btn:
                self.voice_btn.config(text="🎤 VOICE COMMANDS ON", bg='#3498db')

            self.speak("Voice commands deactivated.", priority=True)
            logging.info("Voice recognition stopped")

        except Exception as e:
            logging.error(f"Failed to stop voice recognition: {e}")

    def _listen_for_commands(self):
        """Listen for voice commands in background thread"""
        if not self.microphone:
            logging.warning("Microphone not available for voice commands")
            return

        while self.listening:
            try:
                with self.microphone as source:
                    # Listen for audio with timeout
                    audio = self.recognizer.listen(source, timeout=2, phrase_time_limit=5)

                try:
                    # Recognize speech
                    command = self.recognizer.recognize_google(audio).lower()
                    logging.info(f"Voice command recognized: {command}")

                    # Process commands
                    self._process_voice_command(command)

                except sr.UnknownValueError:
                    # Speech was unintelligible
                    pass
                except sr.RequestError as e:
                    logging.error(f"Speech recognition service error: {e}")

            except sr.WaitTimeoutError:
                # No speech detected, continue listening
                pass
            except Exception as e:
                logging.error(f"Voice listening error: {e}")
                time.sleep(1)

    def _process_voice_command(self, command):
        """Process recognized voice commands"""
        try:
            if "start navigation" in command or "begin navigation" in command or "find door" in command:
                self.start_navigation()

            elif "stop navigation" in command or "stop" in command or "halt" in command:
                self.stop_navigation()

            elif "emergency stop" in command or "emergency" in command:
                self.emergency_stop_action()

            elif "where is the door" in command or "door location" in command:
                if self.door_detected and self.door_distance is not None:
                    direction = self.get_direction(self.door_center_x)
                    state_info = f" It appears to be {self.door_state}" if self.door_state != "unknown" else ""
                    self.speak(f"Door is {direction}, {self.door_distance:.1f} meters away.{state_info}")
                else:
                    self.speak("No door detected. Please look around.")

            elif "what's in front" in command or "obstacles" in command:
                if self.obstacles:
                    close_obstacles = [o for o in self.obstacles if o['distance'] < self.obstacle_warning_threshold]
                    if close_obstacles:
                        obstacle_names = [o['class'] for o in close_obstacles[:3]]
                        self.speak(f"I see {', '.join(obstacle_names)} in front of you")
                    else:
                        self.speak("Path is clear")
                else:
                    self.speak("Path is clear")

            elif "help" in command or "commands" in command:
                help_text = ("Available commands: Start navigation, Stop navigation, Emergency stop, "
                           "Where is the door, What's in front of me, Help")
                self.speak(help_text)

            elif "status" in command:
                status = "navigating" if self.navigating else "standby"
                door_status = f"Door detected {self.door_distance:.1f} meters away" if self.door_detected else "No door detected"
                self.speak(f"System status: {status}. {door_status}")

        except Exception as e:
            logging.error(f"Voice command processing error: {e}")

    def quit_application(self):
        """Safely quit the application"""
        try:
            self.stop_navigation()
            self.stop_voice_recognition()

            if self.cap:
                self.cap.release()

            if self.ui_root:
                self.ui_root.quit()
                self.ui_root.destroy()

            logging.info("Application quit successfully")

        except Exception as e:
            logging.error(f"Error during application quit: {e}")

    def update_video_display(self, frame):
        """Update the video display in the UI"""
        try:
            if self.video_label and frame is not None:
                # Resize frame for display
                display_frame = cv2.resize(frame, (640, 480))

                # Convert BGR to RGB
                rgb_frame = cv2.cvtColor(display_frame, cv2.COLOR_BGR2RGB)

                # Convert to PIL Image
                pil_image = Image.fromarray(rgb_frame)

                # Convert to PhotoImage
                photo = ImageTk.PhotoImage(pil_image)

                # Update label
                self.video_label.config(image=photo)
                self.video_label.image = photo  # Keep a reference

        except Exception as e:
            logging.error(f"Video display update error: {e}")

    def run(self):
        """Main application loop"""
        try:
            logging.info("Starting Enhanced VisionGuard application")

            # Initial voice announcement
            self.speak("Enhanced VisionGuard system ready. Use the interface buttons or voice commands to navigate.", priority=True)

            # Start the main processing loop
            self._main_loop()

        except KeyboardInterrupt:
            logging.info("Application interrupted by user")
        except Exception as e:
            logging.error(f"Application error: {e}")
            messagebox.showerror("Error", f"Application error: {e}")
        finally:
            self.quit_application()

    def _main_loop(self):
        """Main processing loop"""
        try:
            while True:
                # Check if UI is still active
                if not self.ui_root or not self.ui_root.winfo_exists():
                    break

                # Capture frame
                if self.cap and self.cap.isOpened():
                    ret, frame = self.cap.read()
                    if ret:
                        # Process frame
                        processed_frame = self.process_frame(frame)

                        # Update video display
                        self.update_video_display(processed_frame)

                        # Handle corner cases
                        self._handle_corner_cases()
                    else:
                        logging.warning("Failed to capture frame")
                        time.sleep(0.1)
                else:
                    logging.error("Camera not available")
                    break

                # Update UI
                self.ui_root.update()

                # Small delay to prevent excessive CPU usage
                time.sleep(0.03)  # ~30 FPS

        except tk.TclError:
            # UI was closed
            logging.info("UI closed by user")
        except Exception as e:
            logging.error(f"Main loop error: {e}")

    def _handle_corner_cases(self):
        """Handle various corner cases and edge conditions"""
        try:
            current_time = time.time()

            # Case 1: Camera disconnected
            if not self.cap or not self.cap.isOpened():
                if self.navigating:
                    self.speak("Camera disconnected. Stopping navigation for safety.", priority=True)
                    self.stop_navigation()
                return

            # Case 2: Long period without door detection during navigation
            if self.navigating and not self.door_detected:
                time_since_start = current_time - self.last_voice_time
                if time_since_start > 15:  # 15 seconds without finding door
                    self.speak("Unable to locate door. Please ensure you're facing the right direction.", priority=True)
                    self.last_voice_time = current_time

            # Case 3: Multiple obstacles blocking path
            if self.navigating and len(self.obstacles) > 3:
                close_obstacles = [o for o in self.obstacles if o['distance'] < self.obstacle_warning_threshold]
                if len(close_obstacles) > 2:
                    self.speak("Multiple obstacles detected. Please clear the path or ask for assistance.", priority=True)

            # Case 4: Door state changed during navigation
            if self.navigating and self.door_detected and hasattr(self, '_previous_door_state'):
                if self._previous_door_state != self.door_state and self.door_state != "unknown":
                    self.speak(f"Door state changed to {self.door_state}.", priority=True)

            self._previous_door_state = self.door_state if hasattr(self, 'door_state') else "unknown"

            # Case 5: System performance monitoring
            if hasattr(self, '_last_performance_check'):
                if current_time - self._last_performance_check > 30:  # Check every 30 seconds
                    self._check_system_performance()
                    self._last_performance_check = current_time
            else:
                self._last_performance_check = current_time

        except Exception as e:
            logging.error(f"Corner case handling error: {e}")

    def _check_system_performance(self):
        """Monitor system performance and provide warnings"""
        try:
            # Check if voice system is responsive
            if self.voice_queue.qsize() > 10:
                logging.warning("Voice queue is backing up - possible performance issue")

            # Check camera frame rate
            # This is a simplified check - in production you'd want more sophisticated monitoring

            # Log system status
            logging.info(f"System status - Navigating: {self.navigating}, "
                        f"Door detected: {self.door_detected}, "
                        f"Voice commands: {self.listening}, "
                        f"Obstacles: {len(self.obstacles)}")

        except Exception as e:
            logging.error(f"Performance check error: {e}")


def main():
    """Main entry point"""
    try:
        # Create and run the Enhanced VisionGuard system
        vision_guard = EnhancedVisionGuard()
        vision_guard.run()

    except Exception as e:
        logging.error(f"Failed to start application: {e}")
        print(f"Error: {e}")
        print("Please check the log file for more details.")


if __name__ == "__main__":
    main()
