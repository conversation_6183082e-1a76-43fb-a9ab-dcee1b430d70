import cv2
import numpy as np
import time
import threading
import subprocess
import platform
import os
from ultralytics import YOLO
import pyttsx3
import speech_recognition as sr
import queue
import joblib
import json
from pathlib import Path
import math

class VisionGuard:
    def __init__(self,
                 model_path='runs/train/yolov8_door_detection/weights/best.pt',
                 camera_id=0,
                 confidence=0.4,
                 door_class_names=None,
                 obstacle_class_names=None,
                 frame_width=800,  # Increased resolution for better visibility
                 frame_height=600,
                 focal_length=800,
                 known_door_width=0.9):  # meters
        # Initialize dual YOLO models for better detection
        print(f"Loading door detection model from {model_path}...")
        self.door_model = YOLO(model_path)  # Custom door model

        print("Loading general obstacle detection model...")
        self.obstacle_model = YOLO('yolov8n.pt')  # Pre-trained for general objects

        # Set default door class names if not provided
        if door_class_names is None:
            self.door_class_names = ['door', 'Door', 'hinged', 'knob', 'lever']
        else:
            self.door_class_names = door_class_names

        # Set default obstacle class names if not provided
        if obstacle_class_names is None:
            # Include all class names except door-related ones as potential obstacles
            # This ensures we detect any object that's not a door as an obstacle
            self.obstacle_class_names = []
            if hasattr(self.obstacle_model, 'names'):
                for _, name in self.obstacle_model.names.items():  # Use _ for unused index
                    if name.lower() not in [door_name.lower() for door_name in self.door_class_names]:
                        self.obstacle_class_names.append(name)

            # If model doesn't have names or no names were found, use COCO class names
            if not self.obstacle_class_names:
                self.obstacle_class_names = [
                    'person', 'bicycle', 'car', 'motorcycle', 'chair', 'couch',
                    'potted plant', 'bed', 'dining table', 'toilet', 'tv', 'laptop',
                    'mouse', 'remote', 'keyboard', 'cell phone', 'microwave',
                    'oven', 'toaster', 'sink', 'refrigerator', 'book', 'clock',
                    'vase', 'scissors', 'teddy bear', 'hair drier', 'toothbrush',
                    # Add more generic obstacle names
                    'wall', 'furniture', 'object', 'obstacle'
                ]
        else:
            self.obstacle_class_names = obstacle_class_names

        print(f"Door class names: {self.door_class_names}")
        print(f"Obstacle class names: {self.obstacle_class_names}")

        # Initialize camera
        self.camera_id = camera_id
        self.cap = None
        self.frame_width = frame_width
        self.frame_height = frame_height

        # Detection parameters
        self.confidence = confidence

        # Distance estimation parameters
        self.focal_length = focal_length
        self.known_door_width = known_door_width

        # State variables
        self.running = False
        self.navigating = False

        # Load enhanced door classifier
        self.door_classifier = None
        self.load_door_classifier()
        self.door_detected = False
        self.door_distance = None
        self.door_bbox = None
        self.door_center_x = None
        self.obstacles = []

        # Door state detection variables
        self.door_state = "unknown"  # open, closed, unknown
        self.door_state_confidence = 0.0
        self.reference_images = []
        self.door_state_history = []

        # Load reference images for door state detection
        self.load_reference_images()

        # Navigation parameters
        self.last_guidance_time = 0
        self.guidance_interval = 2.0  # seconds
        self.close_door_threshold = 1.0  # meters
        self.obstacle_warning_threshold = 1.5  # meters
        self.door_announced = False
        self.obstacle_announced = False

        # Frame processing rate control
        self.process_every_n_frames = 2  # Process every 2nd frame for better performance
        self.frame_count = 0

        # Processing thread
        self.process_thread = None

        # For depth estimation
        self.depth_map = None

        # For smoother guidance
        self.direction_history = []
        self.direction_history_max = 5
        self.last_direction = None

        # For path planning
        self.path = []
        self.safety_margin = 30  # pixels

        # For perspective views
        self.left_view = None
        self.right_view = None

        # For decision making
        self.decision = None
        self.decision_confidence = 0.0

        # Initialize TTS engine
        self.is_windows = platform.system() == 'Windows'
        if self.is_windows:
            self.tts_engine = pyttsx3.init()
            self.tts_engine.setProperty('rate', 150)
            self.tts_engine.setProperty('volume', 0.8)
        else:
            self.tts_engine = None

        # Initialize speech recognition
        self.recognizer = sr.Recognizer()
        try:
            self.microphone = sr.Microphone()
            with self.microphone as source:
                self.recognizer.adjust_for_ambient_noise(source)
        except Exception as e:
            print(f"Microphone initialization error: {e}")
            print("Voice commands will be disabled. You can still use keyboard controls.")
            print("To enable voice commands, install PyAudio: pip install pyaudio")
            self.microphone = None

        # Message queue for TTS
        self.message_queue = queue.Queue()
        self.speaking = False

        # Start the TTS thread
        self.tts_thread = threading.Thread(target=self._process_tts_queue, daemon=True)
        self.tts_thread.start()

        # Voice command thread
        self.listening = False
        self.listen_thread = None

        # Display frames
        self.display_frames = {}

    def load_reference_images(self):
        """Load reference images for door state detection"""
        reference_dir = "Open Door"

        if not os.path.exists(reference_dir):
            print(f"Warning: Reference directory '{reference_dir}' not found")
            return

        try:
            image_files = [f for f in os.listdir(reference_dir)
                          if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))]

            for img_file in image_files:
                img_path = os.path.join(reference_dir, img_file)
                try:
                    img = cv2.imread(img_path)
                    if img is not None:
                        # Resize to standard size for comparison
                        img_resized = cv2.resize(img, (224, 224))
                        self.reference_images.append(img_resized)
                except Exception as e:
                    print(f"Warning: Failed to load reference image {img_path}: {e}")

            print(f"Loaded {len(self.reference_images)} reference images for door state detection")

        except Exception as e:
            print(f"Error loading reference images: {e}")

    def load_door_classifier(self):
        """
        Load the trained enhanced door classifier
        """
        try:
            model_path = Path("door_models/best_door_classifier.pkl")
            if model_path.exists():
                self.door_classifier = joblib.load(model_path)
                print("Enhanced door classifier loaded successfully")

                # Load metadata
                metadata_path = Path("door_models/model_metadata.json")
                if metadata_path.exists():
                    with open(metadata_path, 'r') as f:
                        metadata = json.load(f)
                    print(f"Model type: {metadata.get('model_type', 'Unknown')}")

                return True
            else:
                print("Enhanced door classifier not found, using fallback method")
                return False
        except Exception as e:
            print(f"Error loading door classifier: {e}")
            return False

    def extract_comprehensive_features(self, image):
        """
        Extract comprehensive features from door image (same as in enhanced_door_classifier.py)
        """
        if image is None:
            return np.zeros(512)  # Feature dimension

        # Resize image
        img_resized = cv2.resize(image, (224, 224))
        gray = cv2.cvtColor(img_resized, cv2.COLOR_BGR2GRAY)

        features = []

        # 1. Histogram features (64 features)
        hist = cv2.calcHist([gray], [0], None, [64], [0, 256])
        hist = hist.flatten() / (224 * 224)  # Normalize
        features.extend(hist)

        # 2. Edge density features (16 features)
        edges = cv2.Canny(gray, 50, 150)

        # Edge density in different regions
        h, w = edges.shape
        regions = [
            edges[:h//2, :w//2],      # Top-left
            edges[:h//2, w//2:],      # Top-right
            edges[h//2:, :w//2],      # Bottom-left
            edges[h//2:, w//2:],      # Bottom-right
            edges[:, w//4:3*w//4],    # Center vertical
            edges[h//4:3*h//4, :],    # Center horizontal
            edges[h//4:3*h//4, w//4:3*w//4],  # Center
            edges[:h//4, :],          # Top strip
            edges[3*h//4:, :],        # Bottom strip
            edges[:, :w//4],          # Left strip
            edges[:, 3*w//4:],        # Right strip
        ]

        for region in regions:
            if region.size > 0:
                edge_density = np.sum(region > 0) / region.size
                features.append(edge_density)

        # Pad to 16 features if needed
        while len(features) < 64 + 16:
            features.append(0.0)

        # Add remaining features (simplified version for integration)
        # Texture features (32), geometric features (32), color features (64), statistical features (64)
        # For brevity, adding simplified versions

        # Simple texture features
        for i in range(32):
            features.append(np.std(gray) / 255.0)

        # Simple geometric features
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if contours:
            largest_contour = max(contours, key=cv2.contourArea)
            area = cv2.contourArea(largest_contour)
            features.append(area / (224 * 224))
        else:
            features.append(0.0)

        # Pad remaining features
        while len(features) < 512:
            features.append(0.0)

        return np.array(features[:512], dtype=np.float32)

    def detect_door_state(self, door_region, yolo_detections=None):
        """
        Enhanced door state detection with specific rules:
        1. If 2 knobs detected = door is open
        2. If similar to open door reference images = door is open
        3. Use trained machine learning model
        4. Fallback to reference comparison
        """
        if door_region is None or door_region.size == 0:
            return "unknown", 0.0

        try:
            # Rule 1: Check for two knobs = open door
            if yolo_detections is not None:
                knob_count = self._count_knobs_in_detections(yolo_detections)
                if knob_count >= 2:
                    print(f"Two knobs detected ({knob_count}) - Door is OPEN")
                    return "open", 0.95

            # Rule 2: Check similarity with open door reference images
            similarity_score = self._check_open_door_similarity(door_region)
            if similarity_score > 0.75:  # High similarity threshold
                print(f"High similarity to open door images ({similarity_score:.3f}) - Door is OPEN")
                return "open", similarity_score

            # Rule 3: Use trained model if available
            if self.door_classifier is not None:
                # Extract features using the same method as training
                features = self.extract_comprehensive_features(door_region)
                features = features.reshape(1, -1)

                # Predict using trained model
                prediction = self.door_classifier.predict(features)[0]

                # Get confidence if available
                if hasattr(self.door_classifier, 'predict_proba'):
                    probabilities = self.door_classifier.predict_proba(features)[0]
                    model_confidence = max(probabilities)
                else:
                    model_confidence = 0.90  # High confidence for trained model

                door_state = "open" if prediction == 1 else "closed"

                # Boost confidence if model says open AND similarity is decent
                if door_state == "open" and similarity_score > 0.5:
                    final_confidence = min(model_confidence + (similarity_score * 0.1), 0.98)
                else:
                    final_confidence = model_confidence

                return door_state, final_confidence

            else:
                # Rule 4: Fallback to reference image comparison
                if similarity_score > 0.6:
                    return "open", similarity_score
                elif similarity_score < 0.3:
                    return "closed", 1.0 - similarity_score
                else:
                    return "unknown", 0.5

        except Exception as e:
            print(f"Door state detection error: {e}")
            return "unknown", 0.0

    def _count_knobs_in_detections(self, detections):
        """
        Count number of knobs/levers in YOLO detections
        """
        knob_count = 0

        for detection in detections:
            try:
                # Handle different detection formats
                if hasattr(detection, 'cls'):
                    class_id = int(detection.cls)
                    # Check if it's a knob or lever (class IDs 2 and 3 based on data.yaml)
                    if class_id in [2, 3]:  # knob=2, lever=3
                        knob_count += 1
                elif isinstance(detection, dict):
                    if 'class' in detection and detection['class'].lower() in ['knob', 'lever']:
                        knob_count += 1
                    elif 'cls' in detection and int(detection['cls']) in [2, 3]:
                        knob_count += 1
            except Exception as e:
                print(f"Error processing detection: {e}")
                continue

        return knob_count

    def _check_open_door_similarity(self, image):
        """
        Check similarity with open door reference images using histogram comparison
        """
        try:
            if not self.reference_images:
                return 0.0

            # Resize input image for comparison
            image_resized = cv2.resize(image, (224, 224))
            image_gray = cv2.cvtColor(image_resized, cv2.COLOR_BGR2GRAY)
            image_hsv = cv2.cvtColor(image_resized, cv2.COLOR_BGR2HSV)

            # Calculate histograms for input image
            hist_gray = cv2.calcHist([image_gray], [0], None, [64], [0, 256])
            hist_hue = cv2.calcHist([image_hsv], [0], None, [32], [0, 180])
            hist_sat = cv2.calcHist([image_hsv], [1], None, [32], [0, 256])

            cv2.normalize(hist_gray, hist_gray)
            cv2.normalize(hist_hue, hist_hue)
            cv2.normalize(hist_sat, hist_sat)

            similarities = []

            # Compare with reference images (sample up to 30 for performance)
            sample_size = min(30, len(self.reference_images))
            step = max(1, len(self.reference_images) // sample_size)

            for i in range(0, len(self.reference_images), step):
                ref_img = self.reference_images[i]

                # Convert reference image
                ref_gray = cv2.cvtColor(ref_img, cv2.COLOR_BGR2GRAY)
                ref_hsv = cv2.cvtColor(ref_img, cv2.COLOR_BGR2HSV)

                # Calculate reference histograms
                ref_hist_gray = cv2.calcHist([ref_gray], [0], None, [64], [0, 256])
                ref_hist_hue = cv2.calcHist([ref_hsv], [0], None, [32], [0, 180])
                ref_hist_sat = cv2.calcHist([ref_hsv], [1], None, [32], [0, 256])

                cv2.normalize(ref_hist_gray, ref_hist_gray)
                cv2.normalize(ref_hist_hue, ref_hist_hue)
                cv2.normalize(ref_hist_sat, ref_hist_sat)

                # Calculate correlations
                corr_gray = cv2.compareHist(hist_gray, ref_hist_gray, cv2.HISTCMP_CORREL)
                corr_hue = cv2.compareHist(hist_hue, ref_hist_hue, cv2.HISTCMP_CORREL)
                corr_sat = cv2.compareHist(hist_sat, ref_hist_sat, cv2.HISTCMP_CORREL)

                # Weighted combination (gray is most important for door structure)
                combined_similarity = (corr_gray * 0.5 + corr_hue * 0.3 + corr_sat * 0.2)
                similarities.append(max(0, combined_similarity))  # Ensure non-negative

            if similarities:
                # Use the maximum similarity (best match)
                max_similarity = max(similarities)
                # Also consider average of top 3 matches for robustness
                top_similarities = sorted(similarities, reverse=True)[:3]
                avg_top_similarity = sum(top_similarities) / len(top_similarities)

                # Final similarity score (weighted combination)
                final_similarity = (max_similarity * 0.7 + avg_top_similarity * 0.3)
                return final_similarity

            return 0.0

        except Exception as e:
            print(f"Error in similarity check: {e}")
            return 0.0

    def _enhanced_reference_comparison(self, door_image):
        """Enhanced comparison with reference open door images using multiple metrics"""
        if not self.reference_images:
            return 0.0

        try:
            # Convert to different color spaces for robust comparison
            door_gray = cv2.cvtColor(door_image, cv2.COLOR_BGR2GRAY)
            door_hsv = cv2.cvtColor(door_image, cv2.COLOR_BGR2HSV)

            # Calculate multiple histograms
            hist_gray = cv2.calcHist([door_gray], [0], None, [64], [0, 256])
            hist_hue = cv2.calcHist([door_hsv], [0], None, [32], [0, 180])
            hist_sat = cv2.calcHist([door_hsv], [1], None, [32], [0, 256])

            # Normalize histograms
            cv2.normalize(hist_gray, hist_gray)
            cv2.normalize(hist_hue, hist_hue)
            cv2.normalize(hist_sat, hist_sat)

            similarities = []

            # Compare with more reference images for better accuracy
            sample_size = min(50, len(self.reference_images))  # Use up to 50 references
            step = max(1, len(self.reference_images) // sample_size)

            for i in range(0, len(self.reference_images), step):
                ref_img = self.reference_images[i]

                # Convert reference image
                ref_gray = cv2.cvtColor(ref_img, cv2.COLOR_BGR2GRAY)
                ref_hsv = cv2.cvtColor(ref_img, cv2.COLOR_BGR2HSV)

                # Calculate reference histograms
                ref_hist_gray = cv2.calcHist([ref_gray], [0], None, [64], [0, 256])
                ref_hist_hue = cv2.calcHist([ref_hsv], [0], None, [32], [0, 180])
                ref_hist_sat = cv2.calcHist([ref_hsv], [1], None, [32], [0, 256])

                # Normalize reference histograms
                cv2.normalize(ref_hist_gray, ref_hist_gray)
                cv2.normalize(ref_hist_hue, ref_hist_hue)
                cv2.normalize(ref_hist_sat, ref_hist_sat)

                # Calculate correlations
                corr_gray = cv2.compareHist(hist_gray, ref_hist_gray, cv2.HISTCMP_CORREL)
                corr_hue = cv2.compareHist(hist_hue, ref_hist_hue, cv2.HISTCMP_CORREL)
                corr_sat = cv2.compareHist(hist_sat, ref_hist_sat, cv2.HISTCMP_CORREL)

                # Weighted combination of correlations
                combined_similarity = (
                    corr_gray * 0.5 +      # Grayscale structure
                    corr_hue * 0.3 +       # Color hue
                    corr_sat * 0.2         # Color saturation
                )

                similarities.append(max(0, combined_similarity))  # Ensure non-negative

            if not similarities:
                return 0.0

            # Use top 10 similarities for robust estimation
            similarities.sort(reverse=True)
            top_similarities = similarities[:min(10, len(similarities))]

            # Calculate weighted average (give more weight to highest similarities)
            weights = np.exp(np.linspace(1, 0, len(top_similarities)))  # Exponential decay
            weighted_avg = np.average(top_similarities, weights=weights)

            # Apply sigmoid transformation for better discrimination
            # This enhances the difference between good and poor matches
            enhanced_score = 1 / (1 + np.exp(-10 * (weighted_avg - 0.5)))

            return min(enhanced_score, 1.0)

        except Exception as e:
            print(f"Enhanced reference comparison error: {e}")
            return 0.0

    def _analyze_door_frame_alignment(self, door_region):
        """Analyze if door is aligned with door frame (closed door indicator)"""
        try:
            gray = cv2.cvtColor(door_region, cv2.COLOR_BGR2GRAY)

            # Apply edge detection
            edges = cv2.Canny(gray, 50, 150)

            # Find vertical lines (door edges)
            lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=50,
                                   minLineLength=30, maxLineGap=10)

            if lines is None:
                return 0.0

            vertical_lines = []
            for line in lines:
                x1, y1, x2, y2 = line[0]
                angle = np.abs(np.arctan2(y2 - y1, x2 - x1) * 180 / np.pi)

                # Check if line is vertical (door edge)
                if angle > 80 and angle < 100:
                    vertical_lines.append(line[0])

            # If we have strong vertical lines, likely a closed door
            alignment_score = min(len(vertical_lines) / 4.0, 1.0)

            return alignment_score

        except Exception as e:
            print(f"Door frame alignment analysis error: {e}")
            return 0.0

    def _analyze_door_edges(self, door_region):
        """Analyze edge patterns to determine door state"""
        try:
            gray = cv2.cvtColor(door_region, cv2.COLOR_BGR2GRAY)

            # Apply Gaussian blur
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)

            # Edge detection
            edges = cv2.Canny(blurred, 30, 100)

            # Calculate edge density
            edge_density = np.sum(edges > 0) / edges.size

            # Open doors typically have more complex edge patterns
            # Normalize edge density (higher = more likely open)
            edge_score = min(edge_density * 10, 1.0)

            return edge_score

        except Exception as e:
            print(f"Edge analysis error: {e}")
            return 0.0

    def _analyze_depth_discontinuity(self, door_region):
        """Analyze depth discontinuities that indicate open doors"""
        try:
            gray = cv2.cvtColor(door_region, cv2.COLOR_BGR2GRAY)

            # Apply Gaussian blur to reduce noise
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)

            # Calculate gradients
            grad_x = cv2.Sobel(blurred, cv2.CV_64F, 1, 0, ksize=3)
            grad_y = cv2.Sobel(blurred, cv2.CV_64F, 0, 1, ksize=3)

            # Calculate gradient magnitude
            gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)

            # Normalize
            gradient_magnitude = gradient_magnitude / np.max(gradient_magnitude) if np.max(gradient_magnitude) > 0 else gradient_magnitude

            # Calculate depth discontinuity score
            # Open doors have more depth variations
            depth_score = np.mean(gradient_magnitude)

            # Enhance score for open door detection
            return min(depth_score * 2.0, 1.0)

        except Exception as e:
            print(f"Depth discontinuity analysis error: {e}")
            return 0.0

    def _analyze_texture_complexity(self, door_region):
        """Analyze texture complexity - open doors typically have more complex textures"""
        try:
            gray = cv2.cvtColor(door_region, cv2.COLOR_BGR2GRAY)

            # Calculate Local Binary Pattern for texture analysis
            # Simplified LBP calculation
            h, w = gray.shape
            lbp_score = 0

            for i in range(1, h-1):
                for j in range(1, w-1):
                    center = gray[i, j]
                    pattern = 0

                    # 8-neighbor LBP
                    neighbors = [
                        gray[i-1, j-1], gray[i-1, j], gray[i-1, j+1],
                        gray[i, j+1], gray[i+1, j+1], gray[i+1, j],
                        gray[i+1, j-1], gray[i, j-1]
                    ]

                    for k, neighbor in enumerate(neighbors):
                        if neighbor >= center:
                            pattern += 2**k

                    lbp_score += pattern

            # Normalize by image size
            texture_complexity = lbp_score / ((h-2) * (w-2) * 255)

            return min(texture_complexity, 1.0)

        except Exception as e:
            print(f"Texture complexity analysis error: {e}")
            return 0.0

    def _detect_opening_patterns(self, door_region):
        """Detect patterns that indicate door openings"""
        try:
            gray = cv2.cvtColor(door_region, cv2.COLOR_BGR2GRAY)

            # Apply edge detection
            edges = cv2.Canny(gray, 30, 100)

            # Look for L-shaped patterns (door frame + opening)
            # Use morphological operations to detect opening patterns
            kernel_horizontal = cv2.getStructuringElement(cv2.MORPH_RECT, (15, 3))
            kernel_vertical = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 15))

            horizontal_lines = cv2.morphologyEx(edges, cv2.MORPH_OPEN, kernel_horizontal)
            vertical_lines = cv2.morphologyEx(edges, cv2.MORPH_OPEN, kernel_vertical)

            # Combine to find L-patterns
            combined = cv2.addWeighted(horizontal_lines, 0.5, vertical_lines, 0.5, 0)

            # Calculate opening pattern score
            pattern_density = np.sum(combined > 0) / combined.size

            return min(pattern_density * 5.0, 1.0)  # Amplify for better discrimination

        except Exception as e:
            print(f"Opening pattern detection error: {e}")
            return 0.0

    def _analyze_color_distribution(self, door_region):
        """Analyze color distribution differences between open and closed doors"""
        try:
            # Convert to HSV for better color analysis
            hsv = cv2.cvtColor(door_region, cv2.COLOR_BGR2HSV)

            # Calculate color variance
            h_var = np.var(hsv[:, :, 0])  # Hue variance
            s_var = np.var(hsv[:, :, 1])  # Saturation variance
            v_var = np.var(hsv[:, :, 2])  # Value variance

            # Open doors typically have more color variation
            color_variance = (h_var + s_var + v_var) / 3.0

            # Normalize and return score
            normalized_score = color_variance / 10000.0  # Normalize to 0-1 range

            return min(normalized_score, 1.0)

        except Exception as e:
            print(f"Color distribution analysis error: {e}")
            return 0.0

    def _process_tts_queue(self):
        """Process messages in the TTS queue."""
        while True:
            try:
                message = self.message_queue.get(timeout=0.1)
                self.speaking = True
                if self.is_windows and self.tts_engine:
                    self.tts_engine.say(message)
                    self.tts_engine.runAndWait()
                else:
                    # Use subprocess for non-Windows platforms
                    subprocess.Popen(['espeak', '-s', '150', '-a', '200', message])
                    time.sleep(len(message) * 0.1)  # Approximate time to speak
                self.speaking = False
                self.message_queue.task_done()
            except queue.Empty:
                time.sleep(0.1)
            except Exception as e:
                print(f"TTS error: {e}")
                self.speaking = False

    def speak(self, text, priority=False):
        print(f"Speaking: {text}")

        if priority:
            # Clear the queue
            while not self.message_queue.empty():
                try:
                    self.message_queue.get_nowait()
                    self.message_queue.task_done()
                except queue.Empty:
                    break

            # Stop current speech
            if self.is_windows and self.tts_engine:
                self.tts_engine.stop()

        self.message_queue.put(text)

    def initialize_camera(self):
        """Initialize the camera or use a sample image if camera is not available."""
        if self.cap is not None and self.cap.isOpened():
            self.cap.release()
        # Try to open the camera
        try:
            self.cap = cv2.VideoCapture(self.camera_id)
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.frame_width)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.frame_height)

            # Try to read a frame to confirm camera is working
            ret, _ = self.cap.read()
            if not ret:
                raise RuntimeError("Camera opened but failed to read frame")

        except Exception as e:
            print(f"Camera error: {e}")
            if self.cap is not None:
                self.cap.release()
                self.cap = None

        # Check if camera opened successfully
        if self.cap is None or not self.cap.isOpened():
            print(f"Warning: Could not open camera {self.camera_id}")
            print("Using sample images instead...")

            # Create a list of sample images to simulate a video feed
            self.sample_images = []

            # Try to find sample images in the dataset
            sample_paths = [
                "Door_Detection_Research_Project/images/results",
                "RaspberryPi_Door_Assistant/images/results",
                "train/images",
                "valid/images",
                "test/images"
            ]
            # Look for images in each path
            for path in sample_paths:
                if os.path.exists(path):
                    image_files = [os.path.join(path, f) for f in os.listdir(path) if f.endswith(('.jpg', '.jpeg', '.png'))]
                    if image_files:
                        print(f"Found {len(image_files)} sample images in {path}")
                        self.sample_images.extend(image_files[:20])  # Limit to 20 images per folder
                        if len(self.sample_images) >= 5:  # If we have at least 5 images, that's enough
                            break

            # If no images found, create a blank image
            if not self.sample_images:
                print("No sample images found. Creating a blank image.")
                blank_image = np.zeros((self.frame_height, self.frame_width, 3), dtype=np.uint8)
                cv2.putText(blank_image, "No camera available", (50, self.frame_height // 2),
                            cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
                self.sample_images = [blank_image]
                self.current_sample_index = 0
                self.using_sample_images = True
                self.using_sample_image_paths = False
            else:
                self.current_sample_index = 0
                self.using_sample_images = True
                self.using_sample_image_paths = True

            print(f"Using {len(self.sample_images)} sample images for simulation")
        else:
            # Get actual camera properties
            self.frame_width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            self.frame_height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            self.using_sample_images = False
            print(f"Camera initialized with resolution: {self.frame_width}x{self.frame_height}")

    def estimate_distance(self, bbox_width):
        if bbox_width == 0:
            return float('inf')

        distance = (self.known_door_width * self.focal_length) / bbox_width
        return distance

    def estimate_depth_map(self, frame):
        # Convert to grayscale
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

        # Apply Sobel filter to get gradients
        sobelx = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
        sobely = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)

        # Calculate gradient magnitude
        magnitude = np.sqrt(sobelx**2 + sobely**2)

        # Normalize to 0-255
        magnitude = cv2.normalize(magnitude, None, 0, 255, cv2.NORM_MINMAX)

        # Invert (stronger edges are closer)
        depth_map = 255 - magnitude.astype(np.uint8)

        # Apply Gaussian blur to smooth the depth map
        depth_map = cv2.GaussianBlur(depth_map, (15, 15), 0)

        return depth_map

    def create_perspective_views(self, frame):
        # Get frame dimensions
        height, width = frame.shape[:2]

        # Split the frame into left and right halves
        mid = width // 2
        left_view = frame[:, :mid].copy()
        right_view = frame[:, mid:].copy()

        # Add a vertical line to show the split
        cv2.line(left_view, (mid-1, 0), (mid-1, height), (0, 255, 255), 2)
        cv2.line(right_view, (0, 0), (0, height), (0, 255, 255), 2)

        # Add labels to the views
        cv2.putText(left_view, "LEFT VIEW", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
        cv2.putText(right_view, "RIGHT VIEW", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)

        return left_view, right_view

    def create_obstacle_map(self, obstacles):
        obstacle_map = np.zeros((self.frame_height, self.frame_width), dtype=np.uint8)

        # Mark obstacles on the map
        for obstacle in obstacles:
            x1, y1, x2, y2 = obstacle['bbox']
            # Add safety margin around obstacles
            x1 = max(0, x1 - self.safety_margin)
            y1 = max(0, y1 - self.safety_margin)
            x2 = min(self.frame_width, x2 + self.safety_margin)
            y2 = min(self.frame_height, y2 + self.safety_margin)

            obstacle_map[y1:y2, x1:x2] = 1

        return obstacle_map

    def find_path(self, start_point, goal_point, obstacle_map):
        from scipy.ndimage import distance_transform_edt

        # Create distance transform from obstacles
        # This gives each pixel its distance to the nearest obstacle
        dist_transform = distance_transform_edt(1 - obstacle_map)

        # Create attractive potential field (goal)
        y, x = np.indices((self.frame_height, self.frame_width))
        goal_x, goal_y = goal_point
        attractive = np.sqrt((x - goal_x)**2 + (y - goal_y)**2)

        # Combine potential fields
        # Higher values of dist_transform mean safer areas
        # Lower values of attractive mean closer to goal
        # We want to maximize safety while minimizing distance to goal
        potential = attractive - 5.0 * dist_transform

        # Find path using gradient descent
        path = []
        current = np.array(start_point)
        path.append(current.copy())

        max_iterations = 100  # Reduced for performance
        step_size = 5
        goal_threshold = 20

        for _ in range(max_iterations):
            # Check if we're close enough to the goal
            if np.linalg.norm(current - np.array(goal_point)) < goal_threshold:
                break

            # Get current position (rounded to integers)
            cx, cy = np.round(current).astype(int)
            cx = np.clip(cx, 0, self.frame_width - 1)
            cy = np.clip(cy, 0, self.frame_height - 1)

            # Sample potential field in neighborhood
            window_size = 5
            x_min = max(0, cx - window_size)
            x_max = min(self.frame_width, cx + window_size + 1)
            y_min = max(0, cy - window_size)
            y_max = min(self.frame_height, cy + window_size + 1)

            window = potential[y_min:y_max, x_min:x_max]
            min_idx = np.unravel_index(np.argmin(window), window.shape)

            # Move towards minimum potential
            next_y, next_x = min_idx
            next_point = np.array([x_min + next_x, y_min + next_y])

            # Ensure we're not stuck at the same point
            if np.array_equal(next_point, np.round(current)):
                # If stuck, take a random step
                angle = np.random.uniform(0, 2 * np.pi)
                next_point = current + step_size * np.array([np.cos(angle), np.sin(angle)])
                next_point = np.clip(next_point, [0, 0], [self.frame_width - 1, self.frame_height - 1])

            # Update current position
            direction = next_point - current
            direction_norm = np.linalg.norm(direction)
            if direction_norm > 0:
                direction = direction / direction_norm

            current = current + step_size * direction
            current = np.clip(current, [0, 0], [self.frame_width - 1, self.frame_height - 1])

            # Add to path
            path.append(current.copy())

            # Check if we're stuck in an obstacle
            cx, cy = np.round(current).astype(int)
            if obstacle_map[cy, cx] == 1:
                # If in obstacle, backtrack and try again
                if len(path) > 1:
                    current = path[-2]
                    path.pop()

        self.path = path
        return path

    def draw_path(self, frame, path=None):
        if path is None:
            path = self.path

        if not path:
            return frame

        # Draw path as a line
        points = np.array([point for point in path], dtype=np.int32)
        cv2.polylines(frame, [points], False, (0, 255, 255), 2)

        # Draw start and end points
        if len(path) > 0:
            cv2.circle(frame, tuple(points[0]), 5, (0, 255, 0), -1)
            cv2.circle(frame, tuple(points[-1]), 5, (0, 0, 255), -1)

        return frame

    def get_direction(self, door_center_x):
        frame_center_x = self.frame_width // 2
        threshold = 50  # Threshold for considering door as centered

        # Determine raw direction
        if door_center_x < frame_center_x - threshold:
            raw_direction = "left"
        elif door_center_x > frame_center_x + threshold:
            raw_direction = "right"
        else:
            raw_direction = "forward"

        # Add to history
        self.direction_history.append(raw_direction)
        if len(self.direction_history) > self.direction_history_max:
            self.direction_history.pop(0)

        # Count occurrences
        left_count = self.direction_history.count("left")
        right_count = self.direction_history.count("right")
        forward_count = self.direction_history.count("forward")

        # Determine smoothed direction
        if left_count > right_count and left_count > forward_count:
            smoothed_direction = "left"
        elif right_count > left_count and right_count > forward_count:
            smoothed_direction = "right"
        else:
            smoothed_direction = "forward"

        # Only update if direction has changed or it's the first time
        if self.last_direction != smoothed_direction or self.last_direction is None:
            self.last_direction = smoothed_direction

        return self.last_direction

    def draw_navigation_arrow(self, frame, door_center_x):
        frame_center_x = self.frame_width // 2
        arrow_length = 50
        arrow_color = (0, 255, 255)
        arrow_thickness = 2

        # Determine direction
        direction = self.get_direction(door_center_x)

        # Draw appropriate arrow
        if direction == "left":
            # Door is to the left
            cv2.arrowedLine(
                frame,
                (frame_center_x, self.frame_height - 30),
                (frame_center_x - arrow_length, self.frame_height - 30),
                arrow_color,
                arrow_thickness
            )
            # Add text
            cv2.putText(
                frame,
                "LEFT",
                (frame_center_x - arrow_length - 40, self.frame_height - 30),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.7,
                arrow_color,
                2
            )
        elif direction == "right":
            # Door is to the right
            cv2.arrowedLine(
                frame,
                (frame_center_x, self.frame_height - 30),
                (frame_center_x + arrow_length, self.frame_height - 30),
                arrow_color,
                arrow_thickness
            )
            # Add text
            cv2.putText(
                frame,
                "RIGHT",
                (frame_center_x + arrow_length + 10, self.frame_height - 30),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.7,
                arrow_color,
                2
            )
        else:
            # Door is centered, draw forward arrow
            cv2.arrowedLine(
                frame,
                (frame_center_x, self.frame_height - 30),
                (frame_center_x, self.frame_height - 30 - arrow_length),
                arrow_color,
                arrow_thickness
            )
            # Add text
            cv2.putText(
                frame,
                "FORWARD",
                (frame_center_x + 10, self.frame_height - 30 - arrow_length),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.7,
                arrow_color,
                2
            )

        return frame

    def check_path_obstacles(self, door_center_x, obstacles):
        if not obstacles:
            return False, None

        # Define path corridor width
        corridor_width = self.frame_width // 4

        # Get direction to door
        direction = self.get_direction(door_center_x)

        # Check each obstacle
        for obstacle in obstacles:
            obstacle_x = obstacle['center_x']
            obstacle_distance = obstacle['distance']

            # Skip obstacles that are too far
            if obstacle_distance > self.obstacle_warning_threshold:
                continue

            # Check if obstacle is in the path
            if direction == "forward":
                # For forward direction, check if obstacle is in the center corridor
                if abs(obstacle_x - self.frame_width // 2) < corridor_width:
                    return True, obstacle
            elif direction == "left":
                # For left direction, check if obstacle is in the left corridor
                if obstacle_x < self.frame_width // 2 and abs(obstacle_x - door_center_x) < corridor_width:
                    return True, obstacle
            elif direction == "right":
                # For right direction, check if obstacle is in the right corridor
                if obstacle_x > self.frame_width // 2 and abs(obstacle_x - door_center_x) < corridor_width:
                    return True, obstacle

        return False, None

    def provide_guidance(self, door_detected, door_distance, door_center_x, obstacles):
        if not self.navigating:
            return

        current_time = time.time()
        if current_time - self.last_guidance_time < self.guidance_interval:
            return

        self.last_guidance_time = current_time

        # Check for obstacles in the path first
        if door_detected:
            path_blocked, blocking_obstacle = self.check_path_obstacles(door_center_x, obstacles)

            if path_blocked and not self.obstacle_announced:
                # Announce obstacle
                obstacle_distance = blocking_obstacle['distance']
                obstacle_class = blocking_obstacle['class']
                self.speak(f"Caution! {obstacle_class} in your path, {obstacle_distance:.1f} meters ahead. Stop.", priority=True)
                self.obstacle_announced = True
                return
            elif not path_blocked:
                self.obstacle_announced = False

        if door_detected:
            # Door is detected
            if not self.door_detected:
                # First time detecting the door
                self.speak("Door detected")

            # Check distance to door
            if door_distance < self.close_door_threshold:
                if not self.door_announced:
                    # Include door state in arrival announcement
                    if self.door_state == "open":
                        self.speak("You have reached the open door. You may proceed through.", priority=True)
                    elif self.door_state == "closed":
                        self.speak("You have reached the closed door. Please open it to proceed.", priority=True)
                    else:
                        self.speak("You have reached the door. Please check if it's open before proceeding.", priority=True)
                    self.door_announced = True
                return

            # Provide directional guidance with door state
            direction = self.get_direction(door_center_x)

            # Create guidance message with door state
            if self.door_state == "open":
                state_info = "Open door"
            elif self.door_state == "closed":
                state_info = "Closed door"
            else:
                state_info = "Door"

            if direction == "left":
                self.speak(f"{state_info} is to your left, turn left")
            elif direction == "right":
                self.speak(f"{state_info} is to your right, turn right")
            else:
                self.speak(f"{state_info} is straight ahead, {door_distance:.1f} meters away")

        elif self.door_detected:
            # Door was detected before but not now
            self.speak("Door lost. Please look around.")
            self.door_announced = False
        else:
            # No door detected
            self.speak("No door detecAVted. Please look around.")

    def process_frame(self, frame):
        # Estimate depth map
        self.depth_map = self.estimate_depth_map(frame)

        # Create perspective views
        self.left_view, self.right_view = self.create_perspective_views(frame)

        # Perform dual detection - doors and obstacles
        door_results = self.door_model(frame, conf=self.confidence)
        obstacle_results = self.obstacle_model(frame, conf=self.confidence)

        # Process results
        door_detected = False
        door_distance = None
        door_bbox = None
        door_center_x = None
        obstacles = []

        # Collect all detections first
        all_detections = []

        # Process door detections
        for result in door_results:
            boxes = result.boxes
            if boxes is not None:
                for box in boxes:
                    # Get class and confidence
                    cls = int(box.cls[0])
                    cls_name = self.door_model.names[cls]
                    conf = float(box.conf[0])

                    # Get bounding box
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy().astype(int)
                    bbox = (x1, y1, x2, y2)

                    # Store detection info
                    detection_info = {
                        'cls': cls,
                        'class': cls_name,
                        'conf': conf,
                        'bbox': bbox
                    }
                    all_detections.append(detection_info)

                    # Check if it's a door
                    if cls_name.lower() in [name.lower() for name in self.door_class_names]:
                        # If multiple doors, choose the closest/largest one
                        if not door_detected or (x2 - x1) > (door_bbox[2] - door_bbox[0]):
                            door_detected = True
                            door_bbox = bbox
                            door_distance = self.estimate_distance(x2 - x1)
                            door_center_x = (x1 + x2) // 2

                            # Extract door region for state detection
                            door_region = frame[y1:y2, x1:x2]
                            # Pass all detections to door state detection for knob counting
                            self.door_state, self.door_state_confidence = self.detect_door_state(door_region, all_detections)

                            # Add to history for stability
                            self.door_state_history.append(self.door_state)
                            if len(self.door_state_history) > 5:
                                self.door_state_history.pop(0)

                            # Use most common state from recent history
                            if len(self.door_state_history) >= 3:
                                from collections import Counter
                                most_common = Counter(self.door_state_history).most_common(1)[0][0]
                                self.door_state = most_common

        # Process obstacle detections
        for result in obstacle_results:
            boxes = result.boxes
            if boxes is not None:
                for box in boxes:
                    # Get class and confidence
                    cls = int(box.cls[0])
                    cls_name = self.obstacle_model.names[cls]
                    conf = float(box.conf[0])

                    # Get bounding box
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy().astype(int)
                    bbox = (x1, y1, x2, y2)

                    # Check if it's a valid obstacle class
                    if cls_name.lower() in [name.lower() for name in self.obstacle_class_names]:
                        # Calculate distance to obstacle
                        obstacle_width = x2 - x1
                        obstacle_distance = self.estimate_distance(obstacle_width)

                        # Store obstacle information
                        obstacle_info = {
                            'bbox': bbox,
                            'class': cls_name,
                            'confidence': conf,
                            'distance': obstacle_distance,
                            'center_x': (x1 + x2) // 2,
                            'center_y': (y1 + y2) // 2
                        }
                        obstacles.append(obstacle_info)
                        print(f"OBSTACLE DETECTED: {cls_name} at {obstacle_distance:.1f}m (conf: {conf:.2f})")

        # Debug: Print detection summary
        print(f"Frame processed: {len(all_detections)} total detections, {len(obstacles)} obstacles")
        if obstacles:
            obstacle_classes = [obs['class'] for obs in obstacles]
            print(f"Obstacles found: {obstacle_classes}")

        # Draw detections on frame (combine both models)
        annotated_frame = frame.copy()
        if door_results[0].boxes is not None:
            annotated_frame = door_results[0].plot(img=annotated_frame)
        if obstacle_results[0].boxes is not None:
            annotated_frame = obstacle_results[0].plot(img=annotated_frame)

        # Create obstacle map and find path if door detected
        if door_detected and obstacles:
            obstacle_map = self.create_obstacle_map(obstacles)

            # Find path from bottom center to door
            start_point = (self.frame_width // 2, self.frame_height - 10)
            goal_point = (door_center_x, door_bbox[1] + (door_bbox[3] - door_bbox[1]) // 2)

            self.find_path(start_point, goal_point, obstacle_map)

            # Draw path on frame
            annotated_frame = self.draw_path(annotated_frame)

        # Draw depth map (small overlay)
        depth_small = cv2.resize(self.depth_map, (self.frame_width // 4, self.frame_height // 4))
        depth_color = cv2.applyColorMap(depth_small, cv2.COLORMAP_JET)

        # Place depth map in top-right corner
        h, w = depth_color.shape[:2]
        annotated_frame[10:10+h, self.frame_width-10-w:self.frame_width-10] = depth_color

        # Draw distance if door detected
        if door_detected and door_distance is not None:
            x1, y1, x2, y2 = door_bbox
            center_x = (x1 + x2) // 2
            center_y = (y1 + y2) // 2

            # Draw distance text
            cv2.putText(
                annotated_frame,
                f"{door_distance:.2f}m",
                (center_x, center_y),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.7,
                (0, 255, 0),
                2
            )

            # Draw door state
            state_color = (0, 255, 0) if self.door_state == "open" else (0, 0, 255) if self.door_state == "closed" else (0, 255, 255)
            state_text = f"DOOR: {self.door_state.upper()}"
            if self.door_state_confidence > 0:
                state_text += f" ({self.door_state_confidence:.1f})"

            cv2.putText(
                annotated_frame,
                state_text,
                (center_x - 50, center_y - 30),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.6,
                state_color,
                2
            )

            # Draw navigation arrow
            annotated_frame = self.draw_navigation_arrow(annotated_frame, door_center_x)

        # Draw obstacles
        for obstacle in obstacles:
            x1, y1, x2, y2 = obstacle['bbox']

            # Draw distance text for close obstacles
            if obstacle['distance'] < self.obstacle_warning_threshold:
                cv2.putText(
                    annotated_frame,
                    f"{obstacle['class']}: {obstacle['distance']:.2f}m",
                    (x1, y1 - 10),
                    cv2.FONT_HERSHEY_SIMPLEX,
                    0.5,
                    (0, 0, 255),
                    2
                )

                # Highlight dangerous obstacles
                cv2.rectangle(
                    annotated_frame,
                    (x1, y1),
                    (x2, y2),
                    (0, 0, 255),
                    2
                )

        # Draw navigation status
        status_text = "Navigating" if self.navigating else "Standby"
        cv2.putText(
            annotated_frame,
            status_text,
            (10, 30),
            cv2.FONT_HERSHEY_SIMPLEX,
            0.7,
            (255, 255, 255),
            2
        )

        # Store frames for display
        self.display_frames['main'] = annotated_frame
        self.display_frames['depth'] = cv2.applyColorMap(self.depth_map, cv2.COLORMAP_JET)
        self.display_frames['left'] = self.left_view
        self.display_frames['right'] = self.right_view

        # Create obstacle-only view - use a copy of the frame for better context
        obstacle_view = frame.copy()

        # Add a semi-transparent overlay to make obstacles stand out
        overlay = np.zeros_like(frame)

        for obstacle in obstacles:
            x1, y1, x2, y2 = obstacle['bbox']

            # Draw filled rectangle on overlay
            cv2.rectangle(overlay, (x1, y1), (x2, y2), (0, 0, 255), -1)

            # Draw rectangle on main view
            cv2.rectangle(obstacle_view, (x1, y1), (x2, y2), (0, 0, 255), 2)

            # Draw distance and class
            cv2.putText(
                obstacle_view,
                f"{obstacle['class']}: {obstacle['distance']:.2f}m",
                (x1, y1 - 10),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.7,
                (0, 0, 255),
                2
            )

            # Draw direction arrow from bottom center to obstacle
            center_x = (x1 + x2) // 2
            center_y = (y1 + y2) // 2
            start_point = (self.frame_width // 2, self.frame_height - 30)
            end_point = (center_x, center_y)

            # Draw arrow
            cv2.arrowedLine(obstacle_view, start_point, end_point, (0, 255, 255), 2)

            # Add distance text along the arrow
            mid_point = ((start_point[0] + end_point[0]) // 2, (start_point[1] + end_point[1]) // 2)
            cv2.putText(
                obstacle_view,
                f"{obstacle['distance']:.1f}m",
                mid_point,
                cv2.FONT_HERSHEY_SIMPLEX,
                0.6,
                (0, 255, 255),
                2
            )

            # Determine direction (left, right, center)
            direction = self.get_direction(center_x)

            # Add direction text
            cv2.putText(
                obstacle_view,
                f"Go {direction.upper()}",
                (self.frame_width // 2 - 80, self.frame_height - 60),
                cv2.FONT_HERSHEY_SIMPLEX,
                1.0,
                (0, 255, 255),
                2
            )

        # If still no obstacles, show a message
        if not obstacles:
            cv2.putText(
                obstacle_view,
                "NO OBSTACLES DETECTED",
                (self.frame_width // 2 - 150, self.frame_height // 2),
                cv2.FONT_HERSHEY_SIMPLEX,
                1.0,
                (0, 255, 0),
                2
            )
        else:
            # Make the obstacle warning more prominent
            cv2.putText(
                obstacle_view,
                "CAUTION: OBSTACLES DETECTED",
                (self.frame_width // 2 - 200, 80),
                cv2.FONT_HERSHEY_SIMPLEX,
                1.0,
                (0, 0, 255),
                2
            )

        # Blend overlay with main view
        alpha = 0.3  # Transparency factor
        cv2.addWeighted(overlay, alpha, obstacle_view, 1 - alpha, 0, obstacle_view)

        # Add title
        cv2.putText(
            obstacle_view,
            "OBSTACLE DETECTION",
            (10, 30),
            cv2.FONT_HERSHEY_SIMPLEX,
            0.9,
            (0, 0, 255),
            2
        )

        self.display_frames['obstacles'] = obstacle_view

        # Create door-only view - use a copy of the frame for better context
        door_view = frame.copy()

        # Add a semi-transparent overlay
        overlay = np.zeros_like(frame)

        if door_detected:
            x1, y1, x2, y2 = door_bbox

            # Draw filled rectangle on overlay
            cv2.rectangle(overlay, (x1, y1), (x2, y2), (0, 255, 0), -1)

            # Draw rectangle on main view
            cv2.rectangle(door_view, (x1, y1), (x2, y2), (0, 255, 0), 3)

            # Draw distance
            cv2.putText(
                door_view,
                f"Door: {door_distance:.2f}m",
                (x1, y1 - 10),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.9,
                (0, 255, 0),
                2
            )

            # Draw direction arrow from bottom center to door
            start_point = (self.frame_width // 2, self.frame_height - 30)
            end_point = (door_center_x, (y1 + y2) // 2)

            # Draw arrow
            cv2.arrowedLine(door_view, start_point, end_point, (0, 255, 255), 3)

            # Add direction text
            direction = self.get_direction(door_center_x)
            cv2.putText(
                door_view,
                f"Go {direction.upper()}",
                (self.frame_width // 2 - 80, self.frame_height - 60),
                cv2.FONT_HERSHEY_SIMPLEX,
                1.0,
                (0, 255, 255),
                2
            )
        else:
            # No door detected
            cv2.putText(
                door_view,
                "NO DOOR DETECTED",
                (self.frame_width // 2 - 150, self.frame_height // 2),
                cv2.FONT_HERSHEY_SIMPLEX,
                1.0,
                (0, 0, 255),
                2
            )

        # Blend overlay with main view
        alpha = 0.3  # Transparency factor
        cv2.addWeighted(overlay, alpha, door_view, 1 - alpha, 0, door_view)

        # Add title
        cv2.putText(
            door_view,
            "DOOR DETECTION",
            (10, 30),
            cv2.FONT_HERSHEY_SIMPLEX,
            0.9,
            (0, 255, 0),
            2
        )

        self.display_frames['door'] = door_view

        return annotated_frame, door_detected, door_distance, door_bbox, door_center_x, obstacles

    def _listen_for_commands(self):
        """Listen for voice commands in a loop."""
        if not self.microphone:
            print("Microphone not available. Voice commands disabled.")
            return

        self.listening = True

        while self.listening:
            try:
                with self.microphone as source:
                    print("Listening for commands...")
                    audio = self.recognizer.listen(source, timeout=1, phrase_time_limit=5)

                try:
                    command = self.recognizer.recognize_google(audio).lower()
                    print(f"Recognized: {command}")

                    # Process command
                    if "take me to the door" in command or "find door" in command:
                        self.start_navigation()
                    elif "stop" in command:
                        self.stop_navigation()
                    elif "where is the door" in command:
                        if self.door_detected and self.door_distance is not None:
                            direction = self.get_direction(self.door_center_x)
                            state_info = f"The door is {self.door_state}" if self.door_state != "unknown" else "Door state unknown"
                            self.speak(f"Door is {direction}, {self.door_distance:.1f} meters away. {state_info}")
                        else:
                            self.speak("No door detected")
                    elif "what's in front of me" in command or "what is in front of me" in command:
                        if self.obstacles:
                            close_obstacles = [o for o in self.obstacles if o['distance'] < self.obstacle_warning_threshold]
                            if close_obstacles:
                                obstacle_names = [o['class'] for o in close_obstacles[:3]]  # Limit to 3 obstacles
                                self.speak(f"I see {', '.join(obstacle_names)} in front of you")
                            else:
                                self.speak("Path is clear")
                        else:
                            self.speak("Path is clear")

                except sr.UnknownValueError:
                    # Speech was unintelligible
                    pass
                except sr.RequestError as e:
                    print(f"Could not request results; {e}")

            except Exception as e:
                print(f"Listening error: {e}")
                time.sleep(0.1)

    def start_voice_recognition(self):
        """Start voice command recognition."""
        if self.listening:
            return

        self.listen_thread = threading.Thread(target=self._listen_for_commands, daemon=True)
        self.listen_thread.start()

        # Announce that the system is ready
        self.speak("Voice commands are now active. Say 'take me to the door' to start navigation.")

    def stop_voice_recognition(self):
        """Stop voice command recognition."""
        self.listening = False
        if self.listen_thread:
            self.listen_thread.join(timeout=1.0)
            self.listen_thread = None

    def process_loop(self):
        """Main processing loop."""
        try:
            self.initialize_camera()

            while self.running:
                # Read frame
                if self.using_sample_images:
                    # Use sample images instead of camera
                    if self.using_sample_image_paths:
                        # Load image from file
                        frame = cv2.imread(self.sample_images[self.current_sample_index])
                        if frame is None:
                            print(f"Error: Failed to load sample image {self.sample_images[self.current_sample_index]}")
                            time.sleep(0.1)
                            continue
                    else:
                        # Use pre-loaded image
                        frame = self.sample_images[self.current_sample_index]

                    # Resize to match desired frame size
                    frame = cv2.resize(frame, (self.frame_width, self.frame_height))

                    # Move to next sample image every 30 frames
                    if self.frame_count % 30 == 0:
                        self.current_sample_index = (self.current_sample_index + 1) % len(self.sample_images)

                    ret = True
                else:
                    # Use camera
                    ret, frame = self.cap.read()

                    if not ret:
                        print("Error: Failed to capture frame")
                        time.sleep(0.1)
                        continue

                # Process only every n-th frame for better performance
                self.frame_count += 1
                if self.frame_count % self.process_every_n_frames != 0:
                    # Skip processing but don't show camera view
                    if cv2.waitKey(1) & 0xFF == ord('q'):
                        break
                    continue

                # Process frame
                _, door_detected, door_distance, door_bbox, door_center_x, obstacles = self.process_frame(frame)

                # Update state
                self.door_detected = door_detected
                self.door_distance = door_distance
                self.door_bbox = door_bbox
                self.door_center_x = door_center_x
                self.obstacles = obstacles

                # Provide guidance
                if self.navigating:
                    self.provide_guidance(door_detected, door_distance, door_center_x, obstacles)

                # Display frames
                # Main view
                cv2.imshow("Vision Guard - Main", self.display_frames['main'])
                display_h, display_w = 300, 400  # Larger size for better visibility

                # Create a combined left-right view (full width)
                stereo_view = np.hstack((self.display_frames['left'], self.display_frames['right']))

                # Resize all views to the same size
                depth_view = cv2.resize(self.display_frames['depth'], (display_w, display_h))
                door_view = cv2.resize(self.display_frames['door'], (display_w, display_h))

                # Add obstacle detection view
                obstacle_view = cv2.resize(self.display_frames['obstacles'], (display_w, display_h))

                # Create a 3-column top row: depth, door detection, obstacle detection
                top_row = np.hstack((depth_view, door_view, obstacle_view))

                # Resize stereo view to match the width of the top row (3 * display_w)
                stereo_view = cv2.resize(stereo_view, (display_w*3, display_h))

                # Create a 2-row grid: top row (3 views) and stereo view
                grid = np.vstack((top_row, stereo_view))

                # Add labels with larger font and better visibility
                cv2.putText(grid, "Depth Map", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 255, 255), 2)
                cv2.putText(grid, "Door Detection", (display_w + 10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 255, 255), 2)
                cv2.putText(grid, "Obstacle Detection", (display_w*2 + 10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 255, 255), 2)

                cv2.imshow("Vision Guard - Analysis", grid)

                # Check for key press
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    break
                elif key == ord('n'):
                    self.start_navigation()
                elif key == ord('s'):
                    self.stop_navigation()
                elif key == ord('v'):
                    if not self.listening:
                        self.start_voice_recognition()
                    else:
                        self.stop_voice_recognition()

        except Exception as e:
            print(f"Error in processing loop: {e}")
        finally:
            if self.cap is not None:
                self.cap.release()
            cv2.destroyAllWindows()

    def start_navigation(self):
        """Start navigation to the door."""
        if not self.navigating:
            self.navigating = True
            self.door_announced = False
            self.obstacle_announced = False
            self.speak("Starting navigation to the door. Please move slowly.", priority=True)

    def stop_navigation(self):
        """Stop navigation."""
        if self.navigating:
            self.navigating = False
            self.speak("Navigation stopped.", priority=True)

    def start(self):
        """Start the Vision Guard system."""
        if self.running:
            return

        self.running = True

        # Start processing thread
        self.process_thread = threading.Thread(target=self.process_loop)
        self.process_thread.daemon = True
        self.process_thread.start()

        print("Vision Guard started")
        print("Press 'q' to quit, 'n' to start navigation, 's' to stop navigation, 'v' to toggle voice commands")

        # Initial announcement
        self.speak("Vision Guard is ready. Press N to start navigation or say take me to the door.")

    def stop(self):
        """Stop the Vision Guard system."""
        self.running = False
        self.navigating = False

        # Stop voice recognition
        self.stop_voice_recognition()

        # Wait for processing thread to finish
        if self.process_thread:
            self.process_thread.join(timeout=1.0)

        print("Vision Guard stopped")


def main():
    """Main function."""
    # Create Vision Guard system
    vision_guard = VisionGuard(
        model_path='runs/train/yolov8_door_detection/weights/best.pt',
        camera_id=0,
        confidence=0.4,
        door_class_names=['door', 'Door', 'hinged', 'knob', 'lever'],
        frame_width=800,  # Increased resolution for better visibility
        frame_height=600
    )

    try:
        # Start system
        vision_guard.start()

        # Keep main thread alive
        while True:
            time.sleep(0.1)

    except KeyboardInterrupt:
        print("Interrupted by user")
    finally:
        # Stop system
        vision_guard.stop()
if __name__ == "__main__":
    main()