import os
import sys
from ultralytics import YOLO
import pkg_resources

def check_ultralytics_version():
    """Check the installed version of ultralytics"""
    try:
        version = pkg_resources.get_distribution("ultralytics").version
        print(f"Installed ultralytics version: {version}")
    except pkg_resources.DistributionNotFound:
        print("Ultralytics package not found")

def list_available_models():
    """List all available YOLO models from Ultralytics"""
    print("\nAttempting to list available YOLO models...")
    
    # Try to list models using different version naming conventions
    model_prefixes = ["yolov8", "yolov9", "yolov10", "yolo11"]
    model_sizes = ["n", "s", "m", "l", "x"]
    model_types = ["", "-seg", "-cls", "-pose", "-obb"]
    
    available_models = []
    
    for prefix in model_prefixes:
        for size in model_sizes:
            for model_type in model_types:
                model_name = f"{prefix}{size}{model_type}.pt"
                try:
                    # Just try to initialize the model to see if it's available
                    # We'll catch the exception if it's not
                    model = YOLO(model_name)
                    available_models.append(model_name)
                    print(f"✓ {model_name} is available")
                except Exception as e:
                    error_message = str(e).lower()
                    if "not found" in error_message or "no such file" in error_message:
                        print(f"✗ {model_name} is not available")
                    else:
                        print(f"? {model_name} error: {e}")
    
    print("\nAvailable models:")
    for model in available_models:
        print(f"- {model}")

def main():
    """Main function to check YOLO versions"""
    print("Checking YOLO versions and available models...")
    check_ultralytics_version()
    list_available_models()

if __name__ == "__main__":
    main()
