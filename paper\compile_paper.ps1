# PowerShell script to compile the Vision Guard IEEE paper

Write-Host "Compiling Vision Guard IEEE Research Paper..." -ForegroundColor Green
Write-Host ""

# Check if we're in the right directory
if (-not (Test-Path ".\latex\vision_guard_paper.tex")) {
    if (Test-Path ".\paper\latex\vision_guard_paper.tex") {
        Set-Location .\paper
        Write-Host "Changed directory to paper folder" -ForegroundColor Yellow
    } else {
        Write-Host "Error: Could not find vision_guard_paper.tex. Please run this script from the project root or paper directory." -ForegroundColor Red
        exit 1
    }
}

# Change to the latex directory
Set-Location .\latex
Write-Host "Changed directory to latex folder" -ForegroundColor Yellow
Write-Host ""

# Check if pdflatex is available
try {
    $pdflatexVersion = pdflatex --version
    Write-Host "Found pdflatex:" -ForegroundColor Green
    Write-Host $pdflatexVersion[0]
} catch {
    Write-Host "Error: pdflatex not found. Please install a LaTeX distribution like MiKTeX or TeX Live." -ForegroundColor Red
    Write-Host "Visit: https://miktex.org/download or https://tug.org/texlive/windows.html" -ForegroundColor Yellow
    exit 1
}

# Check if bibtex is available
try {
    $bibtexVersion = bibtex --version
    Write-Host "Found bibtex:" -ForegroundColor Green
    Write-Host $bibtexVersion[0]
} catch {
    Write-Host "Error: bibtex not found. Please install a LaTeX distribution like MiKTeX or TeX Live." -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "Step 1/5: Compiling cover page..." -ForegroundColor Cyan
pdflatex cover_page.tex

Write-Host ""
Write-Host "Step 2/5: Running pdflatex first pass..." -ForegroundColor Cyan
pdflatex vision_guard_paper.tex

Write-Host ""
Write-Host "Step 3/5: Running bibtex..." -ForegroundColor Cyan
bibtex vision_guard_paper

Write-Host ""
Write-Host "Step 4/5: Running pdflatex second pass..." -ForegroundColor Cyan
pdflatex vision_guard_paper.tex

Write-Host ""
Write-Host "Step 5/5: Running pdflatex final pass..." -ForegroundColor Cyan
pdflatex vision_guard_paper.tex

Write-Host ""
if (Test-Path "vision_guard_paper.pdf") {
    Write-Host "Compilation successful! The PDF has been created." -ForegroundColor Green
    
    # Create a combined PDF with cover page
    Write-Host "Creating combined PDF with cover page..." -ForegroundColor Cyan
    
    # Check if pdftk is available for combining PDFs
    try {
        $pdftk = Get-Command pdftk -ErrorAction Stop
        Write-Host "Using pdftk to combine PDFs..." -ForegroundColor Yellow
        pdftk cover_page.pdf vision_guard_paper.pdf cat output vision_guard_complete.pdf
        Write-Host "Combined PDF created as vision_guard_complete.pdf" -ForegroundColor Green
    } catch {
        Write-Host "pdftk not found. Cover page and main paper will remain as separate PDFs." -ForegroundColor Yellow
        Write-Host "To combine them, install pdftk or use another PDF tool." -ForegroundColor Yellow
    }
    
    # Open the PDF
    Write-Host "Opening the PDF..." -ForegroundColor Cyan
    if (Test-Path "vision_guard_complete.pdf") {
        Invoke-Item vision_guard_complete.pdf
    } else {
        Invoke-Item vision_guard_paper.pdf
    }
} else {
    Write-Host "Error: Compilation failed. Please check the log files for errors." -ForegroundColor Red
}

Write-Host ""
Write-Host "Cleaning up temporary files..." -ForegroundColor Cyan
Remove-Item *.aux, *.log, *.out, *.toc, *.lof, *.lot, *.bbl, *.blg, *.synctex.gz -ErrorAction SilentlyContinue

Write-Host "Done!" -ForegroundColor Green
