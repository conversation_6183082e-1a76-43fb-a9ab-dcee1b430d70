import cv2
import numpy as np
import time
import threading
import subprocess
import platform
import os
from ultralytics import YOLO
import pyttsx3
import speech_recognition as sr
import queue
import random

class VisionGuard:
    def __init__(self,
                 model_path='runs/train/yolov8_door_detection/weights/best.pt',
                 camera_id=0,
                 confidence=0.4,
                 door_class_names=None,
                 obstacle_class_names=None,
                 frame_width=800,
                 frame_height=600,
                 focal_length=800,
                 known_door_width=0.9):

        self.model = YOLO(model_path)

        if door_class_names is None:
            self.door_class_names = ['door', 'Door', 'hinged', 'knob', 'lever']
        else:
            self.door_class_names = door_class_names

        if obstacle_class_names is None:
            self.obstacle_class_names = []
            if hasattr(self.model, 'names'):
                for _, name in self.model.names.items():
                    if name.lower() not in [door_name.lower() for door_name in self.door_class_names]:
                        self.obstacle_class_names.append(name)
            if not self.obstacle_class_names:
                self.obstacle_class_names = [
                    'person', 'bicycle', 'car', 'motorcycle', 'chair', 'couch',
                    'potted plant', 'bed', 'dining table', 'toilet', 'tv', 'laptop',
                    'mouse', 'remote', 'keyboard', 'cell phone', 'microwave',
                    'oven', 'toaster', 'sink', 'refrigerator', 'book', 'clock',
                    'vase', 'scissors', 'teddy bear', 'hair drier', 'toothbrush',
                    'wall', 'furniture', 'object', 'obstacle'
                ]
        else:
            self.obstacle_class_names = obstacle_class_names

        print(f"Door class names: {self.door_class_names}")
        print(f"Obstacle class names: {self.obstacle_class_names}")

        self.camera_id = camera_id
        self.cap = None
        self.frame_width = frame_width
        self.frame_height = frame_height

        self.confidence = confidence

        self.focal_length = focal_length
        self.known_door_width = known_door_width

        self.running = False
        self.navigating = False
        self.door_detected = False
        self.door_distance = None
        self.door_bbox = None
        self.door_center_x = None
        self.obstacles = []
        self.door_state = "unknown"

        self.last_guidance_time = 0
        self.guidance_interval = 2.0
        self.close_door_threshold = 1.0
        self.obstacle_warning_threshold = 1.5
        self.very_close_obstacle_threshold = 0.7
        
        self.door_announced = False
        self.obstacle_announced = False
        self.door_state_announced = False
        self.last_spoken_turn_dir = None
        self.last_spoken_obstacle_msg = None

        self.process_every_n_frames = 2
        self.frame_count = 0

        self.process_thread = None
        self.depth_map = None

        self.direction_history = []
        self.direction_history_max = 5
        self.last_direction = None

        self.path = []
        self.safety_margin = 30

        self.left_view = None
        self.right_view = None

        self.decision = None
        self.decision_confidence = 0.0

        # Enhanced door detection parameters
        self.open_door_reference_images = []
        self.door_state_history = []
        self.door_state_history_max = 5
        self.load_open_door_references()

        # Door detection thresholds
        self.edge_density_threshold = 0.15
        self.contour_area_threshold = 500
        self.depth_variance_threshold = 25
        self.color_variance_threshold = 30

        # Door opening detection parameters
        self.door_opening_angle_threshold = 15  # degrees
        self.door_frame_edge_threshold = 0.3

        self.is_windows = platform.system() == 'Windows'
        if self.is_windows:
            self.tts_engine = pyttsx3.init()
            self.tts_engine.setProperty('rate', 170)
            self.tts_engine.setProperty('volume', 0.9)
        else:
            self.tts_engine = None

        self.recognizer = sr.Recognizer()
        try:
            self.microphone = sr.Microphone()
            with self.microphone as source:
                self.recognizer.adjust_for_ambient_noise(source)
        except Exception as e:
            print(f"Microphone initialization error: {e}")
            print("Voice commands will be disabled. You can still use keyboard controls.")
            print("To enable voice commands, install PyAudio: pip install pyaudio")
            self.microphone = None

        self.message_queue = queue.Queue()
        self.speaking = False

        self.tts_thread = threading.Thread(target=self._process_tts_queue, daemon=True)
        self.tts_thread.start()

        self.listening = False
        self.listen_thread = None

        self.display_frames = {}

        self.guidance_phrases = {
            "turn_left_slight": ["Turn slightly left.", "Just a little to your left.", "Rotate gently left."],
            "turn_left_moderate": ["Turn left.", "Make a left turn.", "Pivot left."],
            "turn_left_sharp": ["Turn sharply left!", "Strong left turn!", "Hard left!"],
            "turn_right_slight": ["Turn slightly right.", "Just a little to your right.", "Rotate gently right."],
            "turn_right_moderate": ["Turn right.", "Make a right turn.", "Pivot right."],
            "turn_right_sharp": ["Turn sharply right!", "Strong right turn!", "Hard right!"],
            "go_forward": ["Move straight ahead.", "Continue forward.", "Keep going straight."],
            "obstacle_stop_urgent": ["Immediate obstacle! STOP! Right in front of you!", "CRITICAL OBSTACLE! Halt!"],
            "obstacle_stop_caution": ["Caution! Obstacle detected ahead. Please stop.", "Obstacle in your path, slow down."],
            "obstacle_avoid_left": ["Move to your left to pass the obstacle.", "Shift left around the obstacle."],
            "obstacle_avoid_right": ["Move to your right to pass the obstacle.", "Shift right around the obstacle."],
            "path_clear_continue": ["Path clear, continue straight.", "You're on track, keep moving."],
            "door_state_open": ["The door is open.", "It's an open door.", "I can see the door is open."],
            "door_state_closed": ["The door is closed.", "It's a closed door.", "The door appears to be closed."],
            "door_state_unknown": ["Door state unclear.", "Cannot determine if door is open or closed.", "Door state is uncertain."],
            "door_reached_open": ["You have reached the open door. You can proceed through.", "The open door is right here. Safe to enter."],
            "door_reached_closed": ["You have reached the closed door. You may need to open it.", "You are at the closed door. Try opening it."],
            "door_reached_unknown": ["You have reached the door. Check if it's open or closed.", "You are at the door. Please assess the situation."],
            "door_lost": ["Door lost. Please reorient.", "I've lost sight of the door. Look around."],
            "no_door": ["No door detected. Searching for doors. Please turn slowly.", "Looking for a door. Nothing found yet."]
        }
        self.turn_threshold_slight = 0.1
        self.turn_threshold_moderate = 0.25

    def _get_random_phrase(self, key):
        return random.choice(self.guidance_phrases.get(key, ["No phrase available."]))

    def load_open_door_references(self):
        """Load reference images of open doors for comparison"""
        open_door_folder = "Open Door"
        if os.path.exists(open_door_folder):
            image_files = [f for f in os.listdir(open_door_folder) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
            print(f"Found {len(image_files)} open door reference images")

            # Load a subset of reference images (to avoid memory issues)
            max_references = 10
            selected_files = image_files[:max_references] if len(image_files) > max_references else image_files

            for img_file in selected_files:
                img_path = os.path.join(open_door_folder, img_file)
                try:
                    img = cv2.imread(img_path)
                    if img is not None:
                        # Resize to standard size for comparison
                        img_resized = cv2.resize(img, (200, 200))
                        self.open_door_reference_images.append(img_resized)
                except Exception as e:
                    print(f"Error loading reference image {img_file}: {e}")

            print(f"Loaded {len(self.open_door_reference_images)} open door reference images")
        else:
            print("Open Door folder not found. Door state detection will use basic methods only.")

    def _process_tts_queue(self):
        while True:
            try:
                message = self.message_queue.get(timeout=0.1)
                self.speaking = True
                if self.is_windows and self.tts_engine:
                    self.tts_engine.say(message)
                    self.tts_engine.runAndWait()
                else:
                    try:
                        subprocess.run(['which', 'espeak'], check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                        subprocess.Popen(['espeak', '-s', '170', '-a', '200', message])
                        time.sleep(len(message) * 0.06)
                    except (subprocess.CalledProcessError, FileNotFoundError):
                        print(f"espeak not found or error. Cannot speak: {message}")
                self.speaking = False
                self.message_queue.task_done()
            except queue.Empty:
                time.sleep(0.1)
            except Exception as e:
                print(f"TTS error: {e}")
                self.speaking = False

    def speak(self, text, priority=False):
        print(f"Speaking: {text}")
        if priority:
            while not self.message_queue.empty():
                try:
                    self.message_queue.get_nowait()
                    self.message_queue.task_done()
                except queue.Empty:
                    break
            if self.is_windows and self.tts_engine:
                self.tts_engine.stop()
        self.message_queue.put(text)

    def initialize_camera(self):
        if self.cap is not None and self.cap.isOpened():
            self.cap.release()
        try:
            self.cap = cv2.VideoCapture(self.camera_id)
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.frame_width)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.frame_height)
            ret, _ = self.cap.read()
            if not ret:
                raise RuntimeError("Camera opened but failed to read frame")
        except Exception as e:
            print(f"Camera error: {e}")
            if self.cap is not None:
                self.cap.release()
                self.cap = None

        if self.cap is None or not self.cap.isOpened():
            print(f"Warning: Could not open camera {self.camera_id}")
            print("Using sample images instead...")
            self.sample_images = []
            sample_paths_root = [".", "dataset", "data", "yolov8_door_detection_dataset"]
            image_subfolders = ["images/train", "images/val", "images/test", "images", "results"]
            found_any_images = False
            for root_path in sample_paths_root:
                for subfolder in image_subfolders:
                    full_path = os.path.join(root_path, subfolder)
                    if os.path.exists(full_path):
                        image_files = [os.path.join(full_path, f) for f in os.listdir(full_path) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
                        if image_files:
                            self.sample_images.extend(image_files[:20])
                            found_any_images = True
                            if len(self.sample_images) >= 20:
                                break
                if found_any_images and len(self.sample_images) >= 20:
                    break

            if not self.sample_images:
                print("No sample images found. Creating a blank image.")
                blank_image = np.zeros((self.frame_height, self.frame_width, 3), dtype=np.uint8)
                cv2.putText(blank_image, "No camera or sample images available", (50, self.frame_height // 2),
                            cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
                self.sample_images = [blank_image]
                self.current_sample_index = 0
                self.using_sample_images = True
                self.using_sample_image_paths = False
            else:
                self.current_sample_index = 0
                self.using_sample_images = True
                self.using_sample_image_paths = True
            print(f"Using {len(self.sample_images)} sample images for simulation")
        else:
            self.frame_width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            self.frame_height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            self.using_sample_images = False
            print(f"Camera initialized with resolution: {self.frame_width}x{self.frame_height}")

    def estimate_distance(self, bbox_width):
        if bbox_width == 0:
            return float('inf')
        distance = (self.known_door_width * self.focal_length) / bbox_width
        return distance

    def estimate_depth_map(self, frame):
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        sobelx = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
        sobely = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
        magnitude = np.sqrt(sobelx**2 + sobely**2)
        magnitude = cv2.normalize(magnitude, None, 0, 255, cv2.NORM_MINMAX)
        depth_map = 255 - magnitude.astype(np.uint8)
        depth_map = cv2.GaussianBlur(depth_map, (15, 15), 0)
        return depth_map

    def create_perspective_views(self, frame):
        height, width = frame.shape[:2]
        mid = width // 2
        left_view = frame[:, :mid].copy()
        right_view = frame[:, mid:].copy()
        cv2.line(left_view, (mid-1, 0), (mid-1, height), (0, 255, 255), 2)
        cv2.line(right_view, (0, 0), (0, height), (0, 255, 255), 2)
        cv2.putText(left_view, "LEFT VIEW", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
        cv2.putText(right_view, "RIGHT VIEW", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
        return left_view, right_view

    def create_obstacle_map(self, obstacles):
        obstacle_map = np.zeros((self.frame_height, self.frame_width), dtype=np.uint8)
        for obstacle in obstacles:
            x1, y1, x2, y2 = obstacle['bbox']
            x1 = max(0, x1 - self.safety_margin)
            y1 = max(0, y1 - self.safety_margin)
            x2 = min(self.frame_width, x2 + self.safety_margin)
            y2 = min(self.frame_height, y2 + self.safety_margin)
            obstacle_map[y1:y2, x1:x2] = 1
        return obstacle_map

    def find_path(self, start_point, goal_point, obstacle_map):
        from scipy.ndimage import distance_transform_edt

        dist_transform = distance_transform_edt(1 - obstacle_map)
        y, x = np.indices((self.frame_height, self.frame_width))
        goal_x, goal_y = goal_point
        attractive = np.sqrt((x - goal_x)**2 + (y - goal_y)**2)
        potential = attractive - 5.0 * dist_transform

        path = []
        current = np.array(start_point).astype(float)
        path.append(current.copy())

        max_iterations = 200
        step_size = 5.0
        goal_threshold = 20

        for _ in range(max_iterations):
            if np.linalg.norm(current - np.array(goal_point)) < goal_threshold:
                break

            cx, cy = np.round(current).astype(int)
            cx = np.clip(cx, 0, self.frame_width - 1)
            cy = np.clip(cy, 0, self.frame_height - 1)

            grad_x = potential[np.clip(cy, 0, self.frame_height-1), np.clip(cx+1, 0, self.frame_width-1)] - potential[cy, np.clip(cx-1, 0, self.frame_width-1)]
            grad_y = potential[np.clip(cy+1, 0, self.frame_height-1), cx] - potential[np.clip(cy-1, 0, self.frame_height-1), cx]

            gradient = np.array([grad_x, grad_y])
            grad_norm = np.linalg.norm(gradient)

            if grad_norm > 0:
                direction = -gradient / grad_norm
            else:
                angle = np.random.uniform(0, 2 * np.pi)
                direction = np.array([np.cos(angle), np.sin(angle)])

            current = current + step_size * direction
            current = np.clip(current, [0, 0], [self.frame_width - 1, self.frame_height - 1])

            if len(path) == 0 or np.linalg.norm(current - path[-1]) > step_size / 2:
                path.append(current.copy())

            if obstacle_map[int(current[1]), int(current[0])] == 1:
                if len(path) > 1:
                    current = path[-2]
                    path.pop()
                else:
                    break
        self.path = path
        return path

    def draw_path(self, frame, path=None):
        if path is None:
            path = self.path
        if not path:
            return frame
        points = np.array([point for point in path], dtype=np.int32)
        cv2.polylines(frame, [points], False, (0, 255, 255), 2)
        if len(path) > 0:
            cv2.circle(frame, tuple(points[0]), 5, (0, 255, 0), -1)
            cv2.circle(frame, tuple(points[-1]), 5, (0, 0, 255), -1)
        return frame

    def get_direction(self, door_center_x):
        frame_center_x = self.frame_width // 2
        threshold_slight = self.frame_width * self.turn_threshold_slight
        threshold_moderate = self.frame_width * self.turn_threshold_moderate

        diff_from_center = door_center_x - frame_center_x

        if diff_from_center < -threshold_moderate:
            raw_direction = "sharp_left"
        elif diff_from_center < -threshold_slight:
            raw_direction = "moderate_left"
        elif diff_from_center > threshold_moderate:
            raw_direction = "sharp_right"
        elif diff_from_center > threshold_slight:
            raw_direction = "moderate_right"
        else:
            raw_direction = "forward"

        self.direction_history.append(raw_direction)
        if len(self.direction_history) > self.direction_history_max:
            self.direction_history.pop(0)

        from collections import Counter
        counts = Counter(self.direction_history)
        smoothed_direction = counts.most_common(1)[0][0]

        return smoothed_direction

    def draw_navigation_arrow(self, frame, door_center_x):
        frame_center_x = self.frame_width // 2
        arrow_length = 50
        arrow_color = (0, 255, 255)
        arrow_thickness = 2

        direction = self.get_direction(door_center_x)
        direction_text = direction.replace('_', ' ').upper()

        if "left" in direction:
            cv2.arrowedLine(frame, (frame_center_x, self.frame_height - 30),
                            (frame_center_x - arrow_length, self.frame_height - 30),
                            arrow_color, arrow_thickness)
            cv2.putText(frame, direction_text, (frame_center_x - arrow_length - 60, self.frame_height - 30),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, arrow_color, 2)
        elif "right" in direction:
            cv2.arrowedLine(frame, (frame_center_x, self.frame_height - 30),
                            (frame_center_x + arrow_length, self.frame_height - 30),
                            arrow_color, arrow_thickness)
            cv2.putText(frame, direction_text, (frame_center_x + arrow_length + 10, self.frame_height - 30),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, arrow_color, 2)
        else: # Forward
            cv2.arrowedLine(frame, (frame_center_x, self.frame_height - 30),
                            (frame_center_x, self.frame_height - 30 - arrow_length),
                            arrow_color, arrow_thickness)
            cv2.putText(frame, direction_text, (frame_center_x + 10, self.frame_height - 30 - arrow_length),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, arrow_color, 2)
        return frame

    def check_path_obstacles(self, obstacles):
        """
        Checks if any significant obstacles are intersecting the current planned path corridor.
        Returns (True, blocking_obstacle) if a blocking obstacle is found, else (False, None).
        """
        if not obstacles or not self.path:
            return False, None

        path_width_pixels = 50
        
        # Immediate path corridor check (from user to next few path points)
        if len(self.path) >= 2:
            user_x, user_y = self.frame_width // 2, self.frame_height - 10
            # Consider a point slightly ahead on the path
            look_ahead_idx = min(5, len(self.path) - 1) 
            first_path_point_x, first_path_point_y = int(self.path[look_ahead_idx][0]), int(self.path[look_ahead_idx][1])

            # Define a rectangle for the immediate path corridor
            rect_x1 = min(user_x, first_path_point_x) - path_width_pixels // 2
            rect_y1 = min(user_y, first_path_point_y) - path_width_pixels // 2
            rect_x2 = max(user_x, first_path_point_x) + path_width_pixels // 2
            rect_y2 = max(user_y, first_path_point_y) + path_width_pixels // 2

            for obstacle in obstacles:
                x1, y1, x2, y2 = obstacle['bbox']
                
                # Check for intersection of obstacle bounding box with this immediate path corridor rectangle
                if not (x2 < rect_x1 or x1 > rect_x2 or y2 < rect_y1 or y1 > rect_y2):
                    # If there's an overlap AND obstacle is within warning threshold
                    if obstacle['distance'] < self.obstacle_warning_threshold:
                        return True, obstacle

        return False, None

    def _analyze_door_edges(self, door_region):
        """Analyze edge patterns to determine door state"""
        gray = cv2.cvtColor(door_region, cv2.COLOR_BGR2GRAY)

        # Apply Gaussian blur to reduce noise
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)

        # Edge detection using Canny
        edges = cv2.Canny(blurred, 50, 150)

        # Calculate edge density
        edge_pixels = np.sum(edges > 0)
        total_pixels = edges.shape[0] * edges.shape[1]
        edge_density = edge_pixels / total_pixels

        # Find contours
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # Analyze contours
        significant_contours = [c for c in contours if cv2.contourArea(c) > self.contour_area_threshold]

        return edge_density, len(significant_contours), edges

    def _analyze_door_depth(self, door_region):
        """Analyze depth variations in door region"""
        gray = cv2.cvtColor(door_region, cv2.COLOR_BGR2GRAY)

        # Calculate depth variance using Laplacian
        laplacian = cv2.Laplacian(gray, cv2.CV_64F)
        depth_variance = np.var(laplacian)

        return depth_variance

    def _analyze_door_color_patterns(self, door_region):
        """Analyze color patterns and uniformity"""
        # Convert to HSV for better color analysis
        hsv = cv2.cvtColor(door_region, cv2.COLOR_BGR2HSV)

        # Calculate color variance in each channel
        h_var = np.var(hsv[:, :, 0])
        s_var = np.var(hsv[:, :, 1])
        v_var = np.var(hsv[:, :, 2])

        # Overall color variance
        color_variance = (h_var + s_var + v_var) / 3

        return color_variance

    def _compare_with_open_door_references(self, door_region):
        """Compare current door region with open door reference images"""
        if not self.open_door_reference_images:
            return 0.0

        # Resize door region to match reference size
        door_resized = cv2.resize(door_region, (200, 200))

        similarities = []
        for ref_img in self.open_door_reference_images:
            try:
                # Calculate structural similarity
                gray_door = cv2.cvtColor(door_resized, cv2.COLOR_BGR2GRAY)
                gray_ref = cv2.cvtColor(ref_img, cv2.COLOR_BGR2GRAY)

                # Simple correlation coefficient
                correlation = cv2.matchTemplate(gray_door, gray_ref, cv2.TM_CCOEFF_NORMED)[0][0]
                similarities.append(correlation)
            except Exception as e:
                print(f"Error comparing with reference: {e}")
                continue

        if similarities:
            return max(similarities)
        return 0.0

    def _detect_door_opening_angle(self, edges):
        """Detect if door is at an angle (partially open)"""
        # Find lines using Hough transform
        lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=50)

        if lines is not None:
            angles = []
            for line in lines[:10]:  # Check first 10 lines
                _, theta = line[0]  # We only need theta, not rho
                angle = theta * 180 / np.pi
                # Normalize angle to 0-90 range
                if angle > 90:
                    angle = 180 - angle
                angles.append(angle)

            if angles:
                # Check if there are lines at significant angles (indicating door opening)
                vertical_lines = [a for a in angles if abs(a - 90) < 10]
                angled_lines = [a for a in angles if abs(a - 90) > self.door_opening_angle_threshold]

                if len(angled_lines) > len(vertical_lines):
                    return True

        return False

    def _determine_door_state(self, door_bbox, all_other_objects_in_frame, current_frame):
        """Enhanced door state detection using multiple analysis methods with improved closed door detection"""
        x1, y1, x2, y2 = door_bbox

        # Extract door region with some padding
        padding = 10
        door_x1 = max(0, x1 - padding)
        door_y1 = max(0, y1 - padding)
        door_x2 = min(self.frame_width, x2 + padding)
        door_y2 = min(self.frame_height, y2 + padding)

        door_region = current_frame[door_y1:door_y2, door_x1:door_x2]

        if door_region.size == 0:
            return "closed"  # Default to closed if no region available

        # Method 1: Check for objects in door area (original method)
        objects_in_door = False
        inner_x1 = int(x1 + (x2 - x1) * 0.2)
        inner_y1 = int(y1 + (y2 - y1) * 0.2)
        inner_x2 = int(x2 - (x2 - x1) * 0.2)
        inner_y2 = int(y2 - (y2 - y1) * 0.2)

        for obj in all_other_objects_in_frame:
            obj_x1, obj_y1, obj_x2, obj_y2 = obj['bbox']
            inter_x1 = max(obj_x1, inner_x1)
            inter_y1 = max(obj_y1, inner_y1)
            inter_x2 = min(obj_x2, inner_x2)
            inter_y2 = min(obj_y2, inner_y2)
            inter_area = max(0, inter_x2 - inter_x1) * max(0, inter_y2 - inter_y1)
            obj_area = (obj_x2 - obj_x1) * (obj_y2 - obj_y1)
            if obj_area > 0 and (inter_area / obj_area) > 0.3:
                objects_in_door = True
                break

        # Method 2: Edge analysis
        edge_density, contour_count, edges = self._analyze_door_edges(door_region)

        # Method 3: Depth analysis
        depth_variance = self._analyze_door_depth(door_region)

        # Method 4: Color pattern analysis
        color_variance = self._analyze_door_color_patterns(door_region)

        # Method 5: Compare with open door references
        reference_similarity = self._compare_with_open_door_references(door_region)

        # Method 6: Door opening angle detection
        has_opening_angle = self._detect_door_opening_angle(edges)

        # Enhanced scoring system for door state determination
        open_score = 0
        closed_score = 0

        # Score based on objects in door area (stronger indicator for open doors)
        if objects_in_door:
            open_score += 4  # Increased weight
        else:
            closed_score += 2  # Increased weight for closed

        # Score based on edge density (open doors typically have more edges)
        if edge_density > self.edge_density_threshold:
            open_score += 3  # Increased weight
        else:
            closed_score += 2

        # Score based on contour count (open doors have more complex contours)
        if contour_count > 3:
            open_score += 2
        else:
            closed_score += 2

        # Score based on depth variance (open doors show more depth variation)
        if depth_variance > self.depth_variance_threshold:
            open_score += 3
        else:
            closed_score += 2

        # Score based on color variance (open doors typically show more color variation)
        if color_variance > self.color_variance_threshold:
            open_score += 2
        else:
            closed_score += 2

        # Score based on reference similarity (strongest indicator)
        if reference_similarity > 0.4:  # Higher threshold for confidence
            open_score += 5  # Strong indicator
        elif reference_similarity > 0.2:
            open_score += 2
        else:
            closed_score += 3  # If no similarity to open doors, likely closed

        # Score based on opening angle
        if has_opening_angle:
            open_score += 3

        # Additional closed door indicators
        # Check for uniform color/texture (typical of closed doors)
        gray_door = cv2.cvtColor(door_region, cv2.COLOR_BGR2GRAY)
        uniformity = np.std(gray_door)
        if uniformity < 20:  # Very uniform appearance
            closed_score += 2

        # Check for rectangular shape (closed doors are typically rectangular)
        door_aspect_ratio = (x2 - x1) / (y2 - y1)
        if 0.3 < door_aspect_ratio < 0.8:  # Typical door proportions
            closed_score += 1

        # Enhanced decision logic - if open door not clearly detected, default to closed
        confidence_threshold = 3  # Minimum confidence difference needed

        if open_score > closed_score + confidence_threshold:
            current_state = "open"
        elif closed_score >= open_score:  # Default to closed if not clearly open
            current_state = "closed"
        else:
            current_state = "closed"  # Default fallback to closed

        # Add to history for smoothing
        self.door_state_history.append(current_state)
        if len(self.door_state_history) > self.door_state_history_max:
            self.door_state_history.pop(0)

        # Use majority vote from history for final decision
        if len(self.door_state_history) >= 3:
            from collections import Counter
            state_counts = Counter(self.door_state_history)
            final_state = state_counts.most_common(1)[0][0]

            # If still uncertain after history, default to closed
            if len(set(self.door_state_history)) > 1:  # Mixed results
                closed_count = state_counts.get("closed", 0)
                open_count = state_counts.get("open", 0)
                if abs(closed_count - open_count) <= 1:  # Very close, default to closed
                    final_state = "closed"
        else:
            final_state = current_state

        # Debug information
        if self.navigating:
            print(f"Door State Analysis - Open: {open_score}, Closed: {closed_score}, "
                  f"Edge Density: {edge_density:.3f}, Depth Var: {depth_variance:.1f}, "
                  f"Color Var: {color_variance:.1f}, Ref Sim: {reference_similarity:.3f}, "
                  f"Uniformity: {uniformity:.1f}, Final: {final_state}")

        return final_state

    def create_door_analysis_overlay(self, frame, door_bbox, door_state):
        """Create a visual overlay showing door analysis results"""
        if door_bbox is None:
            return frame

        x1, y1, x2, y2 = door_bbox
        overlay = frame.copy()

        # Draw door bounding box with color based on state
        if door_state == "open":
            color = (0, 255, 0)  # Green for open
            thickness = 3
        elif door_state == "closed":
            color = (0, 0, 255)  # Red for closed
            thickness = 3
        else:
            color = (0, 255, 255)  # Yellow for unknown
            thickness = 2

        cv2.rectangle(overlay, (x1, y1), (x2, y2), color, thickness)

        # Add state text
        state_text = f"Door: {door_state.upper()}"
        text_size = cv2.getTextSize(state_text, cv2.FONT_HERSHEY_SIMPLEX, 0.8, 2)[0]
        text_x = x1
        text_y = y1 - 10 if y1 > 30 else y2 + 30

        # Add background rectangle for text
        cv2.rectangle(overlay, (text_x, text_y - text_size[1] - 5),
                     (text_x + text_size[0] + 10, text_y + 5), color, -1)
        cv2.putText(overlay, state_text, (text_x + 5, text_y),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)

        # Add analysis region indicators
        inner_x1 = int(x1 + (x2 - x1) * 0.2)
        inner_y1 = int(y1 + (y2 - y1) * 0.2)
        inner_x2 = int(x2 - (x2 - x1) * 0.2)
        inner_y2 = int(y2 - (y2 - y1) * 0.2)

        cv2.rectangle(overlay, (inner_x1, inner_y1), (inner_x2, inner_y2),
                     (255, 255, 255), 1)

        return overlay

    def _speak_turn_instruction(self, direction_type, distance):
        key = f"turn_{direction_type}"
        if distance > self.close_door_threshold * 2 and "sharp" in direction_type:
            key = key.replace("sharp", "moderate") 
        
        phrase = self._get_random_phrase(key)
        
        if self.last_spoken_turn_dir != (key, phrase):
            self.speak(f"{phrase}")
            self.last_spoken_turn_dir = (key, phrase)
        elif random.random() < 0.3:
            self.speak("Still need to adjust.")
                
    def _speak_obstacle_warning(self, blocking_obstacle, obstacle_distance, priority=True):
        obstacle_class = blocking_obstacle['class']
        warning_msg = ""
        
        if obstacle_distance < self.very_close_obstacle_threshold:
            warning_msg = self._get_random_phrase("obstacle_stop_urgent") + f" A {obstacle_class} is {obstacle_distance:.1f} meters away."
        elif obstacle_distance < self.obstacle_warning_threshold:
            warning_msg = self._get_random_phrase("obstacle_stop_caution") + f" A {obstacle_class} is {obstacle_distance:.1f} meters ahead."
            
        if warning_msg and self.last_spoken_obstacle_msg != warning_msg:
            self.speak(warning_msg, priority=priority)
            self.last_spoken_obstacle_msg = warning_msg

    def _speak_door_info(self, door_distance, door_state):
        if self.door_state != door_state:
            self.door_state_announced = False
        
        if not self.door_state_announced:
            state_phrase = self._get_random_phrase(f"door_state_{door_state}")
            self.speak(f"{state_phrase}. It is {door_distance:.1f} meters away.")
            self.door_state_announced = True

    def provide_guidance(self, door_detected, door_distance, door_center_x, obstacles, door_state):
        if not self.navigating:
            return

        current_time = time.time()
        general_guidance_interval = 2.0
        obstacle_check_interval = 0.5 

        path_blocked, blocking_obstacle = self.check_path_obstacles(obstacles)

        if path_blocked:
            obstacle_distance = blocking_obstacle['distance']
            self._speak_obstacle_warning(blocking_obstacle, obstacle_distance, priority=True)
            self.obstacle_announced = True
            self.door_announced = False
            self.door_state_announced = False
            self.last_spoken_turn_dir = None
            return
        else:
            if self.obstacle_announced and current_time - self.last_guidance_time > obstacle_check_interval:
                self.speak(self._get_random_phrase("path_clear_continue"))
                self.obstacle_announced = False
                self.last_spoken_obstacle_msg = None
                self.last_guidance_time = current_time

        if current_time - self.last_guidance_time < general_guidance_interval:
            return

        self.last_guidance_time = current_time

        if door_detected:
            if not self.door_announced:
                self.speak("Door detected. Initiating guidance.")
                self.door_announced = True

            self._speak_door_info(door_distance, door_state)

            if door_distance < self.close_door_threshold:
                if door_state == "open":
                    self.speak(self._get_random_phrase("door_reached_open"), priority=True)
                elif door_state == "closed":
                    self.speak(self._get_random_phrase("door_reached_closed"), priority=True)
                elif door_state == "unknown":
                    self.speak(self._get_random_phrase("door_reached_unknown"), priority=True)
                else:
                    self.speak("You have reached the door. Stop.", priority=True)
                self.stop_navigation()
                return

            direction_type = self.get_direction(door_center_x)
            self._speak_turn_instruction(direction_type, door_distance)

        elif self.door_detected: 
            self.speak(self._get_random_phrase("door_lost"), priority=True)
            self.door_detected = False
            self.door_announced = False
            self.door_state_announced = False
            self.last_spoken_turn_dir = None
            self.last_spoken_obstacle_msg = None
        else:
            self.speak(self._get_random_phrase("no_door"))
            self.door_announced = False
            self.door_state_announced = False
            self.last_spoken_turn_dir = None
            self.last_spoken_obstacle_msg = None


    def process_frame(self, frame):
        self.depth_map = self.estimate_depth_map(frame)
        self.left_view, self.right_view = self.create_perspective_views(frame)

        results = self.model(frame, conf=self.confidence)

        door_detected = False
        door_distance = None
        door_bbox = None
        door_center_x = None
        all_detections = []
        obstacles = []

        for result in results:
            boxes = result.boxes
            for box in boxes:
                cls = int(box.cls[0])
                cls_name = self.model.names[cls]
                conf = float(box.conf[0])
                x1, y1, x2, y2 = box.xyxy[0].cpu().numpy().astype(int)
                bbox = (x1, y1, x2, y2)

                det_info = {
                    'bbox': bbox,
                    'class': cls_name,
                    'confidence': conf,
                    'center_x': (x1 + x2) // 2,
                    'center_y': (y1 + y2) // 2
                }
                all_detections.append(det_info)

                if cls_name.lower() in [name.lower() for name in self.door_class_names]:
                    if not door_detected or (x2 - x1) > (door_bbox[2] - door_bbox[0]):
                        door_detected = True
                        door_bbox = bbox
                        door_distance = self.estimate_distance(x2 - x1)
                        door_center_x = (x1 + x2) // 2
                        det_info['distance'] = door_distance 

                if cls_name.lower() not in [name.lower() for name in self.door_class_names]:
                    obstacle_width = x2 - x1
                    obstacle_distance = self.estimate_distance(obstacle_width)
                    det_info['distance'] = obstacle_distance
                    obstacles.append(det_info)

        if door_detected:
            other_objects_for_state_check = [
                d for d in all_detections
                if d['class'].lower() not in [name.lower() for name in self.door_class_names]
            ]
            self.door_state = self._determine_door_state(door_bbox, other_objects_for_state_check, frame)
        else:
            self.door_state = "unknown"

        annotated_frame = results[0].plot()

        if door_detected and obstacles: 
            obstacle_map = self.create_obstacle_map(obstacles)
            start_point = (self.frame_width // 2, self.frame_height - 10)
            goal_point = (door_center_x, door_bbox[1] + (door_bbox[3] - door_bbox[1]) // 2)
            self.find_path(start_point, goal_point, obstacle_map)
            annotated_frame = self.draw_path(annotated_frame)

        depth_small = cv2.resize(self.depth_map, (self.frame_width // 4, self.frame_height // 4))
        depth_color = cv2.applyColorMap(depth_small, cv2.COLORMAP_JET)
        h, w = depth_color.shape[:2]
        annotated_frame[10:10+h, self.frame_width-10-w:self.frame_width-10] = depth_color

        if door_detected and door_distance is not None:
            x1, y1, x2, y2 = door_bbox
            center_x = (x1 + x2) // 2
            center_y = (y1 + y2) // 2
            door_info_text = f"Door: {door_distance:.2f}m ({self.door_state.upper()})"
            cv2.putText(annotated_frame, door_info_text, (center_x - 70, y1 - 20),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            annotated_frame = self.draw_navigation_arrow(annotated_frame, door_center_x)

        for obstacle in obstacles:
            x1, y1, x2, y2 = obstacle['bbox']
            if obstacle['distance'] < self.obstacle_warning_threshold:
                color = (0, 0, 255)
                thickness = 2
                cv2.putText(annotated_frame, f"{obstacle['class']}: {obstacle['distance']:.2f}m",
                            (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, thickness)
                cv2.rectangle(annotated_frame, (x1, y1), (x2, y2), color, thickness)

        status_text = "Navigating" if self.navigating else "Standby"
        cv2.putText(annotated_frame, status_text, (10, 30),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

        self.display_frames['main'] = annotated_frame
        self.display_frames['depth'] = cv2.applyColorMap(self.depth_map, cv2.COLORMAP_JET)
        self.display_frames['left'] = self.left_view
        self.display_frames['right'] = self.right_view

        obstacle_view = frame.copy()
        overlay = np.zeros_like(frame, dtype=np.uint8)
        if not obstacles:
            cv2.putText(obstacle_view, "NO OBSTACLES DETECTED",
                        (self.frame_width // 2 - 150, self.frame_height // 2),
                        cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 255, 0), 2)
        else:
            for obstacle in obstacles:
                x1, y1, x2, y2 = obstacle['bbox']
                color = (0, 0, 255) if obstacle['distance'] < self.obstacle_warning_threshold else (0, 255, 255)
                cv2.rectangle(overlay, (x1, y1), (x2, y2), color, -1)
                cv2.rectangle(obstacle_view, (x1, y1), (x2, y2), color, 2)
                cv2.putText(obstacle_view, f"{obstacle['class']}: {obstacle['distance']:.2f}m",
                            (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
                center_x, center_y = obstacle['center_x'], obstacle['center_y']
                start_point_arrow = (self.frame_width // 2, self.frame_height - 30)
                end_point_arrow = (center_x, center_y)
                cv2.arrowedLine(obstacle_view, start_point_arrow, end_point_arrow, (0, 255, 255), 2)
                mid_point_arrow = ((start_point_arrow[0] + end_point_arrow[0]) // 2, (start_point_arrow[1] + end_point_arrow[1]) // 2)
                cv2.putText(obstacle_view, f"{obstacle['distance']:.1f}m",
                            mid_point_arrow, cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 2)
            cv2.putText(obstacle_view, "CAUTION: OBSTACLES DETECTED",
                        (self.frame_width // 2 - 200, 80), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 0, 255), 2)
        alpha = 0.3
        cv2.addWeighted(overlay, alpha, obstacle_view, 1 - alpha, 0, obstacle_view)
        cv2.putText(obstacle_view, "OBSTACLE DETECTION", (10, 30),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.9, (0, 0, 255), 2)
        self.display_frames['obstacles'] = obstacle_view

        door_view = frame.copy()
        if door_detected:
            # Use enhanced door analysis overlay
            door_view = self.create_door_analysis_overlay(door_view, door_bbox, self.door_state)

            x1, y1, x2, y2 = door_bbox
            door_text = f"Door: {door_distance:.2f}m ({self.door_state.upper()})"
            cv2.putText(door_view, door_text, (x1, y1 - 40),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.9, (255, 255, 255), 2)

            # Draw navigation arrow
            start_point_arrow = (self.frame_width // 2, self.frame_height - 30)
            end_point_arrow = (door_center_x, (y1 + y2) // 2)
            cv2.arrowedLine(door_view, start_point_arrow, end_point_arrow, (0, 255, 255), 3)

            direction = self.get_direction(door_center_x)
            cv2.putText(door_view, f"Go {direction.upper()}",
                        (self.frame_width // 2 - 80, self.frame_height - 60),
                        cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 255, 255), 2)

            # Add confidence indicators
            if hasattr(self, 'door_state_history') and len(self.door_state_history) > 0:
                from collections import Counter
                state_counts = Counter(self.door_state_history)
                confidence = state_counts[self.door_state] / len(self.door_state_history)
                confidence_text = f"Confidence: {confidence:.1%}"
                cv2.putText(door_view, confidence_text, (x1, y2 + 25),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        else:
            cv2.putText(door_view, "NO DOOR DETECTED",
                        (self.frame_width // 2 - 150, self.frame_height // 2),
                        cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 0, 255), 2)

        cv2.putText(door_view, "ENHANCED DOOR DETECTION", (10, 30),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.9, (0, 255, 0), 2)
        self.display_frames['door'] = door_view

        return annotated_frame, door_detected, door_distance, door_bbox, door_center_x, obstacles

    def _listen_for_commands(self):
        if not self.microphone:
            print("Microphone not available. Voice commands disabled.")
            return
        self.listening = True
        while self.listening:
            try:
                with self.microphone as source:
                    audio = self.recognizer.listen(source, timeout=3, phrase_time_limit=5)
                try:
                    command = self.recognizer.recognize_google(audio).lower()
                    print(f"Recognized: {command}")
                    if "take me to the door" in command or "find door" in command:
                        self.start_navigation()
                    elif "stop" in command:
                        self.stop_navigation()
                    elif "where is the door" in command:
                        if self.door_detected and self.door_distance is not None:
                            direction = self.get_direction(self.door_center_x)
                            self.speak(f"Door is {direction.replace('_', ' ')}, {self.door_distance:.1f} meters away. It is {self.door_state}.")
                        else:
                            self.speak(self._get_random_phrase("no_door"))
                    elif "what's in front of me" in command or "what is in front of me" in command:
                        if self.obstacles:
                            close_obstacles = [o for o in self.obstacles if o['distance'] < self.obstacle_warning_threshold]
                            if close_obstacles:
                                obstacle_names = [o['class'] for o in close_obstacles[:3]]
                                self.speak(f"I see {', '.join(obstacle_names)} in front of you")
                            else:
                                self.speak("Path is clear")
                        else:
                            self.speak("Path is clear")
                except sr.UnknownValueError:
                    pass
                except sr.RequestError as e:
                    print(f"Could not request results from Google Speech Recognition service; {e}")
            except sr.WaitTimeoutError:
                pass
            except Exception as e:
                print(f"General listening error: {e}")
                time.sleep(0.1)

    def start_voice_recognition(self):
        if self.listening:
            return
        self.listening = True
        self.listen_thread = threading.Thread(target=self._listen_for_commands, daemon=True)
        self.listen_thread.start()
        self.speak("Voice commands are now active. Say 'take me to the door' to start navigation.")

    def stop_voice_recognition(self):
        self.listening = False
        if self.listen_thread and self.listen_thread.is_alive():
            time.sleep(0.5)
            self.listen_thread = None
        if self.speaking:
            if self.is_windows and self.tts_engine:
                self.tts_engine.stop()
        self.speak("Voice commands deactivated.", priority=True)

    def process_loop(self):
        try:
            self.initialize_camera()
            while self.running:
                if self.using_sample_images:
                    if self.using_sample_image_paths:
                        frame = cv2.imread(self.sample_images[self.current_sample_index])
                        if frame is None:
                            print(f"Error: Failed to load sample image {self.sample_images[self.current_sample_index]}, skipping.")
                            self.sample_images.pop(self.current_sample_index)
                            if not self.sample_images:
                                blank_image = np.zeros((self.frame_height, self.frame_width, 3), dtype=np.uint8)
                                cv2.putText(blank_image, "No sample images available", (50, self.frame_height // 2),
                                            cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
                                self.sample_images = [blank_image]
                                self.using_sample_image_paths = False
                            else:
                                self.current_sample_index = (self.current_sample_index) % len(self.sample_images)
                            time.sleep(0.1)
                            continue
                    else:
                        frame = self.sample_images[self.current_sample_index]
                    frame = cv2.resize(frame, (self.frame_width, self.frame_height))
                    if self.frame_count % 30 == 0:
                        self.current_sample_index = (self.current_sample_index + 1) % len(self.sample_images)
                    ret = True
                else:
                    ret, frame = self.cap.read()
                    if not ret:
                        print("Error: Failed to capture frame. Attempting to reinitialize camera...")
                        self.cap.release()
                        self.cap = cv2.VideoCapture(self.camera_id)
                        self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.frame_width)
                        self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.frame_height)
                        time.sleep(0.5)
                        continue

                self.frame_count += 1
                if self.frame_count % self.process_every_n_frames != 0:
                    if cv2.waitKey(1) & 0xFF == ord('q'):
                        break
                    continue

                _, door_detected, door_distance, door_bbox, door_center_x, obstacles = self.process_frame(frame)
                self.door_detected = door_detected
                self.door_distance = door_distance
                self.door_bbox = door_bbox
                self.door_center_x = door_center_x
                self.obstacles = obstacles

                if self.navigating:
                    self.provide_guidance(door_detected, door_distance, door_center_x, obstacles, self.door_state)

                cv2.imshow("Vision Guard - Main", self.display_frames['main'])
                display_h, display_w = 300, 400
                depth_view = cv2.resize(self.display_frames['depth'], (display_w, display_h))
                door_view = cv2.resize(self.display_frames['door'], (display_w, display_h))
                obstacle_view = cv2.resize(self.display_frames['obstacles'], (display_w, display_h))
                stereo_view = np.hstack((self.display_frames['left'], self.display_frames['right']))
                stereo_view = cv2.resize(stereo_view, (display_w, display_h))

                top_row = np.hstack((depth_view, door_view))
                bottom_row = np.hstack((obstacle_view, stereo_view))
                grid = np.vstack((top_row, bottom_row))

                cv2.putText(grid, "Depth Map", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
                cv2.putText(grid, "Door Detection", (display_w + 10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
                cv2.putText(grid, "Obstacle Detection", (10, display_h + 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
                cv2.putText(grid, "Left/Right View", (display_w + 10, display_h + 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)

                cv2.imshow("Vision Guard - Analysis", grid)

                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    break
                elif key == ord('n'):
                    self.start_navigation()
                elif key == ord('s'):
                    self.stop_navigation()
                elif key == ord('v'):
                    if not self.listening:
                        self.start_voice_recognition()
                    else:
                        self.stop_voice_recognition()

        except Exception as e:
            print(f"Error in processing loop: {e}")
        finally:
            if self.cap is not None:
                self.cap.release()
            cv2.destroyAllWindows()

    def start_navigation(self):
        if not self.navigating:
            self.navigating = True
            self.door_announced = False
            self.obstacle_announced = False
            self.door_state_announced = False
            self.last_spoken_turn_dir = None
            self.last_spoken_obstacle_msg = None
            self.speak("Starting navigation to the door. Please move slowly.", priority=True)

    def stop_navigation(self):
        if self.navigating:
            self.navigating = False
            self.speak("Navigation stopped.", priority=True)

    def start(self):
        if self.running:
            return
        self.running = True
        self.process_thread = threading.Thread(target=self.process_loop)
        self.process_thread.daemon = True
        self.process_thread.start()
        print("Vision Guard started")
        print("Press 'q' to quit, 'n' to start navigation, 's' to stop navigation, 'v' to toggle voice commands")
        self.speak("Vision Guard is ready. Press N to start navigation or say take me to the door.")

    def stop(self):
        self.running = False
        self.navigating = False
        self.stop_voice_recognition()
        if self.process_thread:
            self.process_thread.join(timeout=1.0)
        while not self.message_queue.empty():
            try:
                self.message_queue.get_nowait()
                self.message_queue.task_done()
            except queue.Empty:
                break
        if self.is_windows and self.tts_engine:
            self.tts_engine.stop()
            self.tts_engine.runAndWait()
        print("Vision Guard stopped")

def main():
    vision_guard = VisionGuard(
        model_path='runs/train/yolov8_door_detection/weights/best.pt',
        camera_id=0,
        confidence=0.4,
        door_class_names=['door', 'Door', 'hinged', 'knob', 'lever'],
        frame_width=800,
        frame_height=600
    )
    try:
        vision_guard.start()
        while True:
            time.sleep(0.1)
    except KeyboardInterrupt:
        print("Interrupted by user")
    finally:
        vision_guard.stop()

if __name__ == "__main__":
    main()