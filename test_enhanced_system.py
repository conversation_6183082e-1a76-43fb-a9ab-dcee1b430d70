#!/usr/bin/env python3
"""
Test Script for Enhanced VisionGuard System
==========================================

This script tests the core functionality of the Enhanced VisionGuard system
without requiring a camera or full UI setup.
"""

import sys
import os
import numpy as np
import cv2
import logging
from unittest.mock import Mock, patch

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing imports...")
    
    try:
        import cv2
        print("✓ OpenCV imported successfully")
    except ImportError as e:
        print(f"✗ OpenCV import failed: {e}")
        return False
    
    try:
        import torch
        from ultralytics import YOLO
        print("✓ YOLO/PyTorch imported successfully")
    except ImportError as e:
        print(f"✗ YOLO/PyTorch import failed: {e}")
        return False
    
    try:
        import pyttsx3
        print("✓ Text-to-speech imported successfully")
    except ImportError as e:
        print(f"✗ Text-to-speech import failed: {e}")
        return False
    
    try:
        import speech_recognition as sr
        print("✓ Speech recognition imported successfully")
    except ImportError as e:
        print(f"✗ Speech recognition import failed: {e}")
        return False
    
    try:
        import tkinter as tk
        print("✓ Tkinter imported successfully")
    except ImportError as e:
        print(f"✗ Tkinter import failed: {e}")
        return False
    
    return True

def test_vision_guard_initialization():
    """Test VisionGuard initialization with mocked components"""
    print("\nTesting VisionGuard initialization...")
    
    try:
        # Mock the camera and YOLO model to avoid hardware dependencies
        with patch('cv2.VideoCapture') as mock_camera, \
             patch('ultralytics.YOLO') as mock_yolo, \
             patch('pyttsx3.init') as mock_tts, \
             patch('tkinter.Tk') as mock_tk:
            
            # Configure mocks
            mock_camera.return_value.isOpened.return_value = True
            mock_yolo.return_value = Mock()
            mock_tts.return_value = Mock()
            mock_tk.return_value = Mock()
            
            # Import and initialize VisionGuard
            from vision_guard_enhanced import EnhancedVisionGuard
            
            vision_guard = EnhancedVisionGuard()
            print("✓ VisionGuard initialized successfully")
            
            # Test basic methods
            test_frame = np.zeros((480, 640, 3), dtype=np.uint8)
            
            # Test distance estimation
            distance = vision_guard.estimate_distance(100)
            assert distance > 0, "Distance estimation failed"
            print("✓ Distance estimation working")
            
            # Test direction detection
            direction = vision_guard.get_direction(320)  # Center
            assert direction == "straight", "Direction detection failed"
            print("✓ Direction detection working")
            
            # Test door state analysis
            door_region = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
            state, confidence = vision_guard.analyze_door_state(door_region)
            assert state in ["open", "closed", "unknown"], "Door state analysis failed"
            print("✓ Door state analysis working")
            
            return True
            
    except Exception as e:
        print(f"✗ VisionGuard initialization failed: {e}")
        return False

def test_voice_system():
    """Test voice system components"""
    print("\nTesting voice system...")
    
    try:
        import pyttsx3
        engine = pyttsx3.init()
        
        # Test basic TTS functionality
        voices = engine.getProperty('voices')
        if voices:
            print(f"✓ Found {len(voices)} voice(s) available")
        else:
            print("⚠ No voices found, but TTS engine initialized")
        
        # Test speech recognition
        import speech_recognition as sr
        recognizer = sr.Recognizer()
        
        # Check for microphone
        try:
            mic = sr.Microphone()
            print("✓ Microphone detected")
        except Exception as e:
            print(f"⚠ Microphone issue: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ Voice system test failed: {e}")
        return False

def test_image_processing():
    """Test image processing capabilities"""
    print("\nTesting image processing...")
    
    try:
        # Create test images
        test_image = np.random.randint(0, 255, (200, 200, 3), dtype=np.uint8)
        
        # Test basic OpenCV operations
        gray = cv2.cvtColor(test_image, cv2.COLOR_BGR2GRAY)
        edges = cv2.Canny(gray, 50, 150)
        
        # Test contour detection
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # Test color space conversion
        hsv = cv2.cvtColor(test_image, cv2.COLOR_BGR2HSV)
        
        # Test template matching
        template = test_image[50:150, 50:150]
        result = cv2.matchTemplate(test_image, template, cv2.TM_CCOEFF_NORMED)
        
        print("✓ Image processing operations working")
        return True
        
    except Exception as e:
        print(f"✗ Image processing test failed: {e}")
        return False

def test_reference_images():
    """Test reference image loading"""
    print("\nTesting reference image loading...")
    
    reference_dir = "Open Door"
    if os.path.exists(reference_dir):
        image_files = [f for f in os.listdir(reference_dir) 
                      if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))]
        
        if image_files:
            print(f"✓ Found {len(image_files)} reference images")
            
            # Test loading a sample image
            sample_image = os.path.join(reference_dir, image_files[0])
            img = cv2.imread(sample_image)
            if img is not None:
                print("✓ Reference image loading working")
                return True
            else:
                print("⚠ Reference image found but couldn't load")
                return False
        else:
            print("⚠ Reference directory exists but no images found")
            return False
    else:
        print("⚠ Reference directory 'Open Door' not found")
        return False

def run_all_tests():
    """Run all tests and provide summary"""
    print("=" * 50)
    print("Enhanced VisionGuard System Test")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("VisionGuard Initialization", test_vision_guard_initialization),
        ("Voice System", test_voice_system),
        ("Image Processing", test_image_processing),
        ("Reference Images", test_reference_images)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("Test Summary")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        print("\n🎉 All tests passed! The system should work correctly.")
    elif passed >= len(results) - 1:
        print("\n⚠️ Most tests passed. Check warnings above.")
    else:
        print("\n❌ Several tests failed. Please check dependencies and setup.")
    
    return passed == len(results)

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
