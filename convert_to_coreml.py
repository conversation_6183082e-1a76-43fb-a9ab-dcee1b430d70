#!/usr/bin/env python3
"""
Convert YOLOv8 model to CoreML format for iPad deployment
"""

import argparse
from ultralytics import Y<PERSON><PERSON>

def parse_args():
    parser = argparse.ArgumentParser(description='Convert YOLOv8 model to CoreML format')
    parser.add_argument('--model', type=str, default='runs/train/yolov8_door_detection/weights/best.pt',
                        help='Path to the YOLOv8 model')
    parser.add_argument('--output', type=str, default='ipad_door_assistant',
                        help='Output name for the CoreML model (without extension)')
    parser.add_argument('--imgsz', type=int, default=640,
                        help='Image size for the model')
    parser.add_argument('--half', action='store_true',
                        help='Use half precision (FP16)')
    parser.add_argument('--nms', action='store_true',
                        help='Add NMS to the model')
    return parser.parse_args()

def main():
    args = parse_args()
    
    print(f"Loading YOLOv8 model from {args.model}...")
    model = YOLO(args.model)
    
    print(f"Converting to CoreML format...")
    model.export(
        format="coreml",
        imgsz=args.imgsz,
        half=args.half,
        nms=args.nms,
        int8=False,  # INT8 quantization is not needed for iPad
        batch=1
    )
    
    print(f"Model converted successfully! The CoreML model is saved as {args.output}.mlpackage")
    print("You can now use this model in your iPad application.")

if __name__ == "__main__":
    main()
