#!/usr/bin/env python3
"""
Convert YOLOv8 model to TensorFlow.js format for web deployment
"""

import argparse
import os
import subprocess
import sys
from ultralytics import YOLO

def parse_args():
    parser = argparse.ArgumentParser(description='Convert YOLOv8 model to TensorFlow.js format')
    parser.add_argument('--model', type=str, default='runs/train/yolov8_door_detection/weights/best.pt',
                        help='Path to the YOLOv8 model')
    parser.add_argument('--output', type=str, default='Web_Door_Assistant/models',
                        help='Output directory for the TensorFlow.js model')
    parser.add_argument('--imgsz', type=int, default=640,
                        help='Image size for the model')
    return parser.parse_args()

def check_requirements():
    """Check if required packages are installed"""
    required_packages = ['tensorflowjs']
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            print(f"Installing {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])

def main():
    args = parse_args()
    
    # Check requirements
    check_requirements()
    
    print(f"Loading YOLOv8 model from {args.model}...")
    model = YOLO(args.model)
    
    # First convert to TensorFlow SavedModel format
    print(f"Converting to TensorFlow SavedModel format...")
    tf_model_path = os.path.join(os.path.dirname(args.output), "tf_model")
    model.export(format="saved_model", imgsz=args.imgsz)
    
    # Create output directory if it doesn't exist
    os.makedirs(args.output, exist_ok=True)
    
    # Convert SavedModel to TensorFlow.js format
    print(f"Converting to TensorFlow.js format...")
    import tensorflowjs as tfjs
    
    # Get the path to the exported SavedModel
    saved_model_path = os.path.join(os.path.dirname(args.model), "saved_model")
    
    # Convert to TensorFlow.js format
    tfjs.converters.convert_tf_saved_model(
        saved_model_path,
        args.output
    )
    
    print(f"Model converted successfully! The TensorFlow.js model is saved in {args.output}")
    print("You can now use this model in your web application.")

if __name__ == "__main__":
    main()
