import os
from ultralytics import YOLO
import matplotlib.pyplot as plt
import numpy as np

def train_yolo_model(
    data_yaml='data.yaml',
    model_size='n',  # n=nano, s=small, m=medium, l=large, x=xlarge
    epochs=50,
    batch_size=16,
    imgsz=640,
    patience=10,
    project='runs/train',
    name='yolov8_door_detection'
):
    """
    Train a YOLOv8 model on the door detection dataset.
    
    Args:
        data_yaml (str): Path to the data.yaml file
        model_size (str): Size of the YOLOv8 model (n, s, m, l, x)
        epochs (int): Number of training epochs
        batch_size (int): Batch size for training
        imgsz (int): Image size for training
        patience (int): Early stopping patience
        project (str): Directory to save results
        name (str): Name of the experiment
    
    Returns:
        model: Trained YOLOv8 model
    """
    # Load a model
    model = YOLO(f'yolov8{model_size}.pt')  # load a pretrained model
    
    # Train the model
    results = model.train(
        data=data_yaml,
        epochs=epochs,
        batch=batch_size,
        imgsz=imgsz,
        patience=patience,
        project=project,
        name=name,
        exist_ok=True  # Overwrite existing experiment
    )
    
    return model

def validate_model(model, data_yaml='data.yaml'):
    """
    Validate the trained model on the validation set.
    
    Args:
        model: Trained YOLOv8 model
        data_yaml (str): Path to the data.yaml file
    
    Returns:
        results: Validation results
    """
    # Validate the model
    results = model.val(data=data_yaml)
    
    return results

def plot_results(results_file):
    """
    Plot training results from the results.csv file.
    
    Args:
        results_file (str): Path to the results.csv file
    """
    try:
        import pandas as pd
        
        # Load results
        results = pd.read_csv(results_file)
        
        # Create figure with subplots
        fig, axs = plt.subplots(2, 2, figsize=(12, 10))
        
        # Plot training loss
        axs[0, 0].plot(results['epoch'], results['train/box_loss'], label='box_loss')
        axs[0, 0].plot(results['epoch'], results['train/cls_loss'], label='cls_loss')
        axs[0, 0].plot(results['epoch'], results['train/dfl_loss'], label='dfl_loss')
        axs[0, 0].set_title('Training Loss')
        axs[0, 0].set_xlabel('Epoch')
        axs[0, 0].set_ylabel('Loss')
        axs[0, 0].legend()
        
        # Plot validation metrics
        axs[0, 1].plot(results['epoch'], results['metrics/precision(B)'], label='Precision')
        axs[0, 1].plot(results['epoch'], results['metrics/recall(B)'], label='Recall')
        axs[0, 1].plot(results['epoch'], results['metrics/mAP50(B)'], label='mAP50')
        axs[0, 1].plot(results['epoch'], results['metrics/mAP50-95(B)'], label='mAP50-95')
        axs[0, 1].set_title('Validation Metrics')
        axs[0, 1].set_xlabel('Epoch')
        axs[0, 1].set_ylabel('Metric')
        axs[0, 1].legend()
        
        # Plot learning rate
        axs[1, 0].plot(results['epoch'], results['lr/pg0'], label='Learning Rate')
        axs[1, 0].set_title('Learning Rate')
        axs[1, 0].set_xlabel('Epoch')
        axs[1, 0].set_ylabel('Learning Rate')
        
        # Plot validation loss
        axs[1, 1].plot(results['epoch'], results['val/box_loss'], label='val_box_loss')
        axs[1, 1].plot(results['epoch'], results['val/cls_loss'], label='val_cls_loss')
        axs[1, 1].plot(results['epoch'], results['val/dfl_loss'], label='val_dfl_loss')
        axs[1, 1].set_title('Validation Loss')
        axs[1, 1].set_xlabel('Epoch')
        axs[1, 1].set_ylabel('Loss')
        axs[1, 1].legend()
        
        plt.tight_layout()
        plt.savefig('training_results.png')
        plt.close()
        
        print(f"Training results plot saved to 'training_results.png'")
    except Exception as e:
        print(f"Error plotting results: {e}")

if __name__ == "__main__":
    # Set training parameters
    model_size = 'm'  # Use medium size model (you can change to n, s, l, x)
    epochs = 100
    batch_size = 16
    imgsz = 640
    patience = 15
    
    print(f"Starting YOLOv8{model_size} training for {epochs} epochs...")
    
    # Train the model
    model = train_yolo_model(
        model_size=model_size,
        epochs=epochs,
        batch_size=batch_size,
        imgsz=imgsz,
        patience=patience
    )
    
    # Validate the model
    print("Validating the model...")
    results = validate_model(model)
    
    # Plot training results
    results_file = f"runs/train/yolov8_door_detection/results.csv"
    if os.path.exists(results_file):
        print("Plotting training results...")
        plot_results(results_file)
    
    print("Training and validation complete!")
    print(f"Model saved to: runs/train/yolov8_door_detection/weights/best.pt")
