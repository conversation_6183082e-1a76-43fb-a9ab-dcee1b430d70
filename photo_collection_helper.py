"""
Photo Collection Helper for Open Door Training
This script helps you systematically collect open door photos
"""

import cv2
import os
import time
from datetime import datetime

class PhotoCollectionHelper:
    def __init__(self, save_folder="Open Door", camera_id=0):
        self.save_folder = save_folder
        self.camera_id = camera_id
        self.photo_count = 0
        
        # Create folder if it doesn't exist
        if not os.path.exists(self.save_folder):
            os.makedirs(self.save_folder)
            print(f"Created folder: {self.save_folder}")
        
        # Count existing photos
        existing_photos = [f for f in os.listdir(self.save_folder) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
        self.photo_count = len(existing_photos)
        print(f"Found {self.photo_count} existing photos in {self.save_folder}")
    
    def start_collection(self):
        """Start the photo collection process"""
        print("\n" + "="*60)
        print("📸 OPEN DOOR PHOTO COLLECTION HELPER")
        print("="*60)
        print("This will help you collect high-quality open door photos")
        print("\nTips for better photos:")
        print("• Take photos from different angles (left, center, right)")
        print("• Vary your distance (close, medium, far)")
        print("• Include different lighting conditions")
        print("• Capture different door opening angles (slightly open, wide open)")
        print("• Include different door types if available")
        print("\nControls:")
        print("• SPACEBAR: Take photo")
        print("• 'q': Quit")
        print("• 'h': Show this help again")
        print("\nStarting camera...")
        
        # Initialize camera
        cap = cv2.VideoCapture(self.camera_id)
        if not cap.isOpened():
            print(f"Error: Could not open camera {self.camera_id}")
            return
        
        # Set camera properties
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
        
        print("Camera ready! Position yourself in front of an OPEN door and press SPACEBAR to take photos")
        
        # Photo collection categories
        categories = [
            "Wide Open - Center View",
            "Wide Open - Left Angle", 
            "Wide Open - Right Angle",
            "Partially Open - 45 degrees",
            "Partially Open - 30 degrees",
            "Close Distance - Wide Open",
            "Medium Distance - Wide Open", 
            "Far Distance - Wide Open",
            "Different Lighting - Bright",
            "Different Lighting - Dim"
        ]
        
        current_category = 0
        photos_in_category = 0
        target_per_category = 3
        
        while True:
            ret, frame = cap.read()
            if not ret:
                print("Error reading from camera")
                break
            
            # Add overlay information
            overlay = frame.copy()
            
            # Category information
            if current_category < len(categories):
                category_text = f"Category {current_category + 1}/{len(categories)}: {categories[current_category]}"
                progress_text = f"Photos in category: {photos_in_category}/{target_per_category}"
            else:
                category_text = "All categories complete! Take any additional photos"
                progress_text = f"Total photos: {self.photo_count}"
            
            # Add text overlay
            cv2.putText(overlay, category_text, (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            cv2.putText(overlay, progress_text, (10, 60), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)
            cv2.putText(overlay, f"Total photos collected: {self.photo_count}", (10, 90), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            
            # Instructions
            cv2.putText(overlay, "SPACEBAR: Take Photo | Q: Quit | H: Help", 
                       (10, frame.shape[0] - 20), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            
            # Show crosshair for centering
            h, w = frame.shape[:2]
            cv2.line(overlay, (w//2 - 20, h//2), (w//2 + 20, h//2), (0, 255, 0), 2)
            cv2.line(overlay, (w//2, h//2 - 20), (w//2, h//2 + 20), (0, 255, 0), 2)
            
            cv2.imshow("Open Door Photo Collection", overlay)
            
            key = cv2.waitKey(1) & 0xFF
            
            if key == ord(' '):  # Spacebar to take photo
                self.save_photo(frame)
                photos_in_category += 1
                
                # Move to next category if enough photos taken
                if photos_in_category >= target_per_category and current_category < len(categories) - 1:
                    current_category += 1
                    photos_in_category = 0
                    print(f"\n✅ Category completed! Moving to: {categories[current_category]}")
                    time.sleep(1)  # Brief pause
                
            elif key == ord('q'):
                break
            elif key == ord('h'):
                self.show_help()
        
        cap.release()
        cv2.destroyAllWindows()
        
        print(f"\n🎉 Photo collection completed!")
        print(f"Total photos collected: {self.photo_count}")
        print(f"Photos saved in: {self.save_folder}")
        print("\nYour enhanced door detection system will now be more accurate!")
    
    def save_photo(self, frame):
        """Save a photo with timestamp"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]  # Include milliseconds
        filename = f"open_door_{timestamp}.jpg"
        filepath = os.path.join(self.save_folder, filename)
        
        # Save the photo
        cv2.imwrite(filepath, frame)
        self.photo_count += 1
        
        print(f"📸 Photo saved: {filename}")
        
        # Brief visual feedback
        time.sleep(0.1)
    
    def show_help(self):
        """Show detailed help information"""
        print("\n" + "="*50)
        print("📋 PHOTO COLLECTION TIPS")
        print("="*50)
        print("For BEST results, take photos of:")
        print("\n🚪 DOOR POSITIONS:")
        print("  • Wide open (90 degrees)")
        print("  • Partially open (45 degrees)")
        print("  • Slightly open (30 degrees)")
        print("\n📐 CAMERA ANGLES:")
        print("  • Straight on (center)")
        print("  • From the left side")
        print("  • From the right side")
        print("\n📏 DISTANCES:")
        print("  • Close (1-2 meters)")
        print("  • Medium (3-4 meters)")
        print("  • Far (5+ meters)")
        print("\n💡 LIGHTING:")
        print("  • Bright lighting")
        print("  • Normal lighting")
        print("  • Dim lighting")
        print("\n🎯 QUALITY TIPS:")
        print("  • Keep the door in center of frame")
        print("  • Avoid blurry photos")
        print("  • Include door frame in the shot")
        print("  • Make sure door opening is clearly visible")
        print("="*50)

def main():
    print("🚪 Open Door Photo Collection Helper")
    print("This tool will help you collect training photos for better door detection")
    
    # Ask user for camera selection
    try:
        camera_id = int(input("\nEnter camera ID (usually 0 for default camera): "))
    except ValueError:
        camera_id = 0
        print("Using default camera (ID: 0)")
    
    # Ask for custom folder name
    folder_name = input("Enter folder name for photos (default: 'Open Door'): ").strip()
    if not folder_name:
        folder_name = "Open Door"
    
    # Create helper and start collection
    helper = PhotoCollectionHelper(save_folder=folder_name, camera_id=camera_id)
    helper.start_collection()

if __name__ == "__main__":
    main()
