import cv2
import time
from ultralytics import YOL<PERSON>

# Configuration
MODEL_PATH = 'runs/train/yolov8_door_detection/weights/best.pt'
CONFIDENCE_THRESHOLD = 0.25
CAMERA_ID = 0
WIDTH = 640
HEIGHT = 480

def main():
    # Load the YOLOv8 model
    print(f"Loading model from {MODEL_PATH}...")
    model = YOLO(MODEL_PATH)
    
    # Initialize the camera
    print(f"Opening camera device {CAMERA_ID}...")
    cap = cv2.VideoCapture(CAMERA_ID)
    
    # Set camera properties
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, WIDTH)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, HEIGHT)
    
    # Check if the camera opened successfully
    if not cap.isOpened():
        print("Error: Could not open camera.")
        return
    
    # Get actual camera properties
    actual_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    actual_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    
    print(f"Camera initialized with resolution: {actual_width}x{actual_height}")
    
    # Create a window for display
    window_name = "YOLOv8 Door Detection"
    cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
    cv2.resizeWindow(window_name, actual_width, actual_height)
    
    # Variables for FPS calculation
    fps = 0
    frame_count = 0
    start_time = time.time()
    
    print("Press 'q' to quit")
    
    # Main loop
    while True:
        # Read a frame from the camera
        ret, frame = cap.read()
        
        if not ret:
            print("Error: Failed to capture frame from camera.")
            break
        
        # Perform detection
        results = model(frame, conf=CONFIDENCE_THRESHOLD)
        
        # Process the results
        annotated_frame = results[0].plot()
        
        # Calculate and display FPS
        frame_count += 1
        elapsed_time = time.time() - start_time
        if elapsed_time >= 1.0:
            fps = frame_count / elapsed_time
            frame_count = 0
            start_time = time.time()
        
        # Display FPS on the frame
        cv2.putText(annotated_frame, f"FPS: {fps:.1f}", (10, 30), 
                    cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        
        # Show the frame
        cv2.imshow(window_name, annotated_frame)
        
        # Check for key press
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            print("Quitting...")
            break
    
    # Release resources
    cap.release()
    cv2.destroyAllWindows()
    print("Camera detection stopped.")

if __name__ == "__main__":
    main()
