import os
import glob
from ultralytics import YOLO
import cv2
import numpy as np
import random

def test_model(
    model_path='runs/train/yolov8_door_detection/weights/best.pt',
    test_dir='test/images',
    output_dir='runs/detect',
    conf=0.25,
    num_samples=5
):
    """
    Test the trained YOLOv8 model on test images.
    
    Args:
        model_path (str): Path to the trained model weights
        test_dir (str): Directory containing test images
        output_dir (str): Directory to save detection results
        conf (float): Confidence threshold for detections
        num_samples (int): Number of random samples to test
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Load the model
    model = YOLO(model_path)
    
    # Get list of test images
    test_images = glob.glob(os.path.join(test_dir, '*.jpg')) + \
                  glob.glob(os.path.join(test_dir, '*.jpeg')) + \
                  glob.glob(os.path.join(test_dir, '*.png'))
    
    if not test_images:
        print(f"No images found in {test_dir}")
        return
    
    # Select random samples if there are more images than num_samples
    if len(test_images) > num_samples:
        test_images = random.sample(test_images, num_samples)
    
    print(f"Testing model on {len(test_images)} images...")
    
    # Process each test image
    for img_path in test_images:
        # Run inference
        results = model(img_path, conf=conf)
        
        # Get the base filename
        base_name = os.path.basename(img_path)
        
        # Save the results
        for i, result in enumerate(results):
            # Get the image with detections
            img_with_boxes = result.plot()
            
            # Save the image with detections
            output_path = os.path.join(output_dir, f"detect_{base_name}")
            cv2.imwrite(output_path, img_with_boxes)
            
            print(f"Detection saved to {output_path}")
            
            # Print detection information
            boxes = result.boxes
            if len(boxes) > 0:
                print(f"Found {len(boxes)} objects in {base_name}:")
                for box in boxes:
                    cls = int(box.cls[0])
                    cls_name = model.names[cls]
                    conf = float(box.conf[0])
                    print(f"  - {cls_name}: {conf:.2f}")
            else:
                print(f"No objects detected in {base_name}")

if __name__ == "__main__":
    # Test the model
    test_model(
        model_path='runs/train/yolov8_door_detection/weights/best.pt',
        test_dir='test/images',
        output_dir='runs/detect',
        conf=0.25,
        num_samples=5
    )
