% System architecture diagram for Vision Guard
% This file can be included in the main paper using \input{system_architecture.tex}

\begin{figure*}[t]
\centering
\begin{tikzpicture}[
    node distance=1.5cm,
    box/.style={rectangle, draw, rounded corners, minimum width=2.5cm, minimum height=1.2cm, text centered, font=\footnotesize},
    arrow/.style={thick,->,>=stealth},
    data/.style={rectangle, draw, dashed, rounded corners, minimum width=2cm, minimum height=0.8cm, text centered, font=\footnotesize},
    module/.style={rectangle, draw, fill=blue!10, rounded corners, minimum width=3cm, minimum height=1.5cm, text centered, font=\footnotesize},
    title/.style={font=\small\bfseries}
]

% Input
\node[box, fill=green!10] (camera) {Camera Input};
\node[data, below=0.5cm of camera] (frame) {RGB Frame};

% Preprocessing
\node[module, right=1.5cm of camera] (preproc) {Preprocessing\\Module};
\node[title, above=0.1cm of preproc] {Frame Processing};
\node[data, below=0.5cm of preproc] (resized) {Resized \& Normalized Frame};

% Detection
\node[module, right=1.5cm of preproc] (yolo) {YOLOv8\\Detection Model};
\node[title, above=0.1cm of yolo] {Object Detection};
\node[data, below=0.5cm of yolo] (detections) {Door \& Obstacle\\Detections};

% Depth Estimation
\node[module, right=1.5cm of yolo] (depth) {Monocular Depth\\Estimation};
\node[title, above=0.1cm of depth] {Distance Calculation};
\node[data, below=0.5cm of depth] (distances) {Object Distances};

% Decision Module
\node[module, right=1.5cm of depth] (decision) {Navigation\\Decision Module};
\node[title, above=0.1cm of decision] {Path Planning};
\node[data, below=0.5cm of decision] (commands) {Navigation\\Commands};

% Output
\node[box, fill=orange!10, right=1.5cm of decision] (output) {Voice Guidance\\Output};

% Arrows
\draw[arrow] (camera) -- (preproc);
\draw[arrow] (frame) -- (preproc);
\draw[arrow] (preproc) -- (yolo);
\draw[arrow] (resized) -- (yolo);
\draw[arrow] (yolo) -- (depth);
\draw[arrow] (detections) -- (depth);
\draw[arrow] (depth) -- (decision);
\draw[arrow] (distances) -- (decision);
\draw[arrow] (decision) -- (output);
\draw[arrow] (commands) -- (output);

% Feedback loop
\draw[arrow] (output) to[out=135, in=45, looseness=1.5] (camera);

% Additional components
\node[data, below=2.5cm of yolo] (dataset) {Custom Door\\Dataset};
\draw[arrow] (dataset) -- (yolo);

\node[data, below=2.5cm of depth] (geometric) {Geometric\\Constraints};
\draw[arrow] (geometric) -- (depth);

\node[data, below=2.5cm of decision] (user) {User\\Preferences};
\draw[arrow] (user) -- (decision);

\end{tikzpicture}
\caption{Vision Guard system architecture. The system processes camera input through a pipeline of preprocessing, object detection, depth estimation, and navigation decision-making, ultimately providing voice guidance to the user. Custom datasets and geometric constraints enhance the accuracy of door detection and distance estimation.}
\label{fig:system_architecture}
\end{figure*}
