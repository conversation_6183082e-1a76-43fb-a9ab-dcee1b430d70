% This file contains the algorithm pseudocode for the Vision Guard system
% It can be included in the main paper using \input{algorithm.tex}

\begin{algorithm}
\caption{Vision Guard Door Detection and Navigation}
\begin{algorithmic}[1]
\Procedure{VisionGuardProcess}{$frame$}
    \State $detections \gets \text{YOLOv8Detect}(frame)$
    \State $depthMap \gets \text{EstimateDepth}(frame)$
    \State $doors \gets \emptyset$, $obstacles \gets \emptyset$
    
    \For{each $detection$ in $detections$}
        \If{$detection.class \in \text{DoorClasses}$}
            \State $distance \gets \text{CalculateDistance}(detection, depthMap)$
            \State $direction \gets \text{CalculateDirection}(detection)$
            \State $doors \gets doors \cup \{(detection, distance, direction)\}$
        \ElsIf{$detection.class \in \text{ObstacleClasses}$}
            \State $distance \gets \text{CalculateDistance}(detection, depthMap)$
            \State $direction \gets \text{CalculateDirection}(detection)$
            \State $obstacles \gets obstacles \cup \{(detection, distance, direction)\}$
        \EndIf
    \EndFor
    
    \State $prioritizedObjects \gets \text{PrioritizeObjects}(doors, obstacles)$
    \State $voiceCommands \gets \emptyset$
    
    \For{each $(object, distance, direction)$ in $prioritizedObjects$}
        \If{$object.class \in \text{DoorClasses}$}
            \State $command \gets \text{FormatDoorCommand}(object, distance, direction)$
        \Else
            \State $command \gets \text{FormatObstacleCommand}(object, distance, direction)$
        \EndIf
        \State $voiceCommands \gets voiceCommands \cup \{command\}$
    \EndFor
    
    \State $\text{SpeakCommands}(voiceCommands)$
    \State \Return $\{doors, obstacles, depthMap\}$
\EndProcedure

\Procedure{CalculateDistance}{$detection, depthMap$}
    \State $roi \gets \text{GetRegionOfInterest}(detection, depthMap)$
    \State $depthValues \gets \text{ExtractDepthValues}(roi)$
    \State $filteredValues \gets \text{RemoveOutliers}(depthValues)$
    
    \If{$detection.class \in \text{DoorClasses}$}
        \State $standardWidth \gets 0.85$ \Comment{Standard door width in meters}
        \State $apparentWidth \gets detection.width$
        \State $geometricDistance \gets \text{CalculateGeometricDistance}(standardWidth, apparentWidth)$
        \State $finalDistance \gets \text{CombineEstimates}(filteredValues, geometricDistance)$
    \Else
        \State $finalDistance \gets \text{MedianDepth}(filteredValues)$
    \EndIf
    
    \State \Return $finalDistance$
\EndProcedure

\Procedure{PrioritizeObjects}{$doors, obstacles$}
    \State $criticalObstacles \gets \{o \in obstacles \mid o.distance < 1.0\}$
    \State $warningObstacles \gets \{o \in obstacles \mid 1.0 \leq o.distance < 2.0\}$
    \State $notificationObstacles \gets \{o \in obstacles \mid 2.0 \leq o.distance < 3.0\}$
    \State $nearbyDoors \gets \{d \in doors \mid d.distance < 5.0\}$
    
    \State $prioritizedList \gets criticalObstacles$
    \State $prioritizedList \gets prioritizedList \cup warningObstacles$
    \State $prioritizedList \gets prioritizedList \cup nearbyDoors$
    \State $prioritizedList \gets prioritizedList \cup notificationObstacles$
    
    \State \Return $prioritizedList$
\EndProcedure
\end{algorithmic}
\end{algorithm}
