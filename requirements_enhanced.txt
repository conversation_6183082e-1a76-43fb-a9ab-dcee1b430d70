# Enhanced VisionGuard Requirements
# ===================================

# Core Computer Vision
opencv-python>=4.8.0
ultralytics>=8.0.0
torch>=2.0.0
torchvision>=0.15.0
numpy>=1.24.0
Pillow>=10.0.0

# Text-to-Speech and Speech Recognition
pyttsx3>=2.90
SpeechRecognition>=3.10.0
pyaudio>=0.2.11

# GUI Framework
tkinter  # Usually included with Python

# Logging and Utilities
logging  # Built-in Python module

# Optional: For better audio support on different platforms
# Windows
# pywin32>=306  # Uncomment for Windows

# Linux
# espeak>=1.48  # Uncomment for Linux TTS support
# python3-espeak  # Uncomment for Linux

# macOS
# py-espeak>=0.71  # Uncomment for macOS

# Additional dependencies for enhanced features
scipy>=1.11.0  # For advanced signal processing
scikit-image>=0.21.0  # For image analysis
matplotlib>=3.7.0  # For debugging visualizations (optional)

# Development and Testing (optional)
pytest>=7.4.0
pytest-cov>=4.1.0
black>=23.7.0
flake8>=6.0.0
