# Makefile for compiling the Vision Guard IEEE paper

# The main LaTeX file
MAIN = vision_guard_paper

# Commands
LATEX = pdflatex
BIBTEX = bibtex
LATEXMK = latexmk

# Default target: compile the paper using latexmk
all: $(MAIN).pdf

# Compile using latexmk (recommended)
$(MAIN).pdf: $(MAIN).tex ../references/references.bib
	$(LATEXMK) -pdf $(MAIN).tex

# Alternative compilation method using pdflatex and bibtex directly
manual: $(MAIN).tex ../references/references.bib
	$(LATEX) $(MAIN).tex
	$(BIBTEX) $(MAIN)
	$(LATEX) $(MAIN).tex
	$(LATEX) $(MAIN).tex

# Clean up auxiliary files but keep the PDF
clean:
	rm -f *.aux *.log *.out *.toc *.lof *.lot *.bbl *.blg *.fls *.fdb_latexmk *.synctex.gz

# Clean up everything including the PDF
cleanall: clean
	rm -f $(MAIN).pdf

# Help target
help:
	@echo "Available targets:"
	@echo "  all       - Compile the paper using latexmk (default)"
	@echo "  manual    - Compile the paper using pdflatex and bibtex directly"
	@echo "  clean     - Remove auxiliary files but keep the PDF"
	@echo "  cleanall  - Remove all generated files including the PDF"
	@echo "  help      - Display this help message"

.PHONY: all manual clean cleanall help
