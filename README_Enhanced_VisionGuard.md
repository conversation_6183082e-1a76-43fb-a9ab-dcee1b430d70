# Enhanced VisionGuard Navigation System

## 🎯 Overview

Enhanced VisionGuard is an advanced navigation assistance system specifically designed for blind and visually impaired users. It provides real-time door detection, voice-guided navigation, and comprehensive safety features.

## ✨ Key Features

### 🚪 Advanced Door Detection
- **Smart State Recognition**: Detects open, closed, and unknown door states
- **Multi-Method Analysis**: Uses 6 different analysis techniques for accuracy
- **Reference Learning**: Learns from your environment using sample images
- **Fallback Logic**: Handles cases where door state cannot be determined

### 🗣️ Intelligent Voice Guidance
- **Timed Instructions**: Provides guidance every 3 seconds during navigation
- **Step-by-Step Directions**: Clear, actionable navigation commands
- **State Announcements**: Informs about door state (open/closed)
- **Distance Information**: Provides accurate distance measurements

### 🎛️ User-Friendly Interface
- **Large Control Buttons**: Easy-to-use interface with accessibility in mind
- **Keyboard Shortcuts**: Quick access via keyboard commands
- **Visual Feedback**: Color-coded displays for different states
- **Status Updates**: Real-time system status information

### 🛡️ Safety Features
- **Emergency Stop**: Instant navigation halt with priority announcement
- **Obstacle Detection**: Warns about objects in the path
- **Lost Door Recovery**: Guidance when door is no longer visible
- **Performance Monitoring**: System health checks and warnings

### 🎤 Voice Commands
- **Hands-Free Operation**: Control the system using voice commands
- **Natural Language**: Understands common phrases and commands
- **Help System**: Built-in command reference and assistance

## 🚀 Quick Start

### Installation

1. **Install Python Dependencies**:
   ```bash
   pip install -r requirements_enhanced.txt
   ```

2. **Install Audio Dependencies** (Platform-specific):
   
   **Windows**:
   ```bash
   pip install pywin32
   ```
   
   **Linux**:
   ```bash
   sudo apt-get install espeak espeak-data libespeak-dev
   pip install espeak
   ```
   
   **macOS**:
   ```bash
   brew install espeak
   pip install py-espeak
   ```

3. **Download YOLO Model** (if not already present):
   The system will automatically download the YOLOv8 model on first run.

### Running the System

```bash
python vision_guard_enhanced.py
```

## 🎮 Controls

### Interface Buttons
- **🚀 START NAVIGATION**: Begin door navigation
- **🛑 STOP NAVIGATION**: Stop current navigation
- **🎤 VOICE COMMANDS ON/OFF**: Toggle voice command recognition
- **⚠️ EMERGENCY STOP**: Immediate halt with safety announcement

### Keyboard Shortcuts
- **SPACE**: Start Navigation
- **S**: Stop Navigation
- **V**: Toggle Voice Commands
- **E**: Emergency Stop
- **Q**: Quit Application

### Voice Commands
- **"Start navigation"** / **"Begin navigation"** / **"Find door"**
- **"Stop navigation"** / **"Stop"** / **"Halt"**
- **"Emergency stop"** / **"Emergency"**
- **"Where is the door?"** / **"Door location"**
- **"What's in front of me?"** / **"Obstacles"**
- **"Help"** / **"Commands"**
- **"Status"**

## 🔧 Configuration

### Door Detection Settings
```python
# Distance thresholds (in meters)
close_door_threshold = 1.5          # When to announce "door reached"
obstacle_warning_threshold = 2.0    # When to warn about obstacles

# Voice guidance timing
voice_guidance_interval = 3.0       # Seconds between guidance messages

# Door state detection
door_state_confidence_threshold = 0.7  # Minimum confidence for state detection
```

### Adding Reference Images
1. Place open door images in the `Open Door` folder
2. Supported formats: JPG, JPEG, PNG, BMP
3. More reference images = better detection accuracy

## 🎯 Usage Scenarios

### Basic Navigation
1. **Start the system**: Run `python vision_guard_enhanced.py`
2. **Position yourself**: Face the general direction of the door
3. **Start navigation**: Press SPACE or say "Start navigation"
4. **Follow guidance**: Listen to voice instructions and move slowly
5. **Reach destination**: System will announce when you reach the door

### Voice-Only Operation
1. **Activate voice commands**: Press V or click the voice button
2. **Use voice commands**: Say "Start navigation" to begin
3. **Get status updates**: Ask "Where is the door?" or "What's in front of me?"
4. **Stop safely**: Say "Stop navigation" or "Emergency stop"

### Emergency Situations
- **Immediate stop**: Press E or say "Emergency stop"
- **Lost door**: System will guide you to relocate the door
- **Obstacles**: System warns about objects in your path
- **System issues**: Check the log file for troubleshooting

## 🔍 Troubleshooting

### Common Issues

**Camera not detected**:
- Ensure camera is connected and not used by other applications
- Try different camera indices in the code (0, 1, 2, etc.)

**Voice commands not working**:
- Check microphone permissions
- Ensure microphone is not muted
- Test with built-in speech recognition tools

**Poor door detection**:
- Improve lighting conditions
- Add more reference images to "Open Door" folder
- Adjust confidence thresholds in the code

**Audio issues**:
- Install platform-specific audio dependencies
- Check system audio settings
- Verify TTS engine installation

### Log Files
Check `visionguard.log` for detailed error messages and system status.

## 🛠️ Advanced Features

### Custom Door Types
The system can be trained for specific door types by adding reference images and adjusting detection parameters.

### Integration Options
- Can be integrated with smart home systems
- Supports external sensors and cameras
- API endpoints for remote control

### Performance Optimization
- Adjustable frame rates for different hardware
- Configurable detection intervals
- Memory usage optimization options

## 📞 Support

For issues, improvements, or questions:
1. Check the troubleshooting section
2. Review log files for error details
3. Ensure all dependencies are properly installed
4. Test with different lighting and camera positions

## 🔄 Updates and Improvements

The Enhanced VisionGuard system is designed to be extensible and can be enhanced with:
- Machine learning model training for specific environments
- Integration with depth cameras for better distance estimation
- Support for multiple door types (sliding, revolving, etc.)
- Mobile app integration for remote monitoring

---

**Note**: This system is designed as an assistive tool. Users should always exercise caution and use additional safety measures when navigating unfamiliar environments.
