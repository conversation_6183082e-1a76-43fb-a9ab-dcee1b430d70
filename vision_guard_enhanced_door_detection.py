
"""
Enhanced VisionGuard with Advanced Door State Detection
======================================================

Features:
- Advanced door detection (open/closed/unknown)
- Reference image comparison for door state detection
- Timed voice guidance with 3-second intervals
- Comprehensive UI with control buttons
- Corner case handling and safety features
- Emergency stop and obstacle warnings
- Voice command system with improved feedback

Author: Enhanced by AI Assistant
Version: 3.0
"""

import cv2
import numpy as np
import torch
from ultralytics import YOLO
import pyttsx3
import speech_recognition as sr
import threading
import time
import os
import platform
import tkinter as tk
from tkinter import ttk, messagebox
from PIL import Image, ImageTk
import queue
import logging
from datetime import datetime
from collections import deque
import json
import math
import subprocess
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.feature_extraction.text import TfidfVectorizer
import pickle

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('visionguard_enhanced.log'),
        logging.StreamHandler()
    ]
)

class EnhancedVisionGuardDoorDetection:
    """Enhanced VisionGuard Navigation System with Advanced Door State Detection"""
    
    def __init__(self,
                 model_path='yolov8n.pt',  # Use default YOLO model
                 camera_id=0,
                 confidence=0.4,
                 door_class_names=None,
                 obstacle_class_names=None,
                 frame_width=640,
                 frame_height=480,
                 focal_length=800,
                 known_door_width=0.9):  # meters
        
        # Initialize YOLO model
        logging.info(f"Loading YOLO model: {model_path}")
        try:
            self.model = YOLO(model_path)
            logging.info(f"YOLO model loaded successfully: {model_path}")
        except Exception as e:
            logging.error(f"Failed to load YOLO model: {e}")
            raise
        
        # Door detection configuration
        if door_class_names is None:
            self.door_class_names = ["door", "Door", "DOOR"]
        else:
            self.door_class_names = door_class_names
            
        # Obstacle detection configuration
        if obstacle_class_names is None:
            # Common COCO classes that could be obstacles
            self.obstacle_class_names = [
                'person', 'bicycle', 'car', 'motorcycle', 'chair', 'couch',
                'potted plant', 'bed', 'dining table', 'toilet', 'tv', 'laptop',
                'mouse', 'remote', 'keyboard', 'cell phone', 'microwave',
                'oven', 'toaster', 'sink', 'refrigerator', 'book', 'clock',
                'vase', 'scissors', 'teddy bear', 'hair drier', 'toothbrush'
            ]
        else:
            self.obstacle_class_names = obstacle_class_names
        
        # Camera configuration
        self.camera_id = camera_id
        self.frame_width = frame_width
        self.frame_height = frame_height
        self.confidence = confidence
        
        # Distance estimation parameters
        self.focal_length = focal_length
        self.known_door_width = known_door_width
        self.close_door_threshold = 1.5  # meters
        self.obstacle_warning_threshold = 2.0  # meters
        
        # Enhanced door state detection
        self.door_state_history = deque(maxlen=10)
        self.door_state_confidence_threshold = 0.7
        self.reference_images = []
        self.load_reference_images()
        
        # Navigation state
        self.navigating = False
        self.door_detected = False
        self.door_distance = None
        self.door_center_x = None
        self.door_state = "unknown"  # open, closed, unknown
        self.obstacles = []
        
        # Voice guidance system - initialize queue first
        self.voice_guidance_interval = 3.0  # seconds
        self.last_voice_time = 0
        self.voice_queue = queue.Queue()
        self.speaking = False
        self.setup_voice_system()
        
        # UI and control system
        self.ui_root = None
        self.video_label = None
        self.control_frame = None
        self.status_var = None
        self.setup_ui()
        
        # Camera system
        self.cap = None
        self.setup_camera()
        
        # Safety and monitoring
        self.emergency_stop = False
        self.last_door_seen_time = time.time()
        self.door_lost_threshold = 5.0  # seconds
        
        # State tracking for announcements
        self.door_announced = False
        self.obstacle_announced = False
        self.door_state_announced = False
        self.last_spoken_direction = None
        self.last_spoken_distance = None
        
        # Voice command system
        self.listening = False
        self.listen_thread = None
        self.setup_voice_commands()
        
        # Processing control
        self.running = False
        self.process_thread = None
        
        logging.info("Enhanced VisionGuard system initialized successfully")

    def load_reference_images(self):
        """Load reference images for door state detection"""
        reference_dir = "Open Door"
        
        if not os.path.exists(reference_dir):
            logging.warning(f"Reference directory '{reference_dir}' not found")
            return
        
        try:
            image_files = [f for f in os.listdir(reference_dir) 
                          if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))]
            
            for img_file in image_files:
                img_path = os.path.join(reference_dir, img_file)
                try:
                    img = cv2.imread(img_path)
                    if img is not None:
                        # Resize to standard size for comparison
                        img_resized = cv2.resize(img, (224, 224))
                        self.reference_images.append(img_resized)
                except Exception as e:
                    logging.warning(f"Failed to load reference image {img_path}: {e}")
            
            logging.info(f"Loaded {len(self.reference_images)} reference images for door state detection")
            
        except Exception as e:
            logging.error(f"Error loading reference images: {e}")

    def setup_voice_system(self):
        """Initialize text-to-speech system"""
        try:
            self.tts_engine = pyttsx3.init()
            
            # Configure voice settings
            voices = self.tts_engine.getProperty('voices')
            if voices:
                # Prefer female voice for better clarity
                for voice in voices:
                    if 'female' in voice.name.lower() or 'zira' in voice.name.lower():
                        self.tts_engine.setProperty('voice', voice.id)
                        break
            
            # Set speech rate and volume
            self.tts_engine.setProperty('rate', 180)  # Slightly slower for clarity
            self.tts_engine.setProperty('volume', 0.9)
            
            # Start voice processing thread
            self.voice_thread = threading.Thread(target=self._process_voice_queue, daemon=True)
            self.voice_thread.start()
            
            logging.info("Voice system initialized successfully")
            
        except Exception as e:
            logging.error(f"Failed to initialize voice system: {e}")
            self.tts_engine = None

    def setup_voice_commands(self):
        """Initialize speech recognition for voice commands"""
        try:
            self.recognizer = sr.Recognizer()
            self.microphone = sr.Microphone()
            
            # Adjust for ambient noise
            with self.microphone as source:
                self.recognizer.adjust_for_ambient_noise(source, duration=1)
            
            logging.info("Voice command system initialized successfully")
            
        except Exception as e:
            logging.error(f"Failed to initialize voice commands: {e}")
            self.microphone = None

    def setup_camera(self):
        """Initialize camera system"""
        try:
            self.cap = cv2.VideoCapture(self.camera_id)
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.frame_width)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.frame_height)
            
            # Test camera
            ret, frame = self.cap.read()
            if not ret:
                raise RuntimeError("Camera opened but failed to read frame")
            
            logging.info("Camera system initialized successfully")
            
        except Exception as e:
            logging.error(f"Failed to initialize camera: {e}")
            if self.cap:
                self.cap.release()
                self.cap = None

    def setup_ui(self):
        """Initialize user interface"""
        try:
            self.ui_root = tk.Tk()
            self.ui_root.title("Enhanced VisionGuard - Door Detection System")
            self.ui_root.geometry("1000x700")
            self.ui_root.configure(bg='#2c3e50')
            
            # Create main frame
            main_frame = ttk.Frame(self.ui_root)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            # Video display frame
            video_frame = ttk.LabelFrame(main_frame, text="Camera Feed", padding=10)
            video_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
            
            self.video_label = ttk.Label(video_frame)
            self.video_label.pack()
            
            # Control frame
            self.control_frame = ttk.LabelFrame(main_frame, text="Navigation Controls", padding=10)
            self.control_frame.pack(fill=tk.X, pady=(0, 10))
            
            # Navigation buttons
            nav_frame = ttk.Frame(self.control_frame)
            nav_frame.pack(fill=tk.X, pady=(0, 10))
            
            self.start_btn = ttk.Button(nav_frame, text="🚀 START NAVIGATION", 
                                       command=self.start_navigation,
                                       style='Success.TButton')
            self.start_btn.pack(side=tk.LEFT, padx=(0, 10))
            
            self.stop_btn = ttk.Button(nav_frame, text="🛑 STOP NAVIGATION", 
                                      command=self.stop_navigation,
                                      style='Danger.TButton')
            self.stop_btn.pack(side=tk.LEFT, padx=(0, 10))
            
            self.emergency_btn = ttk.Button(nav_frame, text="🚨 EMERGENCY STOP", 
                                           command=self.emergency_stop_action,
                                           style='Warning.TButton')
            self.emergency_btn.pack(side=tk.LEFT, padx=(0, 10))
            
            self.voice_btn = ttk.Button(nav_frame, text="🎤 VOICE COMMANDS ON", 
                                       command=self.toggle_voice_commands)
            self.voice_btn.pack(side=tk.LEFT)
            
            # Status display
            status_frame = ttk.Frame(self.control_frame)
            status_frame.pack(fill=tk.X)
            
            ttk.Label(status_frame, text="Status:", font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
            self.status_var = tk.StringVar(value="System Ready")
            status_label = ttk.Label(status_frame, textvariable=self.status_var, 
                                   font=('Arial', 10), foreground='green')
            status_label.pack(side=tk.LEFT, padx=(10, 0))
            
            logging.info("User interface initialized successfully")

        except Exception as e:
            logging.error(f"Failed to initialize UI: {e}")
            raise

    def _process_voice_queue(self):
        """Process voice messages in queue with 3-second intervals"""
        while True:
            try:
                message = self.voice_queue.get(timeout=0.1)
                self.speaking = True

                if self.tts_engine:
                    self.tts_engine.say(message)
                    self.tts_engine.runAndWait()
                else:
                    # Fallback for non-Windows systems
                    if platform.system() != 'Windows':
                        subprocess.run(['espeak', '-s', '180', '-a', '200', message],
                                     capture_output=True)

                self.speaking = False
                self.voice_queue.task_done()

                # Enforce 3-second interval between voice messages
                time.sleep(3.0)

            except queue.Empty:
                time.sleep(0.1)
            except Exception as e:
                logging.error(f"Voice processing error: {e}")
                self.speaking = False

    def speak(self, text, priority=False):
        """Add message to voice queue with priority handling"""
        if priority:
            # Clear queue for priority messages
            while not self.voice_queue.empty():
                try:
                    self.voice_queue.get_nowait()
                    self.voice_queue.task_done()
                except queue.Empty:
                    break

        self.voice_queue.put(text)
        logging.info(f"Voice: {text}")

    def detect_door_state(self, door_region):
        """
        Advanced door state detection using multiple methods:
        1. Reference image comparison for open doors
        2. Geometric analysis for door-frame alignment
        3. Edge detection for door boundaries
        """
        if door_region is None or door_region.size == 0:
            return "unknown", 0.0

        try:
            # Resize door region for comparison
            door_resized = cv2.resize(door_region, (224, 224))

            # Method 1: Reference image comparison
            open_door_score = self._compare_with_reference_images(door_resized)

            # Method 2: Geometric analysis for closed door detection
            closed_door_score = self._analyze_door_frame_alignment(door_region)

            # Method 3: Edge analysis
            edge_score = self._analyze_door_edges(door_region)

            # Combine scores with weights
            open_confidence = (open_door_score * 0.6 + edge_score * 0.4)
            closed_confidence = (closed_door_score * 0.7 + (1.0 - edge_score) * 0.3)

            # Determine door state
            if open_confidence > self.door_state_confidence_threshold and open_confidence > closed_confidence:
                return "open", open_confidence
            elif closed_confidence > self.door_state_confidence_threshold and closed_confidence > open_confidence:
                return "closed", closed_confidence
            else:
                return "unknown", max(open_confidence, closed_confidence)

        except Exception as e:
            logging.error(f"Door state detection error: {e}")
            return "unknown", 0.0

    def _compare_with_reference_images(self, door_image):
        """Compare door image with reference open door images"""
        if not self.reference_images:
            return 0.0

        try:
            # Convert to grayscale for comparison
            door_gray = cv2.cvtColor(door_image, cv2.COLOR_BGR2GRAY)

            # Calculate histogram
            door_hist = cv2.calcHist([door_gray], [0], None, [256], [0, 256])

            similarities = []

            for ref_img in self.reference_images[:20]:  # Use first 20 for speed
                ref_gray = cv2.cvtColor(ref_img, cv2.COLOR_BGR2GRAY)
                ref_hist = cv2.calcHist([ref_gray], [0], None, [256], [0, 256])

                # Calculate correlation
                correlation = cv2.compareHist(door_hist, ref_hist, cv2.HISTCMP_CORREL)
                similarities.append(correlation)

            # Return average of top 5 similarities
            similarities.sort(reverse=True)
            top_similarities = similarities[:5]

            return np.mean(top_similarities) if top_similarities else 0.0

        except Exception as e:
            logging.error(f"Reference comparison error: {e}")
            return 0.0

    def _analyze_door_frame_alignment(self, door_region):
        """Analyze if door is aligned with door frame (closed door indicator)"""
        try:
            gray = cv2.cvtColor(door_region, cv2.COLOR_BGR2GRAY)

            # Apply edge detection
            edges = cv2.Canny(gray, 50, 150)

            # Find vertical lines (door edges)
            lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=50,
                                   minLineLength=30, maxLineGap=10)

            if lines is None:
                return 0.0

            vertical_lines = []
            for line in lines:
                x1, y1, x2, y2 = line[0]
                angle = np.abs(np.arctan2(y2 - y1, x2 - x1) * 180 / np.pi)

                # Check if line is vertical (door edge)
                if angle > 80 and angle < 100:
                    vertical_lines.append(line[0])

            # If we have strong vertical lines, likely a closed door
            alignment_score = min(len(vertical_lines) / 4.0, 1.0)

            return alignment_score

        except Exception as e:
            logging.error(f"Door frame alignment analysis error: {e}")
            return 0.0

    def _analyze_door_edges(self, door_region):
        """Analyze edge patterns to determine door state"""
        try:
            gray = cv2.cvtColor(door_region, cv2.COLOR_BGR2GRAY)

            # Apply Gaussian blur
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)

            # Edge detection
            edges = cv2.Canny(blurred, 30, 100)

            # Calculate edge density
            edge_density = np.sum(edges > 0) / edges.size

            # Open doors typically have more complex edge patterns
            # Closed doors have simpler, more regular patterns

            # Normalize edge density (higher = more likely open)
            edge_score = min(edge_density * 10, 1.0)

            return edge_score

        except Exception as e:
            logging.error(f"Edge analysis error: {e}")
            return 0.0

    def estimate_distance(self, bbox_width):
        """Estimate distance to door based on bounding box width"""
        if bbox_width <= 0:
            return float('inf')

        distance = (self.known_door_width * self.focal_length) / bbox_width
        return max(0.1, distance)  # Minimum distance of 10cm

    def get_direction_guidance(self, door_center_x):
        """Get direction guidance based on door position"""
        frame_center = self.frame_width // 2
        threshold = 80  # Increased threshold for better stability

        if door_center_x < frame_center - threshold:
            return "left"
        elif door_center_x > frame_center + threshold:
            return "right"
        else:
            return "forward"

    def process_frame(self, frame):
        """Process video frame for door and obstacle detection"""
        try:
            # Run YOLO detection
            results = self.model(frame, conf=self.confidence, verbose=False)

            # Initialize detection variables
            door_detected = False
            door_distance = None
            door_center_x = None
            door_bbox = None
            door_state = "unknown"
            door_state_confidence = 0.0
            obstacles = []

            # Process detections
            for result in results:
                if result.boxes is not None:
                    for box in result.boxes:
                        # Get detection info
                        cls_id = int(box.cls[0])
                        cls_name = self.model.names[cls_id]
                        confidence = float(box.conf[0])
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy().astype(int)

                        # Check if it's a door
                        if any(door_name.lower() in cls_name.lower() for door_name in self.door_class_names):
                            if not door_detected or (x2 - x1) > (door_bbox[2] - door_bbox[0]):
                                door_detected = True
                                door_bbox = (x1, y1, x2, y2)
                                door_center_x = (x1 + x2) // 2
                                door_distance = self.estimate_distance(x2 - x1)

                                # Extract door region for state analysis
                                door_region = frame[y1:y2, x1:x2]
                                door_state, door_state_confidence = self.detect_door_state(door_region)

                        # Check for obstacles
                        elif any(obs_name.lower() in cls_name.lower() for obs_name in self.obstacle_class_names):
                            obstacle_distance = self.estimate_distance(x2 - x1)
                            obstacles.append({
                                'bbox': (x1, y1, x2, y2),
                                'class': cls_name,
                                'confidence': confidence,
                                'distance': obstacle_distance,
                                'center_x': (x1 + x2) // 2,
                                'center_y': (y1 + y2) // 2
                            })

            # Update door state history
            if door_detected:
                self.door_state_history.append((door_state, door_state_confidence))
                self.last_door_seen_time = time.time()

            # Get stable door state from history
            if len(self.door_state_history) >= 3:
                recent_states = list(self.door_state_history)[-5:]
                state_counts = {}
                for state, conf in recent_states:
                    if state not in state_counts:
                        state_counts[state] = []
                    state_counts[state].append(conf)

                # Find most confident stable state
                best_state = "unknown"
                best_confidence = 0.0
                for state, confidences in state_counts.items():
                    avg_confidence = np.mean(confidences)
                    if len(confidences) >= 2 and avg_confidence > best_confidence:
                        best_state = state
                        best_confidence = avg_confidence

                door_state = best_state
                door_state_confidence = best_confidence

            # Create annotated frame
            annotated_frame = frame.copy()

            # Draw door detection
            if door_detected:
                x1, y1, x2, y2 = door_bbox

                # Color based on door state
                if door_state == "open":
                    color = (0, 255, 0)  # Green for open
                    state_text = f"OPEN DOOR ({door_state_confidence:.2f})"
                elif door_state == "closed":
                    color = (0, 0, 255)  # Red for closed
                    state_text = f"CLOSED DOOR ({door_state_confidence:.2f})"
                else:
                    color = (0, 255, 255)  # Yellow for unknown
                    state_text = f"DOOR STATE UNKNOWN"

                # Draw bounding box
                cv2.rectangle(annotated_frame, (x1, y1), (x2, y2), color, 3)

                # Draw distance and state
                cv2.putText(annotated_frame, f"Distance: {door_distance:.1f}m",
                           (x1, y1 - 40), cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
                cv2.putText(annotated_frame, state_text,
                           (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)

                # Draw direction arrow
                direction = self.get_direction_guidance(door_center_x)
                self._draw_direction_arrow(annotated_frame, direction, door_center_x)

            # Draw obstacles
            for obstacle in obstacles:
                x1, y1, x2, y2 = obstacle['bbox']
                cv2.rectangle(annotated_frame, (x1, y1), (x2, y2), (255, 0, 0), 2)
                cv2.putText(annotated_frame, f"{obstacle['class']}: {obstacle['distance']:.1f}m",
                           (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 2)

            # Update system state
            self.door_detected = door_detected
            self.door_distance = door_distance
            self.door_center_x = door_center_x
            self.door_state = door_state
            self.obstacles = obstacles

            return annotated_frame

        except Exception as e:
            logging.error(f"Frame processing error: {e}")
            return frame

    def _draw_direction_arrow(self, frame, direction, door_center_x):
        """Draw navigation arrow on frame"""
        frame_center = self.frame_width // 2
        arrow_start = (frame_center, self.frame_height - 50)
        arrow_color = (0, 255, 255)
        arrow_thickness = 3

        if direction == "left":
            arrow_end = (frame_center - 60, self.frame_height - 50)
            cv2.arrowedLine(frame, arrow_start, arrow_end, arrow_color, arrow_thickness)
            cv2.putText(frame, "TURN LEFT", (frame_center - 120, self.frame_height - 60),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, arrow_color, 2)
        elif direction == "right":
            arrow_end = (frame_center + 60, self.frame_height - 50)
            cv2.arrowedLine(frame, arrow_start, arrow_end, arrow_color, arrow_thickness)
            cv2.putText(frame, "TURN RIGHT", (frame_center + 20, self.frame_height - 60),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, arrow_color, 2)
        else:
            arrow_end = (frame_center, self.frame_height - 110)
            cv2.arrowedLine(frame, arrow_start, arrow_end, arrow_color, arrow_thickness)
            cv2.putText(frame, "GO FORWARD", (frame_center - 80, self.frame_height - 120),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, arrow_color, 2)

    def provide_navigation_guidance(self):
        """Provide voice guidance with 3-second intervals"""
        if not self.navigating or self.emergency_stop:
            return

        current_time = time.time()
        if current_time - self.last_voice_time < self.voice_guidance_interval:
            return

        self.last_voice_time = current_time

        # Check for emergency situations
        if current_time - self.last_door_seen_time > self.door_lost_threshold:
            if not self.door_announced:
                self.speak("Door lost. Please look around carefully.", priority=True)
                self.door_announced = True
            return

        # Check for obstacles in path
        close_obstacles = [obs for obs in self.obstacles
                          if obs['distance'] < self.obstacle_warning_threshold]

        if close_obstacles and not self.obstacle_announced:
            obstacle_names = [obs['class'] for obs in close_obstacles[:2]]
            self.speak(f"Caution! {', '.join(obstacle_names)} detected ahead. Stop and assess.", priority=True)
            self.obstacle_announced = True
            return
        elif not close_obstacles:
            self.obstacle_announced = False

        # Provide door guidance
        if self.door_detected:
            self.door_announced = False

            # Check if reached door
            if self.door_distance < self.close_door_threshold:
                if self.door_state == "open":
                    self.speak("Open door reached. You may proceed through.", priority=True)
                elif self.door_state == "closed":
                    self.speak("Closed door reached. Please open the door to proceed.", priority=True)
                else:
                    self.speak("Door reached. Please check if door is open before proceeding.", priority=True)
                return

            # Provide directional guidance
            direction = self.get_direction_guidance(self.door_center_x)
            distance_text = f"{self.door_distance:.1f} meters"

            # Announce door state if not already announced
            if not self.door_state_announced or self.door_state != getattr(self, '_last_announced_state', None):
                if self.door_state == "open":
                    state_msg = f"Open door detected {distance_text} ahead."
                elif self.door_state == "closed":
                    state_msg = f"Closed door detected {distance_text} ahead."
                else:
                    state_msg = f"Door detected {distance_text} ahead. State unknown."

                self.speak(state_msg)
                self.door_state_announced = True
                self._last_announced_state = self.door_state
                return

            # Provide direction guidance
            if direction != self.last_spoken_direction:
                if direction == "left":
                    self.speak("Turn left to align with door.")
                elif direction == "right":
                    self.speak("Turn right to align with door.")
                else:
                    self.speak(f"Door straight ahead. Continue forward {distance_text}.")

                self.last_spoken_direction = direction

        else:
            if not self.door_announced:
                self.speak("No door detected. Please look around.")
                self.door_announced = True

    def start_navigation(self):
        """Start navigation mode"""
        if not self.navigating:
            self.navigating = True
            self.emergency_stop = False
            self.door_announced = False
            self.obstacle_announced = False
            self.door_state_announced = False
            self.last_voice_time = 0

            self.speak("Navigation started. Moving towards door. Listen for guidance.", priority=True)
            self.status_var.set("Navigation Active")

            # Update UI
            self.start_btn.configure(state='disabled')
            self.stop_btn.configure(state='normal')

            logging.info("Navigation started")

    def stop_navigation(self):
        """Stop navigation mode"""
        if self.navigating:
            self.navigating = False
            self.emergency_stop = False

            self.speak("Navigation stopped.", priority=True)
            self.status_var.set("Navigation Stopped")

            # Update UI
            self.start_btn.configure(state='normal')
            self.stop_btn.configure(state='disabled')

            logging.info("Navigation stopped")

    def emergency_stop_action(self):
        """Emergency stop with immediate voice feedback"""
        self.emergency_stop = True
        self.navigating = False

        self.speak("Emergency stop activated. Please remain stationary.", priority=True)
        self.status_var.set("EMERGENCY STOP")

        # Update UI
        self.start_btn.configure(state='normal')
        self.stop_btn.configure(state='disabled')

        logging.warning("Emergency stop activated")

    def toggle_voice_commands(self):
        """Toggle voice command listening"""
        if not self.listening:
            self.start_voice_listening()
            self.voice_btn.configure(text="🎤 VOICE COMMANDS OFF")
        else:
            self.stop_voice_listening()
            self.voice_btn.configure(text="🎤 VOICE COMMANDS ON")

    def start_voice_listening(self):
        """Start listening for voice commands"""
        if self.microphone and not self.listening:
            self.listening = True
            self.listen_thread = threading.Thread(target=self._voice_command_loop, daemon=True)
            self.listen_thread.start()
            self.speak("Voice commands activated. Say 'navigate to door' to start.")
            logging.info("Voice command listening started")

    def stop_voice_listening(self):
        """Stop listening for voice commands"""
        if self.listening:
            self.listening = False
            if self.listen_thread:
                self.listen_thread.join(timeout=1.0)
            logging.info("Voice command listening stopped")

    def _voice_command_loop(self):
        """Voice command processing loop"""
        while self.listening:
            try:
                with self.microphone as source:
                    audio = self.recognizer.listen(source, timeout=1, phrase_time_limit=3)

                try:
                    command = self.recognizer.recognize_google(audio).lower()
                    logging.info(f"Voice command: {command}")

                    # Process commands
                    if any(phrase in command for phrase in ["navigate to door", "find door", "go to door"]):
                        self.start_navigation()
                    elif "stop" in command or "halt" in command:
                        self.stop_navigation()
                    elif "emergency" in command:
                        self.emergency_stop_action()
                    elif "where is door" in command or "door location" in command:
                        if self.door_detected:
                            direction = self.get_direction_guidance(self.door_center_x)
                            state_info = f"The door is {self.door_state}" if self.door_state != "unknown" else "Door state unknown"
                            self.speak(f"Door is {direction}, {self.door_distance:.1f} meters away. {state_info}")
                        else:
                            self.speak("No door detected in current view.")
                    elif "what do you see" in command:
                        if self.door_detected:
                            self.speak(f"{self.door_state.title()} door detected {self.door_distance:.1f} meters away")
                        if self.obstacles:
                            obstacle_names = [obs['class'] for obs in self.obstacles[:3]]
                            self.speak(f"Also detected: {', '.join(obstacle_names)}")
                        if not self.door_detected and not self.obstacles:
                            self.speak("No doors or obstacles detected in current view.")

                except sr.UnknownValueError:
                    pass  # Ignore unrecognized speech
                except sr.RequestError as e:
                    logging.error(f"Speech recognition error: {e}")

            except Exception as e:
                logging.error(f"Voice command error: {e}")
                time.sleep(0.1)

    def update_ui_frame(self, frame):
        """Update UI with processed frame"""
        try:
            # Convert frame to RGB for tkinter
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

            # Resize frame to fit UI
            display_height = 400
            aspect_ratio = frame.shape[1] / frame.shape[0]
            display_width = int(display_height * aspect_ratio)

            frame_resized = cv2.resize(frame_rgb, (display_width, display_height))

            # Convert to PIL Image and then to PhotoImage
            pil_image = Image.fromarray(frame_resized)
            photo = ImageTk.PhotoImage(pil_image)

            # Update label
            self.video_label.configure(image=photo)
            self.video_label.image = photo  # Keep a reference

        except Exception as e:
            logging.error(f"UI update error: {e}")

    def run(self):
        """Main execution loop"""
        if self.running:
            return

        self.running = True

        # Start processing thread
        self.process_thread = threading.Thread(target=self._processing_loop, daemon=True)
        self.process_thread.start()

        # Start UI loop
        try:
            self.ui_root.mainloop()
        except KeyboardInterrupt:
            logging.info("Interrupted by user")
        finally:
            self.cleanup()

    def _processing_loop(self):
        """Main processing loop for camera and detection"""
        frame_count = 0

        while self.running:
            try:
                if self.cap is None or not self.cap.isOpened():
                    time.sleep(0.1)
                    continue

                # Read frame
                ret, frame = self.cap.read()
                if not ret:
                    logging.warning("Failed to read frame from camera")
                    time.sleep(0.1)
                    continue

                # Process every 2nd frame for performance
                frame_count += 1
                if frame_count % 2 == 0:
                    # Process frame
                    processed_frame = self.process_frame(frame)

                    # Update UI
                    self.ui_root.after(0, self.update_ui_frame, processed_frame)

                    # Provide navigation guidance
                    if self.navigating:
                        self.provide_navigation_guidance()

                # Small delay to prevent excessive CPU usage
                time.sleep(0.03)  # ~30 FPS

            except Exception as e:
                logging.error(f"Processing loop error: {e}")
                time.sleep(0.1)

    def cleanup(self):
        """Clean up resources"""
        logging.info("Cleaning up resources...")

        self.running = False
        self.navigating = False
        self.listening = False

        # Stop voice system
        if hasattr(self, 'tts_engine') and self.tts_engine:
            try:
                self.tts_engine.stop()
            except:
                pass

        # Release camera
        if self.cap:
            self.cap.release()

        # Close UI
        if self.ui_root:
            try:
                self.ui_root.quit()
                self.ui_root.destroy()
            except:
                pass

        # Wait for threads
        if self.process_thread and self.process_thread.is_alive():
            self.process_thread.join(timeout=1.0)

        if self.listen_thread and self.listen_thread.is_alive():
            self.listen_thread.join(timeout=1.0)

        logging.info("Cleanup completed")


def main():
    """Main function to run Enhanced VisionGuard"""
    try:
        # Create enhanced system
        vision_guard = EnhancedVisionGuardDoorDetection(
            model_path='yolov8n.pt',  # Use default YOLO model
            camera_id=0,
            confidence=0.4,
            frame_width=640,
            frame_height=480
        )

        # Run system
        vision_guard.run()

    except Exception as e:
        logging.error(f"System error: {e}")
        print(f"Error: {e}")

    finally:
        # Ensure cleanup
        try:
            cv2.destroyAllWindows()
        except:
            pass


if __name__ == "__main__":
    main()
