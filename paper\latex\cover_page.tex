\documentclass[12pt]{article}
\usepackage[a4paper, margin=1in]{geometry}
\usepackage{graphicx}
\usepackage{xcolor}
\usepackage{times}
\usepackage{hyperref}
\usepackage{authblk}

\hypersetup{
    colorlinks=true,
    linkcolor=blue,
    filecolor=magenta,      
    urlcolor=cyan,
    pdftitle={Vision Guard: A Computer Vision System for Assisting Visually Impaired People},
    pdfauthor={Author Names},
    pdfsubject={Computer Vision, Assistive Technology},
    pdfkeywords={computer vision, assistive technology, object detection, YOLOv8, depth estimation}
}

\begin{document}

\begin{titlepage}
    \centering
    \vspace*{1cm}
    
    \includegraphics[width=0.5\textwidth]{../figures/ieee_logo.png}\\[1cm]
    
    {\LARGE\bfseries IEEE International Conference on\\Computer Vision and Pattern Recognition\\[0.5cm]}
    
    \rule{\linewidth}{0.5mm}\\[0.5cm]
    
    {\Huge\bfseries Vision Guard:\\[0.5cm] 
    \Large A Computer Vision System for Assisting\\Visually Impaired People with Door Detection\\and Navigation\\[0.5cm]}
    
    \rule{\linewidth}{0.5mm}\\[1.5cm]
    
    {\Large\bfseries Research Paper}\\[1cm]
    
    {\large\bfseries Authors: <AUTHORS>
    {\large Author Name 1}\\
    {\large Author Name 2}\\[0.5cm]
    
    {\large\bfseries Affiliation:}\\
    {\large Department of Computer Science}\\
    {\large University Name}\\
    {\large City, Country}\\[0.5cm]
    
    {\large\bfseries Contact:}\\
    {\large \texttt{<EMAIL>}}\\[2cm]
    
    {\large \today}\\[2cm]
    
    \vfill
    
    {\large\bfseries Abstract:}\\[0.2cm]
    {\large This paper presents Vision Guard, a novel computer vision system designed to assist visually impaired individuals in navigating indoor environments by detecting doors and obstacles. The system utilizes a custom-trained YOLOv8 object detection model combined with depth estimation techniques to provide real-time guidance through voice commands. Our approach achieves 94\% detection accuracy and provides distance measurements with an average error of less than 10cm.}
    
\end{titlepage}

\end{document}
