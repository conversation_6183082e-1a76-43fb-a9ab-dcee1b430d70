#!/usr/bin/env python3
"""
Convert YOLOv8 model to ONNX format for Android deployment
"""

import argparse
from ultralytics import <PERSON><PERSON><PERSON>

def parse_args():
    parser = argparse.ArgumentParser(description='Convert YOLOv8 model to ONNX format')
    parser.add_argument('--model', type=str, default='runs/train/yolov8_door_detection/weights/best.pt',
                        help='Path to the YOLOv8 model')
    parser.add_argument('--output', type=str, default='Android_Door_Assistant/app/src/main/assets/door_detection.onnx',
                        help='Output path for the ONNX model')
    parser.add_argument('--imgsz', type=int, default=640,
                        help='Image size for the model')
    parser.add_argument('--half', action='store_true',
                        help='Use half precision (FP16)')
    parser.add_argument('--simplify', action='store_true', default=True,
                        help='Simplify the ONNX model')
    return parser.parse_args()

def main():
    args = parse_args()
    
    print(f"Loading YOLOv8 model from {args.model}...")
    model = YOLO(args.model)
    
    print(f"Converting to ONNX format...")
    model.export(
        format="onnx",
        imgsz=args.imgsz,
        half=args.half,
        simplify=args.simplify,
        opset=12  # Use opset 12 for better compatibility
    )
    
    print(f"Model converted successfully! The ONNX model is saved.")
    print("You can now use this model in your Android application.")

if __name__ == "__main__":
    main()
