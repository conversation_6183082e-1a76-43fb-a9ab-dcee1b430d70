# Figures for Vision Guard Paper

This directory should contain all figures used in the Vision Guard research paper. Below is a list of figures that should be created and added to this directory:

1. `system_architecture.png` - A diagram showing the overall architecture of the Vision Guard system
2. `door_detection_examples.png` - Examples of door detection in various environments
3. `depth_estimation_visualization.png` - Visualization of depth estimation results
4. `detection_performance_graph.png` - Graph showing detection performance metrics
5. `user_study_results.png` - Charts showing results from the user study

## Figure Guidelines for IEEE Papers

When creating figures for IEEE papers, follow these guidelines:

1. **Resolution**: Use high-resolution images (at least 300 dpi)
2. **Format**: Vector formats (PDF, EPS) are preferred for diagrams and charts; use PNG or TIFF for photographs
3. **Size**: Figures should be sized appropriately for the paper (typically column width or page width)
4. **Text in figures**: Use a font size that will be readable when printed (at least 8pt)
5. **Color**: Ensure figures are understandable when printed in grayscale
6. **Captions**: Each figure should have a descriptive caption in the LaTeX document

## Creating Diagrams

For system architecture and workflow diagrams, consider using:
- [Draw.io](https://app.diagrams.net/) (free, web-based)
- [Lucidchart](https://www.lucidchart.com/) (freemium, web-based)
- [Microsoft Visio](https://www.microsoft.com/en-us/microsoft-365/visio/flowchart-software) (paid)

## Creating Charts and Graphs

For performance graphs and result visualizations, consider using:
- [Matplotlib](https://matplotlib.org/) (Python library)
- [ggplot2](https://ggplot2.tidyverse.org/) (R library)
- [Microsoft Excel](https://www.microsoft.com/en-us/microsoft-365/excel) or [Google Sheets](https://www.google.com/sheets/about/) (for simpler charts)

## Example Figure Description

### System Architecture (system_architecture.png)

This figure should illustrate the complete Vision Guard system architecture, including:
- Input processing (camera feed)
- YOLOv8 door detection module
- Depth estimation module
- Obstacle detection module
- Voice guidance system
- Data flow between components

Use different colors or shapes to distinguish between the main components, and include arrows to show the flow of data through the system.
