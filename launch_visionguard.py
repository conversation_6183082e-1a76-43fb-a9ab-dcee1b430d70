#!/usr/bin/env python3
"""
Enhanced VisionGuard Launcher
============================

Simple launcher script for the Enhanced VisionGuard Navigation System.
Provides system checks and easy startup options.
"""

import sys
import os
import subprocess
import platform
import time

def print_banner():
    """Print the application banner"""
    print("=" * 60)
    print("🔍 Enhanced VisionGuard Navigation System")
    print("   Advanced Navigation Assistance for Blind Users")
    print("=" * 60)
    print()

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8 or higher is required")
        print(f"   Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python version: {version.major}.{version.minor}.{version.micro}")
    return True

def check_dependencies():
    """Check if required dependencies are installed"""
    print("Checking dependencies...")
    
    required_packages = [
        'cv2',
        'numpy',
        'torch',
        'ultralytics',
        'pyttsx3',
        'speech_recognition',
        'tkinter',
        'PIL'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'cv2':
                import cv2
            elif package == 'PIL':
                from PIL import Image
            else:
                __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
        print("Please install missing dependencies:")
        print("pip install -r requirements_enhanced.txt")
        return False
    
    return True

def check_camera():
    """Check if camera is available"""
    try:
        import cv2
        cap = cv2.VideoCapture(0)
        if cap.isOpened():
            ret, frame = cap.read()
            cap.release()
            if ret:
                print("✅ Camera is working")
                return True
            else:
                print("⚠️  Camera detected but cannot capture frames")
                return False
        else:
            print("❌ Camera not detected")
            return False
    except Exception as e:
        print(f"❌ Camera check failed: {e}")
        return False

def check_audio():
    """Check if audio system is working"""
    try:
        import pyttsx3
        engine = pyttsx3.init()
        voices = engine.getProperty('voices')
        if voices:
            print(f"✅ Audio system ready ({len(voices)} voices available)")
            return True
        else:
            print("⚠️  Audio system initialized but no voices found")
            return False
    except Exception as e:
        print(f"❌ Audio system check failed: {e}")
        return False

def check_reference_images():
    """Check for reference images"""
    reference_dir = "Open Door"
    if os.path.exists(reference_dir):
        image_files = [f for f in os.listdir(reference_dir) 
                      if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))]
        if image_files:
            print(f"✅ Reference images found ({len(image_files)} images)")
            return True
        else:
            print("⚠️  Reference directory exists but no images found")
            return False
    else:
        print("⚠️  Reference directory 'Open Door' not found")
        print("   Door state detection will use basic methods only")
        return False

def run_system_check():
    """Run comprehensive system check"""
    print("Running system check...\n")
    
    checks = [
        ("Python Version", check_python_version),
        ("Dependencies", check_dependencies),
        ("Camera", check_camera),
        ("Audio System", check_audio),
        ("Reference Images", check_reference_images)
    ]
    
    results = []
    for check_name, check_func in checks:
        print(f"\n{check_name}:")
        result = check_func()
        results.append((check_name, result))
    
    print("\n" + "=" * 40)
    print("System Check Summary")
    print("=" * 40)
    
    critical_failed = False
    for check_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{check_name}: {status}")
        
        # Critical checks that must pass
        if check_name in ["Python Version", "Dependencies", "Camera"] and not result:
            critical_failed = True
    
    if critical_failed:
        print("\n❌ Critical system checks failed. Please fix issues before running.")
        return False
    else:
        print("\n✅ System ready to run!")
        return True

def show_menu():
    """Show the main menu"""
    print("\n" + "=" * 40)
    print("Launch Options")
    print("=" * 40)
    print("1. Run System Check")
    print("2. Start Enhanced VisionGuard")
    print("3. Run Test Suite")
    print("4. View README")
    print("5. Install Dependencies")
    print("6. Exit")
    print()

def install_dependencies():
    """Install required dependencies"""
    print("Installing dependencies...")
    
    try:
        # Check if requirements file exists
        if os.path.exists("requirements_enhanced.txt"):
            subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements_enhanced.txt"])
            print("✅ Dependencies installed successfully")
        else:
            print("❌ requirements_enhanced.txt not found")
            print("Please ensure the file is in the current directory")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
    except Exception as e:
        print(f"❌ Installation error: {e}")

def start_visionguard():
    """Start the Enhanced VisionGuard system"""
    print("Starting Enhanced VisionGuard...")
    print("Press Ctrl+C to stop the system\n")
    
    try:
        if os.path.exists("vision_guard_enhanced.py"):
            subprocess.run([sys.executable, "vision_guard_enhanced.py"])
        else:
            print("❌ vision_guard_enhanced.py not found")
            print("Please ensure the file is in the current directory")
    except KeyboardInterrupt:
        print("\n\nSystem stopped by user")
    except Exception as e:
        print(f"❌ Failed to start VisionGuard: {e}")

def run_tests():
    """Run the test suite"""
    print("Running test suite...")
    
    try:
        if os.path.exists("test_enhanced_system.py"):
            subprocess.run([sys.executable, "test_enhanced_system.py"])
        else:
            print("❌ test_enhanced_system.py not found")
    except Exception as e:
        print(f"❌ Failed to run tests: {e}")

def view_readme():
    """Display README information"""
    readme_file = "README_Enhanced_VisionGuard.md"
    
    if os.path.exists(readme_file):
        print("Opening README file...")
        
        # Try to open with default system application
        system = platform.system()
        try:
            if system == "Windows":
                os.startfile(readme_file)
            elif system == "Darwin":  # macOS
                subprocess.run(["open", readme_file])
            else:  # Linux
                subprocess.run(["xdg-open", readme_file])
            
            print("✅ README opened in default application")
        except Exception:
            print("Could not open with default application")
            print(f"Please manually open: {readme_file}")
    else:
        print("❌ README file not found")

def main():
    """Main launcher function"""
    print_banner()
    
    while True:
        show_menu()
        
        try:
            choice = input("Select an option (1-6): ").strip()
            
            if choice == "1":
                run_system_check()
            elif choice == "2":
                start_visionguard()
            elif choice == "3":
                run_tests()
            elif choice == "4":
                view_readme()
            elif choice == "5":
                install_dependencies()
            elif choice == "6":
                print("Goodbye!")
                break
            else:
                print("Invalid option. Please select 1-6.")
                
        except KeyboardInterrupt:
            print("\n\nGoodbye!")
            break
        except Exception as e:
            print(f"Error: {e}")
        
        input("\nPress Enter to continue...")

if __name__ == "__main__":
    main()
