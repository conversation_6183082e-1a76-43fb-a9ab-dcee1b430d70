import cv2
import numpy as np
import time
import threading
import os
import subprocess
import platform
from ultralytics import YOLO
import math
import pyttsx3

class EnhancedRaspberryPiDoorAssistant:
    """
    Enhanced door navigation assistant optimized for Raspberry Pi.
    Features obstacle detection, improved directional guidance, and depth estimation.
    """

    def __init__(self,
                 model_path='runs/train/yolov8_door_detection/weights/best.pt',
                 camera_id=0,
                 confidence=0.4,
                 door_class_names=None,
                 obstacle_class_names=None,
                 frame_width=320*2,  # Reduced resolution for better performance
                 frame_height=240*2,
                 focal_length=800,
                 known_door_width=0.9):  # meters
        """
        Initialize the enhanced door navigation assistant.

        Args:
            model_path (str): Path to the trained YOLOv8 model.
            camera_id (int): Camera device ID.
            confidence (float): Confidence threshold for detections.
            door_class_names (list): List of class names that represent doors.
            obstacle_class_names (list): List of class names that represent obstacles.
            frame_width (int): Width of the camera frame.
            frame_height (int): Height of the camera frame.
            focal_length (float): Focal length of the camera in pixels.
            known_door_width (float): Known width of a door in meters.
        """
        # Initialize YOLO model
        print(f"Loading model from {model_path}...")
        self.model = YOLO(model_path)

        # Set default door class names if not provided
        if door_class_names is None:
            self.door_class_names = ['door', 'Door']
        else:
            self.door_class_names = door_class_names

        # Set default obstacle class names if not provided
        if obstacle_class_names is None:
            self.obstacle_class_names = [
                'person', 'bicycle', 'car', 'motorcycle', 'chair', 'couch',
                'potted plant', 'bed', 'dining table', 'toilet', 'tv', 'laptop',
                'mouse', 'remote', 'keyboard', 'cell phone', 'microwave',
                'oven', 'toaster', 'sink', 'refrigerator', 'book', 'clock',
                'vase', 'scissors', 'teddy bear', 'hair drier', 'toothbrush'
            ]
        else:
            self.obstacle_class_names = obstacle_class_names

        # Initialize camera
        self.camera_id = camera_id
        self.cap = None
        self.frame_width = frame_width
        self.frame_height = frame_height

        # Detection parameters
        self.confidence = confidence

        # Distance estimation parameters
        self.focal_length = focal_length
        self.known_door_width = known_door_width

        # State variables
        self.running = False
        self.navigating = False
        self.door_detected = False
        self.door_distance = None
        self.door_bbox = None
        self.door_center_x = None
        self.obstacles = []

        # Navigation parameters
        self.last_guidance_time = 0
        self.guidance_interval = 2.0  # seconds
        self.close_door_threshold = 1.0  # meters
        self.obstacle_warning_threshold = 1.5  # meters
        self.door_announced = False
        self.obstacle_announced = False

        # Frame processing rate control
        self.process_every_n_frames = 3  # Process every 3rd frame for better performance
        self.frame_count = 0

        # Processing thread
        self.process_thread = None

        # For depth estimation
        self.depth_map = None
        self.use_stereo_depth = False  # Set to True if using stereo camera

        # For smoother guidance
        self.direction_history = []
        self.direction_history_max = 5
        self.last_direction = None

        # Initialize TTS engine
        self.is_windows = platform.system() == 'Windows'
        if self.is_windows:
            self.tts_engine = pyttsx3.init()
            self.tts_engine.setProperty('rate', 150)
            self.tts_engine.setProperty('volume', 0.8)
        else:
            self.tts_engine = None

    def initialize_camera(self):
        """Initialize the camera."""
        if self.cap is not None and self.cap.isOpened():
            self.cap.release()

        self.cap = cv2.VideoCapture(self.camera_id)
        self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.frame_width)
        self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.frame_height)

        if not self.cap.isOpened():
            raise RuntimeError(f"Could not open camera {self.camera_id}")

        # Get actual camera properties
        self.frame_width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        self.frame_height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

        print(f"Camera initialized with resolution: {self.frame_width}x{self.frame_height}")

    def estimate_distance(self, bbox_width):
        """
        Estimate the distance to an object based on its bounding box width.

        Args:
            bbox_width (int): Width of the object's bounding box in pixels.

        Returns:
            float: Estimated distance in meters.
        """
        if bbox_width == 0:
            return float('inf')

        distance = (self.known_door_width * self.focal_length) / bbox_width
        return distance

    def estimate_depth_map(self, frame):
        """
        Estimate depth map using monocular depth estimation.
        This is a simplified implementation - for better results,
        consider using a dedicated depth estimation model.

        Args:
            frame (numpy.ndarray): The image frame.

        Returns:
            numpy.ndarray: Estimated depth map.
        """
        # Convert to grayscale
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

        # Apply Sobel filter to get gradients
        sobelx = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
        sobely = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)

        # Calculate gradient magnitude
        magnitude = np.sqrt(sobelx**2 + sobely**2)

        # Normalize to 0-255
        magnitude = cv2.normalize(magnitude, None, 0, 255, cv2.NORM_MINMAX)

        # Invert (stronger edges are closer)
        depth_map = 255 - magnitude.astype(np.uint8)

        # Apply Gaussian blur to smooth the depth map
        depth_map = cv2.GaussianBlur(depth_map, (15, 15), 0)

        return depth_map

    def speak(self, text, priority=False):
        """
        Speak text using the system's text-to-speech capabilities.
        Uses pyttsx3 on Windows and espeak on Raspberry Pi.

        Args:
            text (str): Text to speak.
            priority (bool): Whether this is a priority message.
        """
        print(f"Speaking: {text}")

        try:
            if self.is_windows:
                # Use pyttsx3 on Windows
                if priority:
                    # Stop any current speech for priority messages
                    self.tts_engine.stop()

                self.tts_engine.say(text)
                self.tts_engine.runAndWait()
            else:
                # Use espeak on Raspberry Pi for better performance
                if priority:
                    # Kill any existing speech processes for priority messages
                    subprocess.run(['pkill', '-f', 'espeak'], stderr=subprocess.DEVNULL)

                subprocess.Popen(['espeak', '-s', '150', '-a', '200', text])
        except Exception as e:
            print(f"TTS error: {e}")
            print(f"Would say: {text}")

    def process_frame(self, frame):
        """
        Process a frame to detect doors and obstacles.

        Args:
            frame (numpy.ndarray): The image frame.

        Returns:
            tuple: Processed frame, door detected flag, door distance, door bbox, door center x, obstacles.
        """
        # Estimate depth map
        self.depth_map = self.estimate_depth_map(frame)

        # Perform detection
        results = self.model(frame, conf=self.confidence)

        # Process results
        door_detected = False
        door_distance = None
        door_bbox = None
        door_center_x = None
        obstacles = []

        # Get detections
        for result in results:
            boxes = result.boxes

            for box in boxes:
                # Get class and confidence
                cls = int(box.cls[0])
                cls_name = self.model.names[cls]
                conf = float(box.conf[0])

                # Get bounding box
                x1, y1, x2, y2 = box.xyxy[0].cpu().numpy().astype(int)
                bbox = (x1, y1, x2, y2)

                # Check if it's a door
                if cls_name.lower() in [name.lower() for name in self.door_class_names]:
                    # If multiple doors, choose the closest/largest one
                    if not door_detected or (x2 - x1) > (door_bbox[2] - door_bbox[0]):
                        door_detected = True
                        door_bbox = bbox
                        door_distance = self.estimate_distance(x2 - x1)
                        door_center_x = (x1 + x2) // 2

                # Check if it's an obstacle
                elif cls_name.lower() in [name.lower() for name in self.obstacle_class_names]:
                    # Calculate distance to obstacle
                    obstacle_width = x2 - x1
                    obstacle_distance = self.estimate_distance(obstacle_width)

                    # Store obstacle information
                    obstacle_info = {
                        'bbox': bbox,
                        'class': cls_name,
                        'confidence': conf,
                        'distance': obstacle_distance,
                        'center_x': (x1 + x2) // 2,
                        'center_y': (y1 + y2) // 2
                    }
                    obstacles.append(obstacle_info)

        # Draw detections on frame
        annotated_frame = results[0].plot()

        # Draw depth map (small overlay)
        depth_small = cv2.resize(self.depth_map, (self.frame_width // 4, self.frame_height // 4))
        depth_color = cv2.applyColorMap(depth_small, cv2.COLORMAP_JET)

        # Place depth map in top-right corner
        h, w = depth_color.shape[:2]
        annotated_frame[10:10+h, self.frame_width-10-w:self.frame_width-10] = depth_color

        # Draw distance if door detected
        if door_detected and door_distance is not None:
            x1, y1, x2, y2 = door_bbox
            center_x = (x1 + x2) // 2
            center_y = (y1 + y2) // 2

            # Draw distance text
            cv2.putText(
                annotated_frame,
                f"{door_distance:.2f}m",
                (center_x, center_y),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.7,
                (0, 255, 0),
                2
            )

            # Draw navigation arrow
            self.draw_navigation_arrow(annotated_frame, door_center_x)

        # Draw obstacles
        for obstacle in obstacles:
            x1, y1, x2, y2 = obstacle['bbox']

            # Draw distance text for close obstacles
            if obstacle['distance'] < self.obstacle_warning_threshold:
                cv2.putText(
                    annotated_frame,
                    f"{obstacle['class']}: {obstacle['distance']:.2f}m",
                    (x1, y1 - 10),
                    cv2.FONT_HERSHEY_SIMPLEX,
                    0.5,
                    (0, 0, 255),
                    2
                )

                # Highlight dangerous obstacles
                cv2.rectangle(
                    annotated_frame,
                    (x1, y1),
                    (x2, y2),
                    (0, 0, 255),
                    2
                )

        # Draw navigation status
        status_text = "Navigating" if self.navigating else "Standby"
        cv2.putText(
            annotated_frame,
            status_text,
            (10, 30),
            cv2.FONT_HERSHEY_SIMPLEX,
            0.7,
            (255, 255, 255),
            2
        )

        return annotated_frame, door_detected, door_distance, door_bbox, door_center_x, obstacles

    def draw_navigation_arrow(self, frame, door_center_x):
        """
        Draw navigation arrow on the frame.

        Args:
            frame (numpy.ndarray): The image frame.
            door_center_x (int): X-coordinate of the door center.
        """
        frame_center_x = self.frame_width // 2
        arrow_length = 50
        arrow_color = (0, 255, 255)
        arrow_thickness = 2

        # Determine direction
        direction = self.get_direction(door_center_x)

        # Draw appropriate arrow
        if direction == "left":
            # Door is to the left
            cv2.arrowedLine(
                frame,
                (frame_center_x, self.frame_height - 30),
                (frame_center_x - arrow_length, self.frame_height - 30),
                arrow_color,
                arrow_thickness
            )
            # Add text
            cv2.putText(
                frame,
                "LEFT",
                (frame_center_x - arrow_length - 40, self.frame_height - 30),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.7,
                arrow_color,
                2
            )
        elif direction == "right":
            # Door is to the right
            cv2.arrowedLine(
                frame,
                (frame_center_x, self.frame_height - 30),
                (frame_center_x + arrow_length, self.frame_height - 30),
                arrow_color,
                arrow_thickness
            )
            # Add text
            cv2.putText(
                frame,
                "RIGHT",
                (frame_center_x + arrow_length + 10, self.frame_height - 30),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.7,
                arrow_color,
                2
            )
        else:
            # Door is centered, draw forward arrow
            cv2.arrowedLine(
                frame,
                (frame_center_x, self.frame_height - 30),
                (frame_center_x, self.frame_height - 30 - arrow_length),
                arrow_color,
                arrow_thickness
            )
            # Add text
            cv2.putText(
                frame,
                "FORWARD",
                (frame_center_x + 10, self.frame_height - 30 - arrow_length),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.7,
                arrow_color,
                2
            )

    def get_direction(self, door_center_x):
        """
        Get direction to the door with smoothing.

        Args:
            door_center_x (int): X-coordinate of the door center.

        Returns:
            str: Direction ('left', 'right', or 'forward').
        """
        frame_center_x = self.frame_width // 2
        threshold = 50  # Threshold for considering door as centered

        # Determine raw direction
        if door_center_x < frame_center_x - threshold:
            raw_direction = "left"
        elif door_center_x > frame_center_x + threshold:
            raw_direction = "right"
        else:
            raw_direction = "forward"

        # Add to history
        self.direction_history.append(raw_direction)
        if len(self.direction_history) > self.direction_history_max:
            self.direction_history.pop(0)

        # Count occurrences
        left_count = self.direction_history.count("left")
        right_count = self.direction_history.count("right")
        forward_count = self.direction_history.count("forward")

        # Determine smoothed direction
        if left_count > right_count and left_count > forward_count:
            smoothed_direction = "left"
        elif right_count > left_count and right_count > forward_count:
            smoothed_direction = "right"
        else:
            smoothed_direction = "forward"

        # Only update if direction has changed or it's the first time
        if self.last_direction != smoothed_direction or self.last_direction is None:
            self.last_direction = smoothed_direction

        return self.last_direction

    def check_path_obstacles(self, door_center_x, obstacles):
        """
        Check if there are obstacles in the path to the door.

        Args:
            door_center_x (int): X-coordinate of the door center.
            obstacles (list): List of detected obstacles.

        Returns:
            tuple: (is_blocked, blocking_obstacle)
        """
        if not obstacles:
            return False, None

        # Define path corridor width
        corridor_width = self.frame_width // 4

        # Get direction to door
        direction = self.get_direction(door_center_x)

        # Check each obstacle
        for obstacle in obstacles:
            obstacle_x = obstacle['center_x']
            obstacle_distance = obstacle['distance']

            # Skip obstacles that are too far
            if obstacle_distance > self.obstacle_warning_threshold:
                continue

            # Check if obstacle is in the path
            if direction == "forward":
                # For forward direction, check if obstacle is in the center corridor
                if abs(obstacle_x - self.frame_width // 2) < corridor_width:
                    return True, obstacle
            elif direction == "left":
                # For left direction, check if obstacle is in the left corridor
                if obstacle_x < self.frame_width // 2 and abs(obstacle_x - door_center_x) < corridor_width:
                    return True, obstacle
            elif direction == "right":
                # For right direction, check if obstacle is in the right corridor
                if obstacle_x > self.frame_width // 2 and abs(obstacle_x - door_center_x) < corridor_width:
                    return True, obstacle

        return False, None

    def provide_guidance(self, door_detected, door_distance, door_center_x, obstacles):
        """
        Provide voice guidance based on door detection and obstacles.

        Args:
            door_detected (bool): Whether a door was detected.
            door_distance (float): Distance to the door in meters.
            door_center_x (int): X-coordinate of the door center.
            obstacles (list): List of detected obstacles.
        """
        if not self.navigating:
            return

        current_time = time.time()
        if current_time - self.last_guidance_time < self.guidance_interval:
            return

        self.last_guidance_time = current_time

        # Check for obstacles in the path first
        if door_detected:
            path_blocked, blocking_obstacle = self.check_path_obstacles(door_center_x, obstacles)

            if path_blocked and not self.obstacle_announced:
                # Announce obstacle
                obstacle_distance = blocking_obstacle['distance']
                obstacle_class = blocking_obstacle['class']
                self.speak(f"Caution! {obstacle_class} in your path, {obstacle_distance:.1f} meters ahead. Stop.", priority=True)
                self.obstacle_announced = True
                return
            elif not path_blocked:
                self.obstacle_announced = False

        if door_detected:
            # Door is detected
            if not self.door_detected:
                # First time detecting the door
                self.speak("Door detected")

            # Check distance to door
            if door_distance < self.close_door_threshold:
                if not self.door_announced:
                    self.speak("You have reached the door. Stop.", priority=True)
                    self.door_announced = True
                return

            # Provide directional guidance
            direction = self.get_direction(door_center_x)

            if direction == "left":
                self.speak("Door is to your left, turn left")
            elif direction == "right":
                self.speak("Door is to your right, turn right")
            else:
                self.speak(f"Door is straight ahead, {door_distance:.1f} meters away")

        elif self.door_detected:
            # Door was detected before but not now
            self.speak("Door lost. Please look around.")
            self.door_announced = False
        else:
            # No door detected
            self.speak("No door detected. Please look around.")

    def process_loop(self):
        """Main processing loop."""
        try:
            self.initialize_camera()

            while self.running:
                # Read frame
                ret, frame = self.cap.read()

                if not ret:
                    print("Error: Failed to capture frame")
                    time.sleep(0.1)
                    continue

                # Process only every n-th frame for better performance
                self.frame_count += 1
                if self.frame_count % self.process_every_n_frames != 0:
                    # Just display the raw frame
                    cv2.imshow("Enhanced Door Navigation Assistant", frame)
                    if cv2.waitKey(1) & 0xFF == ord('q'):
                        break
                    continue

                # Process frame
                processed_frame, door_detected, door_distance, door_bbox, door_center_x, obstacles = self.process_frame(frame)

                # Update state
                self.door_detected = door_detected
                self.door_distance = door_distance
                self.door_bbox = door_bbox
                self.door_center_x = door_center_x
                self.obstacles = obstacles

                # Provide guidance
                if self.navigating:
                    self.provide_guidance(door_detected, door_distance, door_center_x, obstacles)

                # Display frame
                cv2.imshow("Enhanced Door Navigation Assistant", processed_frame)

                # Check for key press
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    break
                elif key == ord('n'):
                    self.start_navigation()
                elif key == ord('s'):
                    self.stop_navigation()

        except Exception as e:
            print(f"Error in processing loop: {e}")
        finally:
            if self.cap is not None:
                self.cap.release()
            cv2.destroyAllWindows()

    def start_navigation(self):
        """Start navigation to the door."""
        if not self.navigating:
            self.navigating = True
            self.door_announced = False
            self.obstacle_announced = False
            self.speak("Starting navigation to the door. Please move slowly.", priority=True)

    def stop_navigation(self):
        """Stop navigation."""
        if self.navigating:
            self.navigating = False
            self.speak("Navigation stopped.", priority=True)

    def start(self):
        """Start the door navigation assistant."""
        if self.running:
            return

        self.running = True

        # Start processing thread
        self.process_thread = threading.Thread(target=self.process_loop)
        self.process_thread.daemon = True
        self.process_thread.start()

        print("Enhanced Door Navigation Assistant started")
        print("Press 'q' to quit, 'n' to start navigation, 's' to stop navigation")

        # Initial announcement
        self.speak("Enhanced door navigation assistant is ready. Press N to start navigation.")

    def stop(self):
        """Stop the door navigation assistant."""
        self.running = False
        self.navigating = False

        # Wait for processing thread to finish
        if self.process_thread:
            self.process_thread.join(timeout=1.0)

        print("Enhanced Door Navigation Assistant stopped")


def main():
    """Main function."""
    # Create enhanced door navigation assistant
    assistant = EnhancedRaspberryPiDoorAssistant(
        model_path='runs/train/yolov8_door_detection/weights/best.pt',
        camera_id=0,
        confidence=0.4,
        door_class_names=['door', 'Door', 'knob', 'lever', 'hinge'],
        frame_width=320,  # Reduced resolution for better performance
        frame_height=240
    )

    try:
        # Start assistant
        assistant.start()

        # Keep main thread alive
        while True:
            time.sleep(0.1)

    except KeyboardInterrupt:
        print("Interrupted by user")
    finally:
        # Stop assistant
        assistant.stop()


if __name__ == "__main__":
    main()
