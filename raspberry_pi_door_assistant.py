import cv2
import numpy as np
import time
import threading
import os
import subprocess
from ultralytics import YOLO

class RaspberryPiDoorAssistant:
    """
    Simplified door navigation assistant optimized for Raspberry Pi.
    Uses a more efficient approach with reduced computational requirements.
    """
    
    def __init__(self, 
                 model_path='runs/train/yolov8_door_detection/weights/best.pt',
                 camera_id=0,
                 confidence=0.4,
                 door_class_names=None,
                 frame_width=320,  # Reduced resolution for better performance
                 frame_height=240,
                 focal_length=800,
                 known_door_width=0.9):  # meters
        """
        Initialize the door navigation assistant.
        
        Args:
            model_path (str): Path to the trained YOLOv8 model.
            camera_id (int): Camera device ID.
            confidence (float): Confidence threshold for detections.
            door_class_names (list): List of class names that represent doors.
            frame_width (int): Width of the camera frame.
            frame_height (int): Height of the camera frame.
            focal_length (float): Focal length of the camera in pixels.
            known_door_width (float): Known width of a door in meters.
        """
        # Initialize YOLO model
        print(f"Loading model from {model_path}...")
        self.model = YOLO(model_path)
        
        # Set default door class names if not provided
        if door_class_names is None:
            self.door_class_names = ['door', 'Door']
        else:
            self.door_class_names = door_class_names
            
        # Initialize camera
        self.camera_id = camera_id
        self.cap = None
        self.frame_width = frame_width
        self.frame_height = frame_height
        
        # Detection parameters
        self.confidence = confidence
        
        # Distance estimation parameters
        self.focal_length = focal_length
        self.known_door_width = known_door_width
        
        # State variables
        self.running = False
        self.navigating = False
        self.door_detected = False
        self.door_distance = None
        self.door_bbox = None
        self.door_center_x = None
        
        # Navigation parameters
        self.last_guidance_time = 0
        self.guidance_interval = 2.0  # seconds
        self.close_door_threshold = 1.0  # meters
        self.door_announced = False
        
        # Frame processing rate control
        self.process_every_n_frames = 3  # Process every 3rd frame for better performance
        self.frame_count = 0
        
        # Processing thread
        self.process_thread = None
        
    def initialize_camera(self):
        """Initialize the camera."""
        if self.cap is not None and self.cap.isOpened():
            self.cap.release()
            
        self.cap = cv2.VideoCapture(self.camera_id)
        self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.frame_width)
        self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.frame_height)
        
        if not self.cap.isOpened():
            raise RuntimeError(f"Could not open camera {self.camera_id}")
            
        # Get actual camera properties
        self.frame_width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        self.frame_height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        print(f"Camera initialized with resolution: {self.frame_width}x{self.frame_height}")
    
    def estimate_distance(self, bbox_width):
        """
        Estimate the distance to an object based on its bounding box width.
        
        Args:
            bbox_width (int): Width of the object's bounding box in pixels.
            
        Returns:
            float: Estimated distance in meters.
        """
        if bbox_width == 0:
            return float('inf')
        
        distance = (self.known_door_width * self.focal_length) / bbox_width
        return distance
    
    def speak(self, text):
        """
        Speak text using the system's text-to-speech capabilities.
        Uses espeak on Raspberry Pi for better performance.
        
        Args:
            text (str): Text to speak.
        """
        try:
            # Use espeak for better performance on Raspberry Pi
            subprocess.Popen(['espeak', '-s', '150', '-a', '200', text])
        except Exception as e:
            print(f"TTS error: {e}")
            print(f"Would say: {text}")
    
    def process_frame(self, frame):
        """
        Process a frame to detect doors.
        
        Args:
            frame (numpy.ndarray): The image frame.
            
        Returns:
            tuple: Processed frame, door detected flag, door distance, door bounding box, door center x.
        """
        # Perform detection
        results = self.model(frame, conf=self.confidence)
        
        # Process results
        door_detected = False
        door_distance = None
        door_bbox = None
        door_center_x = None
        
        # Get detections
        for result in results:
            boxes = result.boxes
            
            for box in boxes:
                # Get class and confidence
                cls = int(box.cls[0])
                cls_name = self.model.names[cls]
                conf = float(box.conf[0])
                
                # Get bounding box
                x1, y1, x2, y2 = box.xyxy[0].cpu().numpy().astype(int)
                bbox = (x1, y1, x2, y2)
                
                # Check if it's a door
                if cls_name.lower() in [name.lower() for name in self.door_class_names]:
                    # If multiple doors, choose the closest/largest one
                    if not door_detected or (x2 - x1) > (door_bbox[2] - door_bbox[0]):
                        door_detected = True
                        door_bbox = bbox
                        door_distance = self.estimate_distance(x2 - x1)
                        door_center_x = (x1 + x2) // 2
        
        # Draw detections on frame
        annotated_frame = results[0].plot()
        
        # Draw distance if door detected
        if door_detected and door_distance is not None:
            x1, y1, x2, y2 = door_bbox
            center_x = (x1 + x2) // 2
            center_y = (y1 + y2) // 2
            
            # Draw distance text
            cv2.putText(
                annotated_frame, 
                f"{door_distance:.2f}m", 
                (center_x, center_y), 
                cv2.FONT_HERSHEY_SIMPLEX, 
                0.7, 
                (0, 255, 0), 
                2
            )
            
            # Draw navigation arrow
            frame_center_x = self.frame_width // 2
            arrow_length = 50
            arrow_color = (0, 255, 255)
            arrow_thickness = 2
            
            if abs(center_x - frame_center_x) > 50:  # If door is not centered
                if center_x < frame_center_x:
                    # Door is to the left
                    cv2.arrowedLine(
                        annotated_frame,
                        (frame_center_x, self.frame_height - 30),
                        (frame_center_x - arrow_length, self.frame_height - 30),
                        arrow_color,
                        arrow_thickness
                    )
                else:
                    # Door is to the right
                    cv2.arrowedLine(
                        annotated_frame,
                        (frame_center_x, self.frame_height - 30),
                        (frame_center_x + arrow_length, self.frame_height - 30),
                        arrow_color,
                        arrow_thickness
                    )
            else:
                # Door is centered, draw forward arrow
                cv2.arrowedLine(
                    annotated_frame,
                    (frame_center_x, self.frame_height - 30),
                    (frame_center_x, self.frame_height - 30 - arrow_length),
                    arrow_color,
                    arrow_thickness
                )
        
        return annotated_frame, door_detected, door_distance, door_bbox, door_center_x
    
    def provide_guidance(self, door_detected, door_distance, door_center_x):
        """
        Provide voice guidance based on door detection.
        
        Args:
            door_detected (bool): Whether a door was detected.
            door_distance (float): Distance to the door in meters.
            door_center_x (int): X-coordinate of the door center.
        """
        if not self.navigating:
            return
            
        current_time = time.time()
        if current_time - self.last_guidance_time < self.guidance_interval:
            return
            
        self.last_guidance_time = current_time
        
        if door_detected:
            # Door is detected
            if not self.door_detected:
                # First time detecting the door
                self.speak("Door detected")
                
            # Check distance to door
            if door_distance < self.close_door_threshold:
                if not self.door_announced:
                    self.speak("You have reached the door. Stop.")
                    self.door_announced = True
                return
                
            # Provide directional guidance
            frame_center_x = self.frame_width // 2
            if abs(door_center_x - frame_center_x) > 50:
                if door_center_x < frame_center_x:
                    self.speak("Door is to your left")
                else:
                    self.speak("Door is to your right")
            else:
                self.speak(f"Door is straight ahead, {door_distance:.1f} meters away")
                
        elif self.door_detected:
            # Door was detected before but not now
            self.speak("Door lost. Please look around.")
            self.door_announced = False
        else:
            # No door detected
            self.speak("No door detected. Please look around.")
    
    def process_loop(self):
        """Main processing loop."""
        try:
            self.initialize_camera()
            
            while self.running:
                # Read frame
                ret, frame = self.cap.read()
                
                if not ret:
                    print("Error: Failed to capture frame")
                    time.sleep(0.1)
                    continue
                
                # Process only every n-th frame for better performance
                self.frame_count += 1
                if self.frame_count % self.process_every_n_frames != 0:
                    # Just display the raw frame
                    cv2.imshow("Door Navigation Assistant", frame)
                    if cv2.waitKey(1) & 0xFF == ord('q'):
                        break
                    continue
                
                # Process frame
                processed_frame, door_detected, door_distance, door_bbox, door_center_x = self.process_frame(frame)
                
                # Update state
                self.door_detected = door_detected
                self.door_distance = door_distance
                self.door_bbox = door_bbox
                self.door_center_x = door_center_x
                
                # Provide guidance
                if self.navigating:
                    self.provide_guidance(door_detected, door_distance, door_center_x)
                
                # Display frame
                cv2.imshow("Door Navigation Assistant", processed_frame)
                
                # Check for key press
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    break
                elif key == ord('n'):
                    self.start_navigation()
                elif key == ord('s'):
                    self.stop_navigation()
                
        except Exception as e:
            print(f"Error in processing loop: {e}")
        finally:
            if self.cap is not None:
                self.cap.release()
            cv2.destroyAllWindows()
    
    def start_navigation(self):
        """Start navigation to the door."""
        if not self.navigating:
            self.navigating = True
            self.door_announced = False
            self.speak("Starting navigation to the door. Please move slowly.")
    
    def stop_navigation(self):
        """Stop navigation."""
        if self.navigating:
            self.navigating = False
            self.speak("Navigation stopped.")
    
    def start(self):
        """Start the door navigation assistant."""
        if self.running:
            return
            
        self.running = True
        
        # Start processing thread
        self.process_thread = threading.Thread(target=self.process_loop)
        self.process_thread.daemon = True
        self.process_thread.start()
        
        print("Door Navigation Assistant started")
        print("Press 'q' to quit, 'n' to start navigation, 's' to stop navigation")
        print("Say 'take me to the door' to start navigation")
        
        # Initial announcement
        self.speak("Door navigation assistant is ready. Press N or say take me to the door to start navigation.")
    
    def stop(self):
        """Stop the door navigation assistant."""
        self.running = False
        self.navigating = False
        
        # Wait for processing thread to finish
        if self.process_thread:
            self.process_thread.join(timeout=1.0)
            
        print("Door Navigation Assistant stopped")

def main():
    """Main function."""
    # Create door navigation assistant
    assistant = RaspberryPiDoorAssistant(
        model_path='runs/train/yolov8_door_detection/weights/best.pt',
        camera_id=0,
        confidence=0.4,
        door_class_names=['door', 'Door', 'knob', 'lever', 'hinge'],
        frame_width=320,  # Reduced resolution for better performance
        frame_height=240
    )
    
    try:
        # Start assistant
        assistant.start()
        
        # Keep main thread alive
        while True:
            time.sleep(0.1)
            
    except KeyboardInterrupt:
        print("Interrupted by user")
    finally:
        # Stop assistant
        assistant.stop()

if __name__ == "__main__":
    main()
