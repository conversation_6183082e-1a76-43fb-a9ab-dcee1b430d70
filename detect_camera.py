import cv2
import time
import numpy as np
from ultralytics import YOLO
import argparse

def parse_arguments():
    parser = argparse.ArgumentParser(description='YOLOv8 Door Detection with Camera')
    parser.add_argument('--model', type=str, default='runs/train/yolov8_door_detection/weights/best.pt',
                        help='Path to the trained YOLOv8 model')
    parser.add_argument('--conf', type=float, default=0.25,
                        help='Confidence threshold for detections')
    parser.add_argument('--camera', type=int, default=0,
                        help='Camera device ID (default: 0)')
    parser.add_argument('--width', type=int, default=640,
                        help='Width of the camera feed')
    parser.add_argument('--height', type=int, default=480,
                        help='Height of the camera feed')
    parser.add_argument('--fps', type=int, default=30,
                        help='Target FPS for the camera feed')
    return parser.parse_args()

def main():
    # Parse command-line arguments
    args = parse_arguments()
    
    # Load the YOLOv8 model
    print(f"Loading model from {args.model}...")
    model = YOLO(args.model)
    
    # Initialize the camera
    print(f"Opening camera device {args.camera}...")
    cap = cv2.VideoCapture(args.camera)
    
    # Set camera properties
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, args.width)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, args.height)
    cap.set(cv2.CAP_PROP_FPS, args.fps)
    
    # Check if the camera opened successfully
    if not cap.isOpened():
        print("Error: Could not open camera.")
        return
    
    # Get actual camera properties
    actual_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    actual_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    actual_fps = cap.get(cv2.CAP_PROP_FPS)
    
    print(f"Camera initialized with resolution: {actual_width}x{actual_height}, FPS: {actual_fps}")
    
    # Create a window for display
    window_name = "YOLOv8 Door Detection"
    cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
    cv2.resizeWindow(window_name, actual_width, actual_height)
    
    # Variables for FPS calculation
    fps = 0
    frame_count = 0
    start_time = time.time()
    
    print("Press 'q' to quit")
    
    # Main loop
    while True:
        # Read a frame from the camera
        ret, frame = cap.read()
        
        if not ret:
            print("Error: Failed to capture frame from camera.")
            break
        
        # Perform detection
        results = model(frame, conf=args.conf)
        
        # Process the results
        annotated_frame = results[0].plot()
        
        # Calculate and display FPS
        frame_count += 1
        elapsed_time = time.time() - start_time
        if elapsed_time >= 1.0:
            fps = frame_count / elapsed_time
            frame_count = 0
            start_time = time.time()
        
        # Display FPS on the frame
        cv2.putText(annotated_frame, f"FPS: {fps:.1f}", (10, 30), 
                    cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        
        # Display the detection count
        detections = results[0].boxes
        num_detections = len(detections)
        cv2.putText(annotated_frame, f"Detections: {num_detections}", (10, 70), 
                    cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        
        # Display detection information
        if num_detections > 0:
            y_offset = 110
            for i, box in enumerate(detections):
                cls = int(box.cls[0])
                cls_name = model.names[cls]
                conf = float(box.conf[0])
                
                # Display class name and confidence
                text = f"{cls_name}: {conf:.2f}"
                cv2.putText(annotated_frame, text, (10, y_offset), 
                            cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
                y_offset += 30
        
        # Show the frame
        cv2.imshow(window_name, annotated_frame)
        
        # Check for key press
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            print("Quitting...")
            break
    
    # Release resources
    cap.release()
    cv2.destroyAllWindows()
    print("Camera detection stopped.")

if __name__ == "__main__":
    main()
