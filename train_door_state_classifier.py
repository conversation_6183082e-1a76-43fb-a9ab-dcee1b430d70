import os
import cv2
import numpy as np
import tensorflow as tf
from tensorflow.keras import layers, models
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
import shutil
from pathlib import Path
import json
import random
from datetime import datetime

class DoorStateClassifier:
    def __init__(self, dataset_dir="door_state_dataset", model_dir="door_state_models"):
        """
        Initialize the Door State Classifier
        
        Args:
            dataset_dir (str): Directory to store organized dataset
            model_dir (str): Directory to store trained models
        """
        self.dataset_dir = Path(dataset_dir)
        self.model_dir = Path(model_dir)
        self.img_size = (224, 224)
        self.batch_size = 32
        self.epochs = 50
        
        # Create directories
        self.dataset_dir.mkdir(exist_ok=True)
        self.model_dir.mkdir(exist_ok=True)
        
        # Create subdirectories for train/val/test
        for split in ['train', 'val', 'test']:
            for class_name in ['open', 'closed']:
                (self.dataset_dir / split / class_name).mkdir(parents=True, exist_ok=True)
    
    def extract_closed_doors_from_training_data(self, train_images_dir="train/images", train_labels_dir="train/labels"):
        """
        Extract closed door images from existing training data by analyzing YOLO labels
        """
        print("Extracting closed door images from existing training data...")
        
        train_images_path = Path(train_images_dir)
        train_labels_path = Path(train_labels_dir)
        
        if not train_images_path.exists() or not train_labels_path.exists():
            print(f"Training data not found at {train_images_path} or {train_labels_path}")
            return []
        
        closed_door_images = []
        
        # Get all image files
        image_files = list(train_images_path.glob("*.jpg")) + list(train_images_path.glob("*.png"))
        
        for img_file in image_files:
            # Find corresponding label file
            label_file = train_labels_path / f"{img_file.stem}.txt"
            
            if label_file.exists():
                # Read YOLO labels
                with open(label_file, 'r') as f:
                    labels = f.readlines()
                
                # Check if image contains doors (class 0 in your data.yaml)
                has_door = False
                for label in labels:
                    parts = label.strip().split()
                    if len(parts) >= 5:
                        class_id = int(parts[0])
                        if class_id == 0:  # door class
                            has_door = True
                            break
                
                if has_door:
                    # Load and analyze the image to determine if door is closed
                    img = cv2.imread(str(img_file))
                    if img is not None:
                        # Use heuristics to determine if door is likely closed
                        # (doors that are rectangular, aligned, have clear frames)
                        if self._is_likely_closed_door(img, labels):
                            closed_door_images.append(str(img_file))
        
        print(f"Found {len(closed_door_images)} potential closed door images")
        return closed_door_images
    
    def _is_likely_closed_door(self, img, labels):
        """
        Heuristic to determine if a door in the image is likely closed
        Based on geometric properties and visual characteristics
        """
        height, width = img.shape[:2]
        
        # Analyze door regions from YOLO labels
        for label in labels:
            parts = label.strip().split()
            if len(parts) >= 5:
                class_id = int(parts[0])
                if class_id == 0:  # door class
                    # Convert YOLO format to pixel coordinates
                    x_center = float(parts[1]) * width
                    y_center = float(parts[2]) * height
                    w = float(parts[3]) * width
                    h = float(parts[4]) * height
                    
                    x1 = int(x_center - w/2)
                    y1 = int(y_center - h/2)
                    x2 = int(x_center + w/2)
                    y2 = int(y_center + h/2)
                    
                    # Extract door region
                    door_region = img[max(0, y1):min(height, y2), max(0, x1):min(width, x2)]
                    
                    if door_region.size > 0:
                        # Heuristics for closed door:
                        # 1. More rectangular/uniform appearance
                        # 2. Less depth variation
                        # 3. More uniform color distribution
                        
                        # Convert to grayscale for analysis
                        gray = cv2.cvtColor(door_region, cv2.COLOR_BGR2GRAY)
                        
                        # Calculate edge density (closed doors have cleaner edges)
                        edges = cv2.Canny(gray, 50, 150)
                        edge_density = np.sum(edges > 0) / edges.size
                        
                        # Calculate color uniformity
                        color_std = np.std(gray)
                        
                        # Calculate aspect ratio (doors are typically taller than wide)
                        aspect_ratio = h / w if w > 0 else 0
                        
                        # Heuristic scoring
                        closed_score = 0
                        
                        # Prefer moderate edge density (not too chaotic)
                        if 0.05 < edge_density < 0.15:
                            closed_score += 1
                        
                        # Prefer moderate color variation (not too uniform, not too chaotic)
                        if 20 < color_std < 60:
                            closed_score += 1
                        
                        # Prefer door-like aspect ratios
                        if 1.5 < aspect_ratio < 3.0:
                            closed_score += 1
                        
                        # Return True if it looks like a closed door
                        return closed_score >= 2
        
        return False
    
    def organize_dataset(self, open_door_dir="Open Door", closed_door_images=None, train_ratio=0.7, val_ratio=0.15):
        """
        Organize images into train/val/test splits
        
        Args:
            open_door_dir (str): Directory containing open door images
            closed_door_images (list): List of closed door image paths
            train_ratio (float): Ratio for training set
            val_ratio (float): Ratio for validation set (test = 1 - train - val)
        """
        print("Organizing dataset...")
        
        # Get open door images
        open_door_path = Path(open_door_dir)
        open_images = list(open_door_path.glob("*.jpg")) + list(open_door_path.glob("*.png"))
        
        print(f"Found {len(open_images)} open door images")
        
        # Get closed door images
        if closed_door_images is None:
            closed_door_images = self.extract_closed_doors_from_training_data()
        
        # Limit closed door images to match open door images for balanced dataset
        if len(closed_door_images) > len(open_images):
            closed_door_images = random.sample(closed_door_images, len(open_images))
        
        print(f"Using {len(closed_door_images)} closed door images")
        
        # Split data
        def split_data(images, train_ratio, val_ratio):
            random.shuffle(images)
            n_total = len(images)
            n_train = int(n_total * train_ratio)
            n_val = int(n_total * val_ratio)
            
            train_imgs = images[:n_train]
            val_imgs = images[n_train:n_train + n_val]
            test_imgs = images[n_train + n_val:]
            
            return train_imgs, val_imgs, test_imgs
        
        # Split open door images
        open_train, open_val, open_test = split_data(open_images, train_ratio, val_ratio)
        
        # Split closed door images
        closed_train, closed_val, closed_test = split_data(closed_door_images, train_ratio, val_ratio)
        
        # Copy images to organized structure
        def copy_images(img_list, dest_dir, class_name):
            for i, img_path in enumerate(img_list):
                if isinstance(img_path, str):
                    img_path = Path(img_path)
                
                dest_file = dest_dir / f"{class_name}_{i:04d}.jpg"
                
                # Load and resize image
                img = cv2.imread(str(img_path))
                if img is not None:
                    img_resized = cv2.resize(img, self.img_size)
                    cv2.imwrite(str(dest_file), img_resized)
        
        # Copy all images
        copy_images(open_train, self.dataset_dir / "train" / "open", "open")
        copy_images(open_val, self.dataset_dir / "val" / "open", "open")
        copy_images(open_test, self.dataset_dir / "test" / "open", "open")
        
        copy_images(closed_train, self.dataset_dir / "train" / "closed", "closed")
        copy_images(closed_val, self.dataset_dir / "val" / "closed", "closed")
        copy_images(closed_test, self.dataset_dir / "test" / "closed", "closed")
        
        # Save dataset info
        dataset_info = {
            "created": datetime.now().isoformat(),
            "total_open": len(open_images),
            "total_closed": len(closed_door_images),
            "train_open": len(open_train),
            "train_closed": len(closed_train),
            "val_open": len(open_val),
            "val_closed": len(closed_val),
            "test_open": len(open_test),
            "test_closed": len(closed_test),
            "img_size": self.img_size
        }
        
        with open(self.dataset_dir / "dataset_info.json", 'w') as f:
            json.dump(dataset_info, f, indent=2)
        
        print("Dataset organization complete!")
        print(f"Train: {len(open_train)} open, {len(closed_train)} closed")
        print(f"Val: {len(open_val)} open, {len(closed_val)} closed")
        print(f"Test: {len(open_test)} open, {len(closed_test)} closed")
        
        return dataset_info

    def create_model(self):
        """
        Create a CNN model for door state classification
        """
        model = models.Sequential([
            # First Convolutional Block
            layers.Conv2D(32, (3, 3), activation='relu', input_shape=(*self.img_size, 3)),
            layers.BatchNormalization(),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),

            # Second Convolutional Block
            layers.Conv2D(64, (3, 3), activation='relu'),
            layers.BatchNormalization(),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),

            # Third Convolutional Block
            layers.Conv2D(128, (3, 3), activation='relu'),
            layers.BatchNormalization(),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),

            # Fourth Convolutional Block
            layers.Conv2D(256, (3, 3), activation='relu'),
            layers.BatchNormalization(),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),

            # Global Average Pooling
            layers.GlobalAveragePooling2D(),

            # Dense Layers
            layers.Dense(512, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.5),

            layers.Dense(256, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.5),

            # Output Layer (binary classification: open/closed)
            layers.Dense(1, activation='sigmoid')
        ])

        # Compile model
        model.compile(
            optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
            loss='binary_crossentropy',
            metrics=['accuracy', 'precision', 'recall']
        )

        return model

    def create_data_generators(self):
        """
        Create data generators with augmentation for training
        """
        # Training data generator with augmentation
        train_datagen = ImageDataGenerator(
            rescale=1./255,
            rotation_range=20,
            width_shift_range=0.2,
            height_shift_range=0.2,
            shear_range=0.2,
            zoom_range=0.2,
            horizontal_flip=True,
            brightness_range=[0.8, 1.2],
            fill_mode='nearest'
        )

        # Validation and test data generators (no augmentation)
        val_test_datagen = ImageDataGenerator(rescale=1./255)

        # Create generators
        train_generator = train_datagen.flow_from_directory(
            self.dataset_dir / 'train',
            target_size=self.img_size,
            batch_size=self.batch_size,
            class_mode='binary',
            classes=['closed', 'open'],  # closed=0, open=1
            shuffle=True
        )

        val_generator = val_test_datagen.flow_from_directory(
            self.dataset_dir / 'val',
            target_size=self.img_size,
            batch_size=self.batch_size,
            class_mode='binary',
            classes=['closed', 'open'],
            shuffle=False
        )

        test_generator = val_test_datagen.flow_from_directory(
            self.dataset_dir / 'test',
            target_size=self.img_size,
            batch_size=self.batch_size,
            class_mode='binary',
            classes=['closed', 'open'],
            shuffle=False
        )

        return train_generator, val_generator, test_generator

    def train_model(self, model_name="door_state_classifier"):
        """
        Train the door state classification model
        """
        print("Creating model...")
        model = self.create_model()

        print("Creating data generators...")
        train_gen, val_gen, test_gen = self.create_data_generators()

        print(f"Training samples: {train_gen.samples}")
        print(f"Validation samples: {val_gen.samples}")
        print(f"Test samples: {test_gen.samples}")

        # Callbacks
        callbacks = [
            tf.keras.callbacks.ModelCheckpoint(
                self.model_dir / f"{model_name}_best.h5",
                monitor='val_accuracy',
                save_best_only=True,
                mode='max',
                verbose=1
            ),
            tf.keras.callbacks.EarlyStopping(
                monitor='val_accuracy',
                patience=10,
                restore_best_weights=True,
                verbose=1
            ),
            tf.keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=5,
                min_lr=1e-7,
                verbose=1
            )
        ]

        print("Starting training...")
        history = model.fit(
            train_gen,
            epochs=self.epochs,
            validation_data=val_gen,
            callbacks=callbacks,
            verbose=1
        )

        # Save final model
        model.save(self.model_dir / f"{model_name}_final.h5")

        # Evaluate on test set
        print("Evaluating on test set...")
        test_loss, test_accuracy, test_precision, test_recall = model.evaluate(test_gen, verbose=1)

        # Calculate F1 score
        f1_score = 2 * (test_precision * test_recall) / (test_precision + test_recall)

        print(f"\nTest Results:")
        print(f"Accuracy: {test_accuracy:.4f}")
        print(f"Precision: {test_precision:.4f}")
        print(f"Recall: {test_recall:.4f}")
        print(f"F1 Score: {f1_score:.4f}")

        # Save training history and results
        results = {
            "model_name": model_name,
            "training_completed": datetime.now().isoformat(),
            "epochs_trained": len(history.history['loss']),
            "final_train_accuracy": float(history.history['accuracy'][-1]),
            "final_val_accuracy": float(history.history['val_accuracy'][-1]),
            "test_accuracy": float(test_accuracy),
            "test_precision": float(test_precision),
            "test_recall": float(test_recall),
            "test_f1_score": float(f1_score),
            "model_path": str(self.model_dir / f"{model_name}_best.h5")
        }

        with open(self.model_dir / f"{model_name}_results.json", 'w') as f:
            json.dump(results, f, indent=2)

        # Plot training history
        self.plot_training_history(history, model_name)

        return model, history, results

    def plot_training_history(self, history, model_name):
        """
        Plot training history
        """
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))

        # Accuracy
        axes[0, 0].plot(history.history['accuracy'], label='Training Accuracy')
        axes[0, 0].plot(history.history['val_accuracy'], label='Validation Accuracy')
        axes[0, 0].set_title('Model Accuracy')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Accuracy')
        axes[0, 0].legend()
        axes[0, 0].grid(True)

        # Loss
        axes[0, 1].plot(history.history['loss'], label='Training Loss')
        axes[0, 1].plot(history.history['val_loss'], label='Validation Loss')
        axes[0, 1].set_title('Model Loss')
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('Loss')
        axes[0, 1].legend()
        axes[0, 1].grid(True)

        # Precision
        axes[1, 0].plot(history.history['precision'], label='Training Precision')
        axes[1, 0].plot(history.history['val_precision'], label='Validation Precision')
        axes[1, 0].set_title('Model Precision')
        axes[1, 0].set_xlabel('Epoch')
        axes[1, 0].set_ylabel('Precision')
        axes[1, 0].legend()
        axes[1, 0].grid(True)

        # Recall
        axes[1, 1].plot(history.history['recall'], label='Training Recall')
        axes[1, 1].plot(history.history['val_recall'], label='Validation Recall')
        axes[1, 1].set_title('Model Recall')
        axes[1, 1].set_xlabel('Epoch')
        axes[1, 1].set_ylabel('Recall')
        axes[1, 1].legend()
        axes[1, 1].grid(True)

        plt.tight_layout()
        plt.savefig(self.model_dir / f"{model_name}_training_history.png", dpi=300, bbox_inches='tight')
        plt.close()

        print(f"Training history plot saved to {self.model_dir / f'{model_name}_training_history.png'}")

    def predict_door_state(self, image, model_path=None):
        """
        Predict door state for a single image

        Args:
            image: numpy array or path to image
            model_path: path to trained model (if None, uses latest)

        Returns:
            tuple: (prediction, confidence) where prediction is 'open' or 'closed'
        """
        if model_path is None:
            # Find the best model
            model_files = list(self.model_dir.glob("*_best.h5"))
            if not model_files:
                raise ValueError("No trained model found. Please train a model first.")
            model_path = model_files[-1]  # Use the latest

        # Load model
        model = tf.keras.models.load_model(model_path)

        # Prepare image
        if isinstance(image, str):
            image = cv2.imread(image)

        if image is None:
            raise ValueError("Could not load image")

        # Resize and normalize
        image_resized = cv2.resize(image, self.img_size)
        image_normalized = image_resized.astype(np.float32) / 255.0
        image_batch = np.expand_dims(image_normalized, axis=0)

        # Predict
        prediction = model.predict(image_batch, verbose=0)[0][0]

        # Convert to class and confidence
        if prediction > 0.5:
            door_state = "open"
            confidence = prediction
        else:
            door_state = "closed"
            confidence = 1 - prediction

        return door_state, confidence


def main():
    """
    Main function to run the complete door state classification pipeline
    """
    print("=== Door State Classification Training Pipeline ===")

    # Initialize classifier
    classifier = DoorStateClassifier()

    # Step 1: Organize dataset
    print("\n1. Organizing dataset...")
    dataset_info = classifier.organize_dataset()

    # Step 2: Train model
    print("\n2. Training model...")
    model, history, results = classifier.train_model()

    print("\n=== Training Complete ===")
    print(f"Model saved to: {results['model_path']}")
    print(f"Test Accuracy: {results['test_accuracy']:.4f}")
    print(f"Test F1 Score: {results['test_f1_score']:.4f}")

    # Step 3: Test prediction on a sample image
    print("\n3. Testing prediction...")
    try:
        # Test with first open door image
        open_door_path = Path("Open Door")
        sample_images = list(open_door_path.glob("*.jpg"))
        if sample_images:
            sample_image = sample_images[0]
            prediction, confidence = classifier.predict_door_state(str(sample_image))
            print(f"Sample prediction: {prediction} (confidence: {confidence:.4f})")
    except Exception as e:
        print(f"Could not test prediction: {e}")

    return classifier, results


if __name__ == "__main__":
    classifier, results = main()
