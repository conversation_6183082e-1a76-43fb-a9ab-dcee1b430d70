# Enhanced VisionGuard - Advanced Door Detection System

## Overview

Enhanced VisionGuard is an AI-powered navigation system specifically designed for blind users. This advanced version features sophisticated door state detection that can distinguish between open and closed doors using computer vision and machine learning techniques.

## Key Features

### 🚪 Advanced Door Detection
- **Open/Closed Door Recognition**: Uses reference image comparison and geometric analysis
- **Door State Confidence**: Provides confidence scores for door state detection
- **Multiple Detection Methods**: Combines histogram comparison, edge analysis, and frame alignment detection

### 🎯 Navigation System
- **Real-time Object Detection**: YOLO-based detection for doors and obstacles
- **Distance Estimation**: Accurate distance calculation using focal length and known door dimensions
- **Directional Guidance**: Left/right/forward navigation instructions

### 🔊 Accessibility Features
- **Timed Voice Guidance**: 3-second intervals between voice instructions
- **Priority Voice Messages**: Emergency and important messages override regular guidance
- **Voice Commands**: Hands-free control with speech recognition
- **Clear Audio Feedback**: Optimized speech rate and volume for clarity

### 🖥️ User Interface
- **Real-time Camera Feed**: Live video display with detection overlays
- **Control Buttons**: Start/Stop navigation, Emergency stop, Voice commands toggle
- **Status Display**: Current system status and navigation state
- **Visual Indicators**: Color-coded door state indicators (Green=Open, Red=Closed, Yellow=Unknown)

### 🛡️ Safety Features
- **Emergency Stop**: Immediate halt with voice confirmation
- **Obstacle Detection**: Warns about obstacles in navigation path
- **Door Lost Detection**: Alerts when door is no longer visible
- **Path Safety**: Monitors for safe navigation corridors

## Installation

### Prerequisites
```bash
pip install opencv-python
pip install ultralytics
pip install torch torchvision
pip install pyttsx3
pip install speechrecognition
pip install pillow
pip install scikit-learn
pip install numpy
```

### Quick Start
1. Ensure your camera is connected
2. Place the "Open Door" folder with reference images in the project directory
3. Run the system:
```bash
python vision_guard_enhanced_door_detection.py
```

## Usage

### Starting Navigation
1. **GUI Method**: Click "🚀 START NAVIGATION" button
2. **Voice Command**: Say "navigate to door" or "find door"
3. **Keyboard**: Press 'N' key (if using keyboard controls)

### Voice Commands
- **"navigate to door"** - Start navigation
- **"stop"** - Stop navigation
- **"emergency"** - Emergency stop
- **"where is door"** - Get door location information
- **"what do you see"** - Get current detection summary

### Navigation Guidance
The system provides voice guidance every 3 seconds with:
- Door state information (open/closed/unknown)
- Distance to door
- Direction instructions (left/right/forward)
- Obstacle warnings
- Safety alerts

## Door Detection Algorithm

### Method 1: Reference Image Comparison
- Compares detected door region with 166+ reference images of open doors
- Uses histogram correlation for similarity matching
- Returns confidence score for "open door" classification

### Method 2: Geometric Analysis
- Analyzes door frame alignment using edge detection
- Detects vertical lines indicating door edges
- Higher alignment scores suggest closed doors

### Method 3: Edge Pattern Analysis
- Examines edge density and complexity
- Open doors typically have more complex edge patterns
- Closed doors show simpler, more regular patterns

### Combined Decision Making
- Weighs all three methods with confidence thresholds
- Maintains detection history for stability
- Provides final classification: Open/Closed/Unknown

## File Structure

```
VisionGuard-Clean/
├── vision_guard_enhanced_door_detection.py  # Main enhanced system
├── vision_guard_clean.py                    # Original clean version
├── launch_visionguard.py                    # System launcher
├── requirements_enhanced.txt                # Dependencies
├── Open Door/                               # Reference images (166+ images)
│   ├── IMG_20250626_*.jpg
│   └── open_door_20250629_*.jpg
├── runs/                                    # YOLO model weights
├── sample_images/                           # Sample test images
└── README.md                               # This file
```

## Technical Specifications

- **Camera Resolution**: 640x480 (configurable)
- **Detection Confidence**: 0.4 threshold
- **Voice Guidance Interval**: 3.0 seconds
- **Close Door Threshold**: 1.5 meters
- **Obstacle Warning Threshold**: 2.0 meters
- **Processing Rate**: ~30 FPS with every 2nd frame processing

## Safety Considerations

1. **Always use in safe environments** - The system is an aid, not a replacement for human judgment
2. **Emergency stop available** - Use emergency stop button or voice command in dangerous situations
3. **Obstacle detection** - System warns about obstacles but cannot detect all hazards
4. **Door state verification** - Always verify door state manually before proceeding through

## Troubleshooting

### Camera Issues
- Ensure camera is connected and not used by other applications
- Check camera permissions in system settings
- Try different camera IDs (0, 1, 2) if default doesn't work

### Voice Issues
- Check microphone permissions
- Ensure speakers/headphones are connected
- Verify audio drivers are installed

### Detection Issues
- Ensure adequate lighting
- Check if "Open Door" folder contains reference images
- Verify YOLO model file (yolov8n.pt) is present

## Contributing

This system is designed for accessibility and safety. When contributing:
1. Test thoroughly in safe environments
2. Maintain voice guidance timing (3-second intervals)
3. Preserve safety features and emergency stops
4. Document any changes to detection algorithms

## License

This project is developed for accessibility purposes. Please use responsibly and ensure proper testing before deployment in real-world scenarios.

---

**Note**: This system is designed as an assistive technology for blind users. Always prioritize safety and use in conjunction with traditional navigation methods.
