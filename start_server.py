#!/usr/bin/env python3
"""
Simple HTTP server for the Door Navigation Assistant web application
"""

import http.server
import socketserver
import webbrowser
import os
import argparse

def parse_args():
    parser = argparse.ArgumentParser(description='Start a web server for the Door Navigation Assistant')
    parser.add_argument('--port', type=int, default=8000,
                        help='Port to run the server on (default: 8000)')
    parser.add_argument('--no-browser', action='store_true',
                        help='Do not open the browser automatically')
    return parser.parse_args()

def main():
    args = parse_args()
    
    # Set up the server
    handler = http.server.SimpleHTTPRequestHandler
    
    # Allow CORS for development
    class CORSHTTPRequestHandler(handler):
        def end_headers(self):
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET')
            self.send_header('Cache-Control', 'no-store, no-cache, must-revalidate')
            return super().end_headers()
    
    # Create the server
    with socketserver.TCPServer(("", args.port), CORSHTTPRequestHandler) as httpd:
        print(f"Server running at http://localhost:{args.port}/")
        print(f"Open your browser and navigate to http://localhost:{args.port}/Web_Door_Assistant/")
        print("Press Ctrl+C to stop the server")
        
        # Open the browser
        if not args.no_browser:
            webbrowser.open(f"http://localhost:{args.port}/Web_Door_Assistant/")
        
        # Start the server
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\nServer stopped.")

if __name__ == "__main__":
    main()
