import cv2
import numpy as np
import math

class DistanceEstimator:
    """
    Class for estimating distance to objects using a single camera.
    Uses a simple focal length based approach for monocular distance estimation.
    """
    
    def __init__(self, focal_length=800, known_width=0.9):
        """
        Initialize the distance estimator.
        
        Args:
            focal_length (float): Focal length of the camera in pixels.
                                 Can be calibrated using a known object at a known distance.
            known_width (float): Known width of a door in meters (default is 0.9m or 90cm).
        """
        self.focal_length = focal_length
        self.known_width = known_width
        
    def calibrate_focal_length(self, known_distance, known_width_pixels):
        """
        Calibrate the focal length using a known distance and pixel width.
        
        Args:
            known_distance (float): Known distance to an object in meters.
            known_width_pixels (int): Width of the object in pixels at that distance.
            
        Returns:
            float: Calculated focal length.
        """
        # Focal Length = (Pixel Width * Known Distance) / Known Width
        self.focal_length = (known_width_pixels * known_distance) / self.known_width
        return self.focal_length
    
    def estimate_distance(self, bbox_width):
        """
        Estimate the distance to an object based on its bounding box width.
        
        Args:
            bbox_width (int): Width of the object's bounding box in pixels.
            
        Returns:
            float: Estimated distance in meters.
        """
        # Distance = (Known Width * Focal Length) / Pixel Width
        if bbox_width == 0:
            return float('inf')
        
        distance = (self.known_width * self.focal_length) / bbox_width
        return distance
    
    def estimate_distance_from_bbox(self, bbox):
        """
        Estimate distance from a bounding box (x1, y1, x2, y2).
        
        Args:
            bbox (tuple): Bounding box coordinates (x1, y1, x2, y2).
            
        Returns:
            float: Estimated distance in meters.
        """
        x1, y1, x2, y2 = bbox
        bbox_width = x2 - x1
        return self.estimate_distance(bbox_width)
    
    def draw_distance(self, frame, bbox, distance):
        """
        Draw the estimated distance on the frame.
        
        Args:
            frame (numpy.ndarray): The image frame.
            bbox (tuple): Bounding box coordinates (x1, y1, x2, y2).
            distance (float): Estimated distance in meters.
            
        Returns:
            numpy.ndarray: Frame with distance information drawn.
        """
        x1, y1, x2, y2 = bbox
        center_x = (x1 + x2) // 2
        center_y = (y1 + y2) // 2
        
        # Draw distance text
        cv2.putText(
            frame, 
            f"{distance:.2f}m", 
            (center_x, center_y), 
            cv2.FONT_HERSHEY_SIMPLEX, 
            0.7, 
            (0, 255, 0), 
            2
        )
        
        return frame
