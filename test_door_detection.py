#!/usr/bin/env python3
"""
Test script for enhanced door detection
This script allows you to test the door detection on individual images
"""

import cv2
import numpy as np
import os
from vision_gurd_v2 import VisionGuard

def test_door_detection_on_image(image_path, vision_guard):
    """Test door detection on a single image"""
    print(f"\nTesting door detection on: {image_path}")
    
    # Load image
    frame = cv2.imread(image_path)
    if frame is None:
        print(f"Error: Could not load image {image_path}")
        return
    
    # Resize to match expected dimensions
    frame = cv2.resize(frame, (vision_guard.frame_width, vision_guard.frame_height))
    
    # Process the frame
    try:
        annotated_frame, door_detected, door_distance, door_bbox, door_center_x, obstacles = vision_guard.process_frame(frame)
        
        print(f"Door detected: {door_detected}")
        if door_detected:
            print(f"Door state: {vision_guard.door_state}")
            print(f"Door distance: {door_distance:.2f}m")
            print(f"Door center X: {door_center_x}")
        
        print(f"Obstacles detected: {len(obstacles)}")
        
        # Display results
        cv2.imshow("Original", frame)
        cv2.imshow("Detection Result", annotated_frame)
        cv2.imshow("Door Analysis", vision_guard.display_frames.get('door', frame))
        
        print("Press any key to continue to next image, 'q' to quit...")
        key = cv2.waitKey(0) & 0xFF
        cv2.destroyAllWindows()
        
        return key != ord('q')
        
    except Exception as e:
        print(f"Error processing image: {e}")
        return True

def main():
    print("Enhanced Door Detection Test")
    print("=" * 40)
    
    # Initialize VisionGuard
    try:
        vision_guard = VisionGuard(
            model_path='runs/train/yolov8_door_detection/weights/best.pt',
            camera_id=0,
            confidence=0.4,
            door_class_names=['door', 'Door', 'hinged', 'knob', 'lever'],
            frame_width=800,
            frame_height=600
        )
        print("VisionGuard initialized successfully!")
    except Exception as e:
        print(f"Error initializing VisionGuard: {e}")
        return
    
    # Test on open door images
    open_door_folder = "Open Door"
    if os.path.exists(open_door_folder):
        print(f"\nTesting on images from '{open_door_folder}' folder...")
        image_files = [f for f in os.listdir(open_door_folder) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
        
        # Test on first 5 images
        test_images = image_files[:5]
        
        for img_file in test_images:
            img_path = os.path.join(open_door_folder, img_file)
            if not test_door_detection_on_image(img_path, vision_guard):
                break
    else:
        print(f"'{open_door_folder}' folder not found!")
    
    # Test on any other sample images
    sample_folders = ["sample_images", "dataset/images", "data/images"]
    for folder in sample_folders:
        if os.path.exists(folder):
            print(f"\nTesting on images from '{folder}' folder...")
            image_files = [f for f in os.listdir(folder) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
            
            # Test on first 3 images
            test_images = image_files[:3]
            
            for img_file in test_images:
                img_path = os.path.join(folder, img_file)
                if not test_door_detection_on_image(img_path, vision_guard):
                    break
            break
    
    print("\nTesting completed!")

if __name__ == "__main__":
    main()
