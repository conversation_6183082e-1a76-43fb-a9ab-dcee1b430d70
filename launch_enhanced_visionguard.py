
"""
Enhanced VisionGuard Launcher
============================

Simple launcher script for the Enhanced VisionGuard Door Detection System.
Performs basic system checks before launching the main application.

Usage: python launch_enhanced_visionguard.py
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = [
        'cv2', 'numpy', 'torch', 'ultralytics', 'pyttsx3', 
        'speech_recognition', 'PIL', 'sklearn', 'tkinter'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'cv2':
                import cv2
            elif package == 'PIL':
                from PIL import Image
            elif package == 'tkinter':
                import tkinter
            else:
                __import__(package)
            print(f"✓ {package} - OK")
        except ImportError:
            missing_packages.append(package)
            print(f"✗ {package} - MISSING")
    
    return missing_packages

def check_files():
    """Check if required files exist"""
    required_files = [
        'vision_guard_enhanced_door_detection.py',
        'Open Door',  # Directory
        'yolov8n.pt'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            if file_path == 'Open Door':
                # Check if directory has images
                image_count = len([f for f in os.listdir(file_path) 
                                 if f.lower().endswith(('.jpg', '.jpeg', '.png'))])
                print(f"✓ {file_path} - OK ({image_count} reference images)")
            else:
                print(f"✓ {file_path} - OK")
        else:
            missing_files.append(file_path)
            print(f"✗ {file_path} - MISSING")
    
    return missing_files

def check_camera():
    """Check if camera is available"""
    try:
        import cv2
        cap = cv2.VideoCapture(0)
        if cap.isOpened():
            ret, frame = cap.read()
            cap.release()
            if ret:
                print("✓ Camera - OK")
                return True
            else:
                print("✗ Camera - Cannot read frames")
                return False
        else:
            print("✗ Camera - Cannot open")
            return False
    except Exception as e:
        print(f"✗ Camera - Error: {e}")
        return False

def main():
    """Main launcher function"""
    print("=" * 60)
    print("Enhanced VisionGuard Door Detection System")
    print("=" * 60)
    print()
    
    print("Checking system requirements...")
    print("-" * 40)
    
    # Check dependencies
    missing_deps = check_dependencies()
    print()
    
    # Check files
    missing_files = check_files()
    print()
    
    # Check camera
    camera_ok = check_camera()
    print()
    
    # Summary
    if missing_deps:
        print("❌ MISSING DEPENDENCIES:")
        for dep in missing_deps:
            print(f"   - {dep}")
        print("\nPlease install missing packages:")
        print("pip install opencv-python ultralytics torch pyttsx3 speechrecognition pillow scikit-learn")
        return False
    
    if missing_files:
        print("❌ MISSING FILES:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    if not camera_ok:
        print("⚠️  Camera not available - system will use sample images")
    
    print("✅ System checks completed successfully!")
    print()
    
    # Launch the enhanced system
    print("Launching Enhanced VisionGuard...")
    print("=" * 60)
    
    try:
        # Run the enhanced door detection system
        subprocess.run([sys.executable, 'vision_guard_enhanced_door_detection.py'], check=True)
    except subprocess.CalledProcessError as e:
        print(f"Error launching system: {e}")
        return False
    except KeyboardInterrupt:
        print("\nSystem interrupted by user")
        return True
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        input("\nPress Enter to exit...")
        sys.exit(1)
