#!/usr/bin/env python3
"""
AR Navigation Assistant
A voice-controlled assistant system that helps users navigate to doors with AR path visualization.
"""

import cv2
import numpy as np
import time
import threading
import os
import subprocess
import argparse
from ultralytics import YOLO
import pyttsx3
import speech_recognition as sr
import math
import queue

class DoorDetector:
    """Door detection using YOLOv8."""

    def __init__(self, model_path=None, confidence=0.4, door_class_names=None):
        """
        Initialize the door detector.

        Args:
            model_path (str): Path to the YOLOv8 model.
            confidence (float): Confidence threshold for detections.
            door_class_names (list): List of class names that represent doors.
        """
        self.model_path = model_path
        self.confidence = confidence
        self.door_class_names = door_class_names or ['door', 'Door', 'knob', 'lever', 'hinge']
        self.model = None
        self.use_mock = model_path is None

    def load_model(self):
        """Load the YOLOv8 model."""
        try:
            print(f"Loading model from {self.model_path}...")
            self.model = YOLO(self.model_path)
            print("Model loaded successfully!")
            return True
        except Exception as e:
            print(f"Error loading model: {e}")
            return False

    def detect(self, frame):
        """
        Detect doors in a frame.

        Args:
            frame (numpy.ndarray): The image frame.

        Returns:
            tuple: (annotated_frame, door_detected, door_bbox, door_center_x, door_confidence)
        """
        # Use mock detection if no model path is provided or model fails to load
        if self.use_mock or self.model is None and not self.load_model():
            return self._mock_detection(frame)

        try:
            # Run YOLOv8 inference
            results = self.model(frame, conf=self.confidence)

            door_detected = False
            door_bbox = (0, 0, 0, 0)
            door_center_x = 0
            door_confidence = 0

            # Get detections
            for result in results:
                boxes = result.boxes

                for box in boxes:
                    # Get class and confidence
                    cls = int(box.cls[0])
                    cls_name = self.model.names[cls]
                    conf = float(box.conf[0])

                    # Get bounding box
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy().astype(int)
                    bbox = (x1, y1, x2, y2)

                    # Check if it's a door
                    if cls_name.lower() in [name.lower() for name in self.door_class_names]:
                        # If multiple doors, choose the closest/largest one
                        if not door_detected or (x2 - x1) > (door_bbox[2] - door_bbox[0]):
                            door_detected = True
                            door_bbox = bbox
                            door_center_x = (x1 + x2) // 2
                            door_confidence = conf

            # Draw detections on frame
            annotated_frame = results[0].plot()

            return annotated_frame, door_detected, door_bbox, door_center_x, door_confidence

        except Exception as e:
            print(f"Error in door detection: {e}")
            return self._mock_detection(frame)

    def _mock_detection(self, frame):
        """
        Create a mock door detection for testing when model is unavailable.

        Args:
            frame (numpy.ndarray): The image frame.

        Returns:
            tuple: (annotated_frame, door_detected, door_bbox, door_center_x, door_confidence)
        """
        height, width = frame.shape[:2]

        # Create a mock door detection in the center of the frame
        x1 = int(width * 0.25)
        y1 = int(height * 0.2)
        x2 = int(width * 0.75)
        y2 = int(height * 0.9)

        door_bbox = (x1, y1, x2, y2)
        door_center_x = (x1 + x2) // 2

        # Draw the mock detection
        annotated_frame = frame.copy()
        cv2.rectangle(annotated_frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
        cv2.putText(annotated_frame, "Door 95%", (x1, y1 - 10),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.9, (0, 255, 0), 2)

        return annotated_frame, True, door_bbox, door_center_x, 0.95


class DistanceEstimator:
    """Distance estimation using monocular vision."""

    def __init__(self, focal_length=800, known_door_width=0.9):
        """
        Initialize the distance estimator.

        Args:
            focal_length (float): Focal length of the camera in pixels.
            known_door_width (float): Known width of a door in meters.
        """
        self.focal_length = focal_length
        self.known_door_width = known_door_width

    def estimate_distance(self, pixel_width):
        """
        Estimate distance to a door based on its pixel width.

        Args:
            pixel_width (int): Width of the door in pixels.

        Returns:
            float: Estimated distance in meters.
        """
        if pixel_width <= 0:
            return float('inf')

        # Distance = (Known Width * Focal Length) / Pixel Width
        distance = (self.known_door_width * self.focal_length) / pixel_width

        # Ensure distance is within a reasonable range
        return max(0.5, min(10.0, distance))


class PathPlanner:
    """Path planning for navigation."""

    def __init__(self, frame_width, frame_height, grid_size=20):
        """
        Initialize the path planner.

        Args:
            frame_width (int): Width of the frame.
            frame_height (int): Height of the frame.
            grid_size (int): Size of grid cells for path planning.
        """
        self.frame_width = frame_width
        self.frame_height = frame_height
        self.grid_size = grid_size
        self.path = []

    def create_obstacle_map(self, obstacles):
        """
        Create a grid-based obstacle map.

        Args:
            obstacles (list): List of obstacle bounding boxes.

        Returns:
            numpy.ndarray: Binary obstacle map.
        """
        # Create empty grid
        grid_width = self.frame_width // self.grid_size
        grid_height = self.frame_height // self.grid_size
        obstacle_map = np.zeros((grid_height, grid_width), dtype=np.uint8)

        # Add obstacles to grid
        for obstacle in obstacles:
            if len(obstacle) == 4:  # (x1, y1, x2, y2) format
                x1, y1, x2, y2 = obstacle
                grid_x1 = max(0, x1 // self.grid_size)
                grid_y1 = max(0, y1 // self.grid_size)
                grid_x2 = min(grid_width - 1, x2 // self.grid_size)
                grid_y2 = min(grid_height - 1, y2 // self.grid_size)

                obstacle_map[grid_y1:grid_y2+1, grid_x1:grid_x2+1] = 1

        return obstacle_map

    def find_path(self, start_point, goal_point, obstacle_map):
        """
        Find a path from start to goal using A* algorithm.

        Args:
            start_point (tuple): Starting point (x, y).
            goal_point (tuple): Goal point (x, y).
            obstacle_map (numpy.ndarray): Binary obstacle map.

        Returns:
            list: List of path points.
        """
        # Convert to grid coordinates
        start_grid = (start_point[0] // self.grid_size, start_point[1] // self.grid_size)
        goal_grid = (goal_point[0] // self.grid_size, goal_point[1] // self.grid_size)

        # Check if start or goal is in obstacle
        grid_height, grid_width = obstacle_map.shape
        if (start_grid[1] >= grid_height or start_grid[0] >= grid_width or
            goal_grid[1] >= grid_height or goal_grid[0] >= grid_width):
            return []

        if obstacle_map[start_grid[1], start_grid[0]] == 1 or obstacle_map[goal_grid[1], goal_grid[0]] == 1:
            return []

        # A* algorithm
        open_set = queue.PriorityQueue()
        open_set.put((0, start_grid))
        came_from = {}
        g_score = {start_grid: 0}
        f_score = {start_grid: self._heuristic(start_grid, goal_grid)}
        open_set_hash = {start_grid}

        while not open_set.empty():
            current = open_set.get()[1]
            open_set_hash.remove(current)

            if current == goal_grid:
                # Reconstruct path
                path = []
                while current in came_from:
                    # Convert back to pixel coordinates (center of grid cell)
                    x = current[0] * self.grid_size + self.grid_size // 2
                    y = current[1] * self.grid_size + self.grid_size // 2
                    path.append((x, y))
                    current = came_from[current]

                # Add start point
                x = start_grid[0] * self.grid_size + self.grid_size // 2
                y = start_grid[1] * self.grid_size + self.grid_size // 2
                path.append((x, y))

                # Reverse path (start to goal)
                path.reverse()

                # Add actual goal point at the end
                path.append(goal_point)

                self.path = path
                return path

            # Check neighbors
            for dx, dy in [(0, 1), (1, 0), (0, -1), (-1, 0), (1, 1), (-1, 1), (1, -1), (-1, -1)]:
                neighbor = (current[0] + dx, current[1] + dy)

                # Check if valid
                if (neighbor[1] < 0 or neighbor[1] >= grid_height or
                    neighbor[0] < 0 or neighbor[0] >= grid_width):
                    continue

                # Check if obstacle
                if obstacle_map[neighbor[1], neighbor[0]] == 1:
                    continue

                # Calculate g_score
                tentative_g_score = g_score[current] + self._distance(current, neighbor)

                if neighbor not in g_score or tentative_g_score < g_score[neighbor]:
                    # This path is better
                    came_from[neighbor] = current
                    g_score[neighbor] = tentative_g_score
                    f_score[neighbor] = tentative_g_score + self._heuristic(neighbor, goal_grid)

                    if neighbor not in open_set_hash:
                        open_set.put((f_score[neighbor], neighbor))
                        open_set_hash.add(neighbor)

        # No path found
        return []

    def _heuristic(self, a, b):
        """Calculate heuristic distance between two points."""
        return math.sqrt((b[0] - a[0])**2 + (b[1] - a[1])**2)

    def _distance(self, a, b):
        """Calculate Euclidean distance between two points."""
        return math.sqrt((b[0] - a[0])**2 + (b[1] - a[1])**2)

    def draw_path(self, frame, path):
        """
        Draw the path on the frame.

        Args:
            frame (numpy.ndarray): The image frame.
            path (list): List of path points.

        Returns:
            numpy.ndarray: Frame with path drawn.
        """
        if not path or len(path) < 2:
            return frame

        # Draw path
        for i in range(len(path) - 1):
            pt1 = (int(path[i][0]), int(path[i][1]))
            pt2 = (int(path[i+1][0]), int(path[i+1][1]))
            cv2.line(frame, pt1, pt2, (255, 204, 0), 3)

            # Draw waypoint
            if i > 0:
                cv2.circle(frame, pt1, 5, (255, 255, 255), -1)

        # Draw arrow at the end
        if len(path) >= 2:
            self._draw_arrow(frame, path[-2], path[-1], (255, 204, 0), 15)

        return frame

    def _draw_arrow(self, frame, start, end, color, arrow_size):
        """Draw an arrow on the frame."""
        start = (int(start[0]), int(start[1]))
        end = (int(end[0]), int(end[1]))

        # Draw line
        cv2.line(frame, start, end, color, 3)

        # Calculate arrow head
        angle = math.atan2(end[1] - start[1], end[0] - start[0])

        # Draw arrow head
        pt1 = (int(end[0] - arrow_size * math.cos(angle - math.pi/6)),
               int(end[1] - arrow_size * math.sin(angle - math.pi/6)))
        pt2 = (int(end[0] - arrow_size * math.cos(angle + math.pi/6)),
               int(end[1] - arrow_size * math.sin(angle + math.pi/6)))

        cv2.line(frame, end, pt1, color, 3)
        cv2.line(frame, end, pt2, color, 3)

    def get_navigation_command(self, user_position, path):
        """
        Get navigation command based on the path.

        Args:
            user_position (tuple): User position (x, y).
            path (list): List of path points.

        Returns:
            str: Navigation command ('left', 'right', 'forward', or 'stop').
        """
        if not path or len(path) < 2:
            return "stop"

        # Get the first segment of the path
        next_point = path[1]

        # Calculate direction vector
        dir_x = next_point[0] - user_position[0]
        dir_y = next_point[1] - user_position[1]

        # Calculate angle
        angle = math.degrees(math.atan2(dir_y, dir_x))

        # Determine direction
        if angle < -45 and angle > -135:
            return "forward"
        elif angle >= -45 and angle <= 45:
            return "right"
        elif (angle >= 135 and angle <= 180) or (angle <= -135 and angle >= -180):
            return "left"
        else:
            return "forward"


class ObstacleDetector:
    """Obstacle detection using motion detection."""

    def __init__(self, motion_threshold=20, sensitivity=0.5):
        """
        Initialize the obstacle detector.

        Args:
            motion_threshold (int): Threshold for motion detection.
            sensitivity (float): Sensitivity of motion detection (0-1).
        """
        self.motion_threshold = motion_threshold
        self.sensitivity = sensitivity
        self.last_frame = None
        self.obstacles = []

    def detect_obstacles(self, frame, door_bbox=None):
        """
        Detect obstacles in a frame using motion detection.

        Args:
            frame (numpy.ndarray): The image frame.
            door_bbox (tuple, optional): Door bounding box to avoid false positives.

        Returns:
            list: List of obstacle bounding boxes.
        """
        # Convert to grayscale
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        gray = cv2.GaussianBlur(gray, (21, 21), 0)

        # If this is the first frame, just store it and return
        if self.last_frame is None:
            self.last_frame = gray
            return []

        # Compute absolute difference between current and previous frame
        frame_delta = cv2.absdiff(self.last_frame, gray)
        thresh = cv2.threshold(frame_delta, self.motion_threshold, 255, cv2.THRESH_BINARY)[1]

        # Dilate the thresholded image to fill in holes
        thresh = cv2.dilate(thresh, None, iterations=2)

        # Find contours
        contours, _ = cv2.findContours(thresh.copy(), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # Process contours
        obstacles = []
        for contour in contours:
            # Filter small contours
            if cv2.contourArea(contour) < 500 * self.sensitivity:
                continue

            # Get bounding box
            (x, y, w, h) = cv2.boundingRect(contour)
            obstacle_bbox = (x, y, x + w, y + h)

            # Skip if this overlaps with the door
            if door_bbox and self._do_rects_overlap(obstacle_bbox, door_bbox):
                continue

            obstacles.append(obstacle_bbox)

        # Update last frame
        self.last_frame = gray
        self.obstacles = obstacles

        return obstacles

    def _do_rects_overlap(self, rect1, rect2):
        """Check if two rectangles overlap."""
        x1, y1, x2, y2 = rect1
        x3, y3, x4, y4 = rect2

        return not (x2 < x3 or x1 > x4 or y2 < y3 or y1 > y4)

    def draw_obstacles(self, frame, obstacles):
        """
        Draw obstacles on the frame.

        Args:
            frame (numpy.ndarray): The image frame.
            obstacles (list): List of obstacle bounding boxes.

        Returns:
            numpy.ndarray: Frame with obstacles drawn.
        """
        for obstacle in obstacles:
            x1, y1, x2, y2 = obstacle

            # Draw warning triangle
            center_x = (x1 + x2) // 2
            center_y = (y1 + y2) // 2
            size = 30
            height = int(size * math.sqrt(3) / 2)

            # Triangle points
            pt1 = (center_x, center_y - height // 2)
            pt2 = (center_x - size // 2, center_y + height // 2)
            pt3 = (center_x + size // 2, center_y + height // 2)

            # Draw triangle
            triangle_cnt = np.array([pt1, pt2, pt3])
            cv2.drawContours(frame, [triangle_cnt], 0, (0, 0, 255), -1)
            cv2.drawContours(frame, [triangle_cnt], 0, (255, 255, 255), 2)

            # Draw exclamation mark
            cv2.putText(frame, "!", (center_x - 3, center_y + 5),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

        return frame


class VoiceAssistant:
    """Voice assistant for navigation guidance."""

    def __init__(self, rate=150, volume=0.8):
        """
        Initialize the voice assistant.

        Args:
            rate (int): Speech rate.
            volume (float): Speech volume (0-1).
        """
        self.engine = pyttsx3.init()
        self.engine.setProperty('rate', rate)
        self.engine.setProperty('volume', volume)
        self.speech_queue = queue.Queue()
        self.speaking = False
        self.speech_thread = threading.Thread(target=self._speech_worker)
        self.speech_thread.daemon = True
        self.speech_thread.start()

        # For speech recognition
        self.recognizer = sr.Recognizer()
        try:
            self.microphone = sr.Microphone()
            with self.microphone as source:
                self.recognizer.adjust_for_ambient_noise(source)
        except:
            print("Warning: Speech recognition microphone not available")
            self.microphone = None

        self.listening = False
        self.listen_thread = None
        self.command_callback = None
        self.last_command_time = 0
        self.command_cooldown = 2.0  # seconds

    def speak(self, text, priority=False):
        """
        Speak a message.

        Args:
            text (str): Text to speak.
            priority (bool): Whether this message has priority.
        """
        if priority:
            # Clear queue for priority messages
            while not self.speech_queue.empty():
                try:
                    self.speech_queue.get_nowait()
                except queue.Empty:
                    break

        self.speech_queue.put(text)

    def _speech_worker(self):
        """Worker thread for speech synthesis."""
        while True:
            try:
                text = self.speech_queue.get()
                self.speaking = True
                self.engine.say(text)
                self.engine.runAndWait()
                self.speaking = False
                self.speech_queue.task_done()
            except Exception as e:
                print(f"Speech error: {e}")

    def start_listening(self, command_callback):
        """
        Start listening for voice commands.

        Args:
            command_callback (function): Callback function for commands.
        """
        if self.microphone is None:
            print("Speech recognition not available")
            return False

        if self.listening:
            return True

        self.command_callback = command_callback
        self.listening = True
        self.listen_thread = threading.Thread(target=self._listen_worker)
        self.listen_thread.daemon = True
        self.listen_thread.start()
        return True

    def stop_listening(self):
        """Stop listening for voice commands."""
        self.listening = False
        if self.listen_thread:
            self.listen_thread.join(timeout=1.0)
            self.listen_thread = None

    def _listen_worker(self):
        """Worker thread for speech recognition."""
        if self.microphone is None:
            return

        while self.listening:
            try:
                # Check if enough time has passed since the last command
                if time.time() - self.last_command_time < self.command_cooldown:
                    time.sleep(0.1)
                    continue

                with self.microphone as source:
                    print("Listening for commands...")
                    audio = self.recognizer.listen(source, timeout=1, phrase_time_limit=5)

                try:
                    command = self.recognizer.recognize_google(audio).lower()
                    print(f"Recognized: {command}")

                    # Update last command time
                    self.last_command_time = time.time()

                    # Process command
                    if self.command_callback:
                        self.command_callback(command)

                except sr.UnknownValueError:
                    # Speech was unintelligible
                    pass
                except sr.RequestError as e:
                    print(f"Could not request results; {e}")

            except Exception as e:
                print(f"Listening error: {e}")
                time.sleep(0.1)

    def provide_navigation_guidance(self, command, distance=None):
        """
        Provide navigation guidance based on the command.

        Args:
            command (str): Navigation command ('left', 'right', 'forward', or 'stop').
            distance (float, optional): Distance to the door in meters.
        """
        if command == "left":
            self.speak("Turn left")
        elif command == "right":
            self.speak("Turn right")
        elif command == "forward":
            self.speak("Go forward")
        elif command == "stop":
            self.speak("Stop")

        # Provide distance information if available
        if distance is not None:
            if distance < 1.0:
                self.speak(f"Door is {distance:.1f} meters away. You are close to the door.", priority=True)
            elif distance < 3.0:
                self.speak(f"Door is {distance:.1f} meters away")

    def announce_door_detected(self, distance=None):
        """
        Announce that a door has been detected.

        Args:
            distance (float, optional): Distance to the door in meters.
        """
        if distance is not None:
            self.speak(f"Door detected, {distance:.1f} meters away")
        else:
            self.speak("Door detected")

    def announce_no_door(self):
        """Announce that no door is detected."""
        self.speak("No door detected. Please look around.")

    def announce_obstacle(self):
        """Announce that an obstacle has been detected."""
        self.speak("Caution! Obstacle detected.", priority=True)


class ARNavigationAssistant:
    """Main AR Navigation Assistant class."""

    def __init__(self,
                 model_path=None,
                 camera_id=0,
                 confidence=0.4,
                 door_class_names=None,
                 frame_width=640,
                 frame_height=480,
                 focal_length=800,
                 known_door_width=0.9):
        """
        Initialize the AR Navigation Assistant.

        Args:
            model_path (str): Path to the YOLOv8 model.
            camera_id (int): Camera device ID.
            confidence (float): Confidence threshold for detections.
            door_class_names (list): List of class names that represent doors.
            frame_width (int): Width of the frame.
            frame_height (int): Height of the frame.
            focal_length (float): Focal length of the camera in pixels.
            known_door_width (float): Known width of a door in meters.
        """
        self.model_path = model_path
        self.camera_id = camera_id
        self.confidence = confidence
        self.door_class_names = door_class_names or ['door', 'Door', 'knob', 'lever', 'hinge']
        self.frame_width = frame_width
        self.frame_height = frame_height

        # Initialize components
        self.door_detector = DoorDetector(model_path, confidence, door_class_names)
        self.distance_estimator = DistanceEstimator(focal_length, known_door_width)
        self.obstacle_detector = ObstacleDetector()
        self.path_planner = PathPlanner(frame_width, frame_height)
        self.voice_assistant = VoiceAssistant()

        # Initialize state variables
        self.cap = None
        self.running = False
        self.navigating = False
        self.door_detected = False
        self.door_distance = None
        self.door_bbox = None
        self.door_center_x = None
        self.door_announced = False
        self.obstacle_announced = False
        self.last_guidance_time = 0
        self.guidance_interval = 3.0  # seconds
        self.close_door_threshold = 1.0  # meters

        # For processing loop
        self.process_thread = None

    def start(self):
        """Start the AR Navigation Assistant."""
        if self.running:
            return

        # Initialize camera
        self.cap = cv2.VideoCapture(self.camera_id)
        self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.frame_width)
        self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.frame_height)

        if not self.cap.isOpened():
            raise ValueError(f"Could not open camera {self.camera_id}")

        # Start processing thread
        self.running = True
        self.process_thread = threading.Thread(target=self._process_loop)
        self.process_thread.daemon = True
        self.process_thread.start()

        # Start voice command recognition
        self.voice_assistant.start_listening(self._process_voice_command)

        # Welcome message
        self.voice_assistant.speak("AR Navigation Assistant started. Say 'navigate' to start navigation.", priority=True)

    def stop(self):
        """Stop the AR Navigation Assistant."""
        self.running = False
        if self.process_thread:
            self.process_thread.join(timeout=1.0)
            self.process_thread = None

        if self.cap:
            self.cap.release()
            self.cap = None

        self.voice_assistant.stop_listening()
        cv2.destroyAllWindows()

    def _process_loop(self):
        """Main processing loop."""
        try:
            while self.running:
                # Capture frame
                ret, frame = self.cap.read()
                if not ret:
                    print("Error: Could not read frame")
                    time.sleep(0.1)
                    continue

                # Process frame
                processed_frame = self._process_frame(frame)

                # Display frame
                cv2.imshow("AR Navigation Assistant", processed_frame)

                # Check for key press
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    self.running = False
                    break
                elif key == ord('n'):
                    self.start_navigation()
                elif key == ord('s'):
                    self.stop_navigation()

        except Exception as e:
            print(f"Error in processing loop: {e}")
        finally:
            if self.cap is not None:
                self.cap.release()
            cv2.destroyAllWindows()

    def _process_frame(self, frame):
        """
        Process a frame.

        Args:
            frame (numpy.ndarray): The image frame.

        Returns:
            numpy.ndarray: Processed frame with AR elements.
        """
        # Detect doors
        annotated_frame, door_detected, door_bbox, door_center_x, door_confidence = self.door_detector.detect(frame)

        # Update state
        self.door_detected = door_detected
        self.door_bbox = door_bbox
        self.door_center_x = door_center_x

        # Estimate distance if door detected
        if door_detected:
            door_width = door_bbox[2] - door_bbox[0]
            self.door_distance = self.distance_estimator.estimate_distance(door_width)

            # Draw distance
            cv2.putText(annotated_frame, f"Distance: {self.door_distance:.1f}m",
                        (door_bbox[0], door_bbox[1] - 30),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        else:
            self.door_distance = None

        # Process navigation if active
        if self.navigating:
            # Detect obstacles
            obstacles = self.obstacle_detector.detect_obstacles(frame, door_bbox if door_detected else None)

            # Draw obstacles
            annotated_frame = self.obstacle_detector.draw_obstacles(annotated_frame, obstacles)

            # Process navigation
            annotated_frame = self._process_navigation(annotated_frame, door_detected, door_bbox, self.door_distance, obstacles)

            # Provide guidance
            self._provide_guidance(door_detected, self.door_distance, door_center_x, obstacles)

        # Add status text
        status_text = "Navigating" if self.navigating else "Ready"
        cv2.putText(annotated_frame, status_text, (10, 30),
                    cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

        return annotated_frame

    def _process_navigation(self, frame, door_detected, door_bbox, door_distance, obstacles):
        """
        Process navigation based on detections.

        Args:
            frame (numpy.ndarray): The image frame.
            door_detected (bool): Whether a door was detected.
            door_bbox (tuple): Door bounding box (x1, y1, x2, y2).
            door_distance (float): Distance to the door in meters.
            obstacles (list): List of obstacle bounding boxes.

        Returns:
            numpy.ndarray: Frame with navigation information drawn.
        """
        if not self.navigating:
            return frame

        # Create obstacle map
        obstacle_map = self.path_planner.create_obstacle_map(obstacles)

        # Define start and goal points
        start_point = (self.frame_width // 2, self.frame_height - 50)  # Bottom center of frame

        if door_detected and door_bbox is not None:
            # Goal is the bottom center of the door
            x1, y1, x2, y2 = door_bbox
            goal_point = ((x1 + x2) // 2, y2)

            # Find path
            path = self.path_planner.find_path(start_point, goal_point, obstacle_map)

            # Draw path
            frame = self.path_planner.draw_path(frame, path)

        return frame

    def _provide_guidance(self, door_detected, door_distance, door_center_x, obstacles):
        """
        Provide navigation guidance.

        Args:
            door_detected (bool): Whether a door was detected.
            door_distance (float): Distance to the door in meters.
            door_center_x (int): X-coordinate of the door center.
            obstacles (list): List of obstacle bounding boxes.
        """
        current_time = time.time()
        if current_time - self.last_guidance_time < self.guidance_interval:
            return

        self.last_guidance_time = current_time

        # Check for obstacles first
        if obstacles and not self.obstacle_announced:
            self.voice_assistant.announce_obstacle()
            self.obstacle_announced = True
            return
        elif not obstacles:
            self.obstacle_announced = False

        if door_detected:
            # Door is detected
            if not self.door_announced:
                # First time detecting the door
                self.voice_assistant.announce_door_detected(door_distance)
                self.door_announced = True

            # Check distance to door
            if door_distance < self.close_door_threshold:
                self.voice_assistant.speak("You have reached the door. Stop.", priority=True)
                return

            # Provide directional guidance
            frame_center_x = self.frame_width // 2
            if abs(door_center_x - frame_center_x) > 50:
                if door_center_x < frame_center_x:
                    self.voice_assistant.speak("Door is to your left")
                else:
                    self.voice_assistant.speak("Door is to your right")
            else:
                self.voice_assistant.speak(f"Door is straight ahead, {door_distance:.1f} meters away")
        else:
            # No door detected
            self.door_announced = False
            self.voice_assistant.announce_no_door()

    def _process_voice_command(self, command):
        """
        Process a voice command.

        Args:
            command (str): Voice command.
        """
        if "navigate" in command or "start" in command:
            self.start_navigation()
        elif "stop" in command:
            self.stop_navigation()
        elif "quit" in command or "exit" in command:
            self.running = False

    def start_navigation(self):
        """Start navigation to the door."""
        if not self.navigating:
            self.navigating = True
            self.door_announced = False
            self.obstacle_announced = False
            self.voice_assistant.speak("Starting navigation to the door. Please move slowly.", priority=True)

            if self.door_detected and self.door_distance is not None:
                self.voice_assistant.announce_door_detected(self.door_distance)
            else:
                self.voice_assistant.announce_no_door()

    def stop_navigation(self):
        """Stop navigation."""
        if self.navigating:
            self.navigating = False
            self.voice_assistant.speak("Navigation stopped.", priority=True)


def main():
    """Main function."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='AR Navigation Assistant')
    parser.add_argument('--model', type=str, default=None, help='Path to YOLOv8 model (None for mock detection)')
    parser.add_argument('--camera', type=int, default=0, help='Camera device ID')
    parser.add_argument('--width', type=int, default=640, help='Frame width')
    parser.add_argument('--height', type=int, default=480, help='Frame height')
    parser.add_argument('--confidence', type=float, default=0.4, help='Detection confidence threshold')
    parser.add_argument('--mock', action='store_true', help='Use mock detection instead of YOLOv8')
    args = parser.parse_args()

    # Use mock detection if --mock is specified or no model is provided
    model_path = None if args.mock else args.model

    # Create AR Navigation Assistant
    assistant = ARNavigationAssistant(
        model_path=model_path,
        camera_id=args.camera,
        confidence=args.confidence,
        frame_width=args.width,
        frame_height=args.height
    )

    try:
        # Start assistant
        assistant.start()

        # Keep main thread alive
        while assistant.running:
            time.sleep(0.1)

    except KeyboardInterrupt:
        print("Interrupted by user")
    finally:
        # Stop assistant
        assistant.stop()


if __name__ == "__main__":
    main()