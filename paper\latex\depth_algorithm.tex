% Depth estimation algorithm for Vision Guard
% This file can be included in the main paper using \input{depth_algorithm.tex}

\begin{algorithm}
\caption{Vision Guard Depth Estimation Algorithm}
\label{alg:depth_estimation}
\begin{algorithmic}[1]
\Require RGB image $I$, door detection bounding box $B = (x_1, y_1, x_2, y_2)$, detection confidence $c$
\Ensure Estimated distance $Z_{final}$ to the door
\State $D \gets \text{MonocularDepthModel}(I)$ \Comment{Generate depth map}
\State $\mathcal{D}_{door} \gets \{D(i,j) | (i,j) \in B\}$ \Comment{Extract depth values in door region}

\State $\tilde{D} \gets \text{median}(\mathcal{D}_{door})$ \Comment{Compute median depth}
\State $\text{MAD} \gets \text{median}(|D_i - \tilde{D}|) \quad \forall D_i \in \mathcal{D}_{door}$ \Comment{Median absolute deviation}
\State $k \gets 2.5$ \Comment{Outlier threshold}
\State $\mathcal{D}_{filtered} \gets \{D_i \in \mathcal{D}_{door} : |D_i - \tilde{D}| < k \cdot \text{MAD}\}$ \Comment{Filter outliers}
\State $Z_{mono} \gets \frac{1}{|\mathcal{D}_{filtered}|} \sum_{D_i \in \mathcal{D}_{filtered}} D_i$ \Comment{Average filtered depths}

\State $W_{real} \gets 0.85$ \Comment{Standard door width in meters}
\State $w_{px} \gets x_2 - x_1$ \Comment{Door width in pixels}
\State $f \gets \text{GetCameraFocalLength}()$ \Comment{Camera focal length in pixels}
\State $Z_{geo} \gets \frac{f \cdot W_{real}}{w_{px}}$ \Comment{Geometric distance estimate}

\State $\gamma \gets 1.5, \delta \gets 0.3$ \Comment{Confidence weighting parameters}
\State $\beta \gets \min(1, \max(0, \gamma \cdot c - \delta))$ \Comment{Confidence factor}
\State $Z_{final} \gets \beta \cdot Z_{geo} + (1-\beta) \cdot Z_{mono}$ \Comment{Combined estimate}

\State \Return $Z_{final}$
\end{algorithmic}
\end{algorithm}
