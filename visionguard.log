2025-06-29 02:26:35,424 - INFO - <PERSON><PERSON><PERSON> model loaded successfully: yolov8n.pt
2025-06-29 02:26:40,236 - INFO - Loaded 166 reference images for door state detection
2025-06-29 02:26:40,237 - ERROR - Failed to initialize voice system: 'Mock' object is not iterable
2025-06-29 02:26:40,238 - ERROR - Failed to initialize UI: Too early to create variable: no default root window
2025-06-29 02:26:40,324 - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-06-29 02:26:40,324 - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\comtypes\gen'
2025-06-29 02:29:34,839 - INFO - YOLO model loaded successfully: yolov8n.pt
2025-06-29 02:29:38,879 - INFO - <PERSON>aded 166 reference images for door state detection
2025-06-29 02:29:38,880 - ERROR - Failed to initialize voice system: 'Mock' object is not iterable
2025-06-29 02:29:38,880 - ERROR - Failed to initialize UI: Too early to create variable: no default root window
2025-06-29 02:29:38,941 - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-06-29 02:29:38,941 - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\comtypes\gen'
