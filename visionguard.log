2025-06-29 02:26:35,424 - INFO - <PERSON><PERSON><PERSON> model loaded successfully: yolov8n.pt
2025-06-29 02:26:40,236 - INFO - Loaded 166 reference images for door state detection
2025-06-29 02:26:40,237 - ERROR - Failed to initialize voice system: 'Mock' object is not iterable
2025-06-29 02:26:40,238 - ERROR - Failed to initialize UI: Too early to create variable: no default root window
2025-06-29 02:26:40,324 - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-06-29 02:26:40,324 - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\comtypes\gen'
2025-06-29 02:29:34,839 - INFO - YOLO model loaded successfully: yolov8n.pt
2025-06-29 02:29:38,879 - INFO - <PERSON>aded 166 reference images for door state detection
2025-06-29 02:29:38,880 - ERROR - Failed to initialize voice system: 'Mock' object is not iterable
2025-06-29 02:29:38,880 - ERROR - Failed to initialize UI: Too early to create variable: no default root window
2025-06-29 02:29:38,941 - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-06-29 02:29:38,941 - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\comtypes\gen'
2025-06-29 02:36:35,088 - INFO - YOLO model loaded successfully: yolov8n.pt
2025-06-29 02:36:39,246 - INFO - Loaded 166 reference images for door state detection
2025-06-29 02:36:39,247 - ERROR - Failed to initialize voice system: 'Mock' object is not iterable
2025-06-29 02:36:39,247 - ERROR - Failed to initialize UI: Too early to create variable: no default root window
2025-06-29 02:36:39,318 - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-06-29 02:36:39,319 - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\comtypes\gen'
2025-06-29 02:37:35,017 - INFO - YOLO model loaded successfully: yolov8n.pt
2025-06-29 02:37:39,123 - INFO - Loaded 166 reference images for door state detection
2025-06-29 02:37:39,152 - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-06-29 02:37:39,152 - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\comtypes\gen'
2025-06-29 02:37:39,227 - ERROR - Voice processing error: 'EnhancedVisionGuard' object has no attribute 'voice_queue'
2025-06-29 02:37:39,227 - INFO - Voice system initialized successfully
2025-06-29 02:37:39,793 - INFO - User interface initialized successfully
2025-06-29 02:38:34,024 - INFO - Camera system initialized successfully
2025-06-29 02:38:35,145 - INFO - Voice command system initialized successfully
2025-06-29 02:38:35,145 - INFO - Enhanced VisionGuard system initialized successfully
2025-06-29 02:38:35,145 - INFO - Starting Enhanced VisionGuard application
2025-06-29 02:38:41,145 - INFO - Voice: Enhanced VisionGuard system ready. Use the interface buttons or voice commands to navigate.
2025-06-29 02:38:47,883 - INFO - YOLO model loaded successfully: yolov8n.pt
2025-06-29 02:38:52,045 - INFO - Loaded 166 reference images for door state detection
2025-06-29 02:38:52,075 - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-06-29 02:38:52,075 - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\comtypes\gen'
2025-06-29 02:38:52,153 - INFO - Voice system initialized successfully
2025-06-29 02:38:52,380 - INFO - User interface initialized successfully
2025-06-29 02:39:46,506 - INFO - Camera system initialized successfully
2025-06-29 02:39:47,627 - INFO - Voice command system initialized successfully
2025-06-29 02:39:47,627 - INFO - Enhanced VisionGuard system initialized successfully
2025-06-29 02:39:47,627 - INFO - Starting Enhanced VisionGuard application
2025-06-29 02:39:53,663 - INFO - Voice: Enhanced VisionGuard system ready. Use the interface buttons or voice commands to navigate.
2025-06-29 02:39:54,758 - INFO - YOLO model loaded successfully: yolov8n.pt
2025-06-29 02:39:59,091 - INFO - Loaded 166 reference images for door state detection
2025-06-29 02:39:59,091 - ERROR - Failed to initialize voice system: 'Mock' object is not iterable
2025-06-29 02:39:59,092 - ERROR - Failed to initialize UI: Too early to create variable: no default root window
2025-06-29 02:39:59,165 - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-06-29 02:39:59,165 - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\comtypes\gen'
2025-06-29 02:40:12,278 - INFO - Navigation started
2025-06-29 02:40:16,856 - INFO - Voice: Navigation started. Please move slowly and listen for guidance.
2025-06-29 02:40:19,727 - INFO - System status - Navigating: True, Door detected: False, Voice commands: False, Obstacles: 0
2025-06-29 02:40:21,287 - INFO - Voice: Searching for door. Please turn slowly to scan the area.
2025-06-29 02:40:25,719 - INFO - Voice: Searching for door. Please turn slowly to scan the area.
2025-06-29 02:40:30,200 - INFO - Voice: Searching for door. Please turn slowly to scan the area.
2025-06-29 02:40:34,631 - INFO - Voice: Searching for door. Please turn slowly to scan the area.
2025-06-29 02:40:39,112 - INFO - Voice: Searching for door. Please turn slowly to scan the area.
2025-06-29 02:40:43,541 - INFO - Voice: Searching for door. Please turn slowly to scan the area.
2025-06-29 02:40:45,447 - INFO - Voice recognition started
2025-06-29 02:40:46,476 - INFO - Voice recognition stopped
2025-06-29 02:40:48,076 - INFO - Voice: Searching for door. Please turn slowly to scan the area.
2025-06-29 02:40:49,729 - INFO - System status - Navigating: True, Door detected: False, Voice commands: False, Obstacles: 0
2025-06-29 02:40:49,840 - INFO - Voice command recognized: please turn slowly to stanley area
2025-06-29 02:40:50,470 - INFO - Voice: Voice commands deactivated.
2025-06-29 02:40:52,539 - INFO - Voice recognition started
2025-06-29 02:40:54,728 - INFO - Navigation stopped
2025-06-29 02:40:54,728 - INFO - Voice recognition stopped
2025-06-29 02:40:55,026 - INFO - Application quit successfully
2025-06-29 02:40:55,049 - INFO - Voice: Searching for door. Please turn slowly to scan the area.
2025-06-29 02:40:55,059 - INFO - UI closed by user
2025-06-29 02:40:55,059 - ERROR - Error during application quit: can't invoke "destroy" command: application has been destroyed
