import os
import matplotlib.pyplot as plt
import numpy as np
from ultralytics import YOLO
import cv2
import random

def plot_training_results(results_file='runs/train/yolov8_door_detection/results.csv'):
    """
    Plot training results from the results.csv file.
    
    Args:
        results_file (str): Path to the results.csv file
    """
    try:
        import pandas as pd
        
        # Load results
        results = pd.read_csv(results_file)
        
        # Create figure with subplots
        fig, axs = plt.subplots(2, 2, figsize=(12, 10))
        
        # Plot training loss
        axs[0, 0].plot(results['epoch'], results['train/box_loss'], label='box_loss')
        axs[0, 0].plot(results['epoch'], results['train/cls_loss'], label='cls_loss')
        axs[0, 0].plot(results['epoch'], results['train/dfl_loss'], label='dfl_loss')
        axs[0, 0].set_title('Training Loss')
        axs[0, 0].set_xlabel('Epoch')
        axs[0, 0].set_ylabel('Loss')
        axs[0, 0].legend()
        
        # Plot validation metrics
        axs[0, 1].plot(results['epoch'], results['metrics/precision(B)'], label='Precision')
        axs[0, 1].plot(results['epoch'], results['metrics/recall(B)'], label='Recall')
        axs[0, 1].plot(results['epoch'], results['metrics/mAP50(B)'], label='mAP50')
        axs[0, 1].plot(results['epoch'], results['metrics/mAP50-95(B)'], label='mAP50-95')
        axs[0, 1].set_title('Validation Metrics')
        axs[0, 1].set_xlabel('Epoch')
        axs[0, 1].set_ylabel('Metric')
        axs[0, 1].legend()
        
        # Plot learning rate
        axs[1, 0].plot(results['epoch'], results['lr/pg0'], label='Learning Rate')
        axs[1, 0].set_title('Learning Rate')
        axs[1, 0].set_xlabel('Epoch')
        axs[1, 0].set_ylabel('Learning Rate')
        
        # Plot validation loss
        axs[1, 1].plot(results['epoch'], results['val/box_loss'], label='val_box_loss')
        axs[1, 1].plot(results['epoch'], results['val/cls_loss'], label='val_cls_loss')
        axs[1, 1].plot(results['epoch'], results['val/dfl_loss'], label='val_dfl_loss')
        axs[1, 1].set_title('Validation Loss')
        axs[1, 1].set_xlabel('Epoch')
        axs[1, 1].set_ylabel('Loss')
        axs[1, 1].legend()
        
        plt.tight_layout()
        plt.savefig('training_results.png')
        plt.close()
        
        print(f"Training results plot saved to 'training_results.png'")
    except Exception as e:
        print(f"Error plotting results: {e}")

def visualize_predictions(
    model_path='runs/train/yolov8_door_detection/weights/best.pt',
    test_dir='test/images',
    output_dir='runs/visualize',
    conf=0.25,
    num_samples=5
):
    """
    Visualize predictions from the trained YOLOv8 model on test images.
    
    Args:
        model_path (str): Path to the trained model weights
        test_dir (str): Directory containing test images
        output_dir (str): Directory to save visualization results
        conf (float): Confidence threshold for detections
        num_samples (int): Number of random samples to visualize
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Load the model
    model = YOLO(model_path)
    
    # Get list of test images
    test_images = []
    for ext in ['jpg', 'jpeg', 'png']:
        test_images.extend([os.path.join(test_dir, f) for f in os.listdir(test_dir) if f.lower().endswith(f'.{ext}')])
    
    if not test_images:
        print(f"No images found in {test_dir}")
        return
    
    # Select random samples if there are more images than num_samples
    if len(test_images) > num_samples:
        test_images = random.sample(test_images, num_samples)
    
    print(f"Visualizing predictions on {len(test_images)} images...")
    
    # Create a figure with subplots
    fig, axs = plt.subplots(len(test_images), 1, figsize=(10, 5 * len(test_images)))
    if len(test_images) == 1:
        axs = [axs]
    
    # Process each test image
    for i, img_path in enumerate(test_images):
        # Run inference
        results = model(img_path, conf=conf)
        
        # Get the base filename
        base_name = os.path.basename(img_path)
        
        # Get the image with detections
        img_with_boxes = results[0].plot()
        
        # Convert from BGR to RGB for matplotlib
        img_with_boxes = cv2.cvtColor(img_with_boxes, cv2.COLOR_BGR2RGB)
        
        # Display the image with detections
        axs[i].imshow(img_with_boxes)
        axs[i].set_title(f"Detections in {base_name}")
        axs[i].axis('off')
        
        # Save the individual image with detections
        output_path = os.path.join(output_dir, f"detect_{base_name}")
        cv2.imwrite(output_path, results[0].plot())
        
        print(f"Detection saved to {output_path}")
        
        # Print detection information
        boxes = results[0].boxes
        if len(boxes) > 0:
            print(f"Found {len(boxes)} objects in {base_name}:")
            for box in boxes:
                cls = int(box.cls[0])
                cls_name = model.names[cls]
                conf = float(box.conf[0])
                print(f"  - {cls_name}: {conf:.2f}")
        else:
            print(f"No objects detected in {base_name}")
    
    # Save the figure with all visualizations
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'all_detections.png'))
    plt.close()
    
    print(f"All detections saved to {os.path.join(output_dir, 'all_detections.png')}")

def visualize_confusion_matrix(model_path='runs/train/yolov8_door_detection/weights/best.pt'):
    """
    Visualize the confusion matrix from the trained YOLOv8 model.
    
    Args:
        model_path (str): Path to the trained model weights
    """
    # Load the model
    model = YOLO(model_path)
    
    # Get the confusion matrix
    conf_matrix_path = os.path.join(os.path.dirname(model_path), '../confusion_matrix.png')
    
    if os.path.exists(conf_matrix_path):
        print(f"Confusion matrix found at {conf_matrix_path}")
        
        # Display the confusion matrix
        img = cv2.imread(conf_matrix_path)
        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        
        plt.figure(figsize=(10, 10))
        plt.imshow(img)
        plt.axis('off')
        plt.title('Confusion Matrix')
        plt.savefig('confusion_matrix_display.png')
        plt.close()
        
        print("Confusion matrix visualization saved to 'confusion_matrix_display.png'")
    else:
        print(f"Confusion matrix not found at {conf_matrix_path}")

if __name__ == "__main__":
    # Plot training results
    results_file = "runs/train/yolov8_door_detection/results.csv"
    if os.path.exists(results_file):
        print("Plotting training results...")
        plot_training_results(results_file)
    else:
        print(f"Results file not found at {results_file}")
    
    # Visualize predictions
    model_path = "runs/train/yolov8_door_detection/weights/best.pt"
    if os.path.exists(model_path):
        print("Visualizing predictions...")
        visualize_predictions(
            model_path=model_path,
            test_dir='test/images',
            output_dir='runs/visualize',
            conf=0.25,
            num_samples=5
        )
        
        # Visualize confusion matrix
        print("Visualizing confusion matrix...")
        visualize_confusion_matrix(model_path)
    else:
        print(f"Model not found at {model_path}")
