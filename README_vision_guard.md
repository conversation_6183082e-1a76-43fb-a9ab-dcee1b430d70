# Vision Guard: Vision Assistance System for Visually Impaired Individuals

Vision Guard is a comprehensive vision assistance system designed to help visually impaired individuals navigate their environment, with a focus on door detection and obstacle avoidance.

## Features

- **Door Detection**: Detects doors and provides directional guidance (left, right, forward)
- **Obstacle Detection**: Identifies obstacles in the path and provides warnings
- **Depth Estimation**: Estimates distance to objects using monocular depth estimation
- **Path Planning**: Plans a safe path around obstacles to reach the door
- **Voice Commands**: Accepts voice commands for hands-free operation
- **Voice Guidance**: Provides audio feedback for navigation
- **Multiple Views**: Displays various analysis views for better understanding
  - Main camera view with annotations
  - Depth map visualization
  - Left and right perspective views
  - Obstacle-only view
  - Door-only view

## Requirements

- Python 3.8+
- OpenCV
- NumPy
- Ultralytics YOLOv8
- pyttsx3 (for text-to-speech)
- SpeechRecognition (for voice commands)
- scipy (for path planning)

## Installation

1. Install the required packages:
   ```
   pip install opencv-python numpy ultralytics pyttsx3 SpeechRecognition scipy
   ```

2. Make sure you have a trained YOLOv8 model for door detection. The default path is:
   ```
   runs/train/yolov8_door_detection/weights/best.pt
   ```

## Usage

1. Run the Vision Guard system:
   ```
   python vision_guard.py
   ```

2. Key Controls:
   - `n`: Start navigation to the door
   - `s`: Stop navigation
   - `v`: Toggle voice command recognition
   - `q`: Quit the application

3. Voice Commands:
   - "Take me to the door" or "Find door": Start navigation to the door
   - "Stop": Stop navigation
   - "Where is the door": Get information about the door's location
   - "What's in front of me": Get information about obstacles in your path

## System Windows

The system displays multiple windows:

1. **Vision Guard - Camera**: Raw camera feed
2. **Vision Guard - Main**: Main view with all detections, path planning, and navigation arrows
3. **Vision Guard - Analysis**: Grid view with multiple analysis frames:
   - Depth map
   - Left perspective view
   - Right perspective view
   - Obstacle detection
   - Door detection

## How It Works

### Door Detection
The system uses YOLOv8 to detect doors in the camera feed. When a door is detected, the system calculates its distance and direction relative to the user.

### Obstacle Detection
The system also detects obstacles in the path and provides warnings when they are too close or blocking the path to the door.

### Depth Estimation
A monocular depth estimation algorithm is used to create a depth map of the scene, which helps in understanding the 3D structure of the environment.

### Path Planning
When both a door and obstacles are detected, the system plans a safe path around the obstacles to reach the door using a potential field approach.

### Voice Guidance
The system provides voice guidance based on the detected door and obstacles, helping the user navigate safely to the door.

## Customization

You can customize various parameters in the `VisionGuard` class:

- `frame_width` and `frame_height`: Camera resolution
- `confidence`: Detection confidence threshold
- `door_class_names`: List of class names that represent doors
- `obstacle_class_names`: List of class names that represent obstacles
- `close_door_threshold`: Distance threshold for considering a door as reached
- `obstacle_warning_threshold`: Distance threshold for warning about obstacles

## Research Applications

This system is designed for research purposes to explore how computer vision can assist visually impaired individuals. Key research areas include:

1. Improving door detection accuracy in various lighting conditions
2. Enhancing depth estimation from a single camera
3. Developing more intuitive voice guidance systems
4. Optimizing path planning algorithms for real-time navigation

## Future Improvements

- Integration with more advanced depth sensing (stereo vision, ToF sensors)
- Improved obstacle classification for more specific guidance
- Enhanced path planning with dynamic obstacle avoidance
- User interface improvements for better accessibility
- Support for additional navigation targets beyond doors
