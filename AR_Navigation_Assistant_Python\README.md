# AR Navigation Assistant (Python)

A voice-controlled assistant system that helps users navigate to doors with AR path visualization.

## Features

- **Door Detection**: Uses YOLOv8 to detect doors and door components
- **AR Path Visualization**: Displays a path to the detected door
- **Distance Estimation**: Estimates the distance to doors using monocular vision
- **Voice Guidance**: Provides voice commands to guide the user to the door
- **Obstacle Detection**: Detects obstacles in the path using motion detection
- **Voice Control**: Responds to voice commands for navigation

## Requirements

- Python 3.7+
- OpenCV
- NumPy
- PyTorch (for YOLOv8)
- Ultralytics YOLOv8
- pyttsx3 (for text-to-speech)
- SpeechRecognition (for voice commands)

## Installation

1. Install the required packages:

```bash
pip install ultralytics pyttsx3 SpeechRecognition opencv-python numpy
```

2. Clone this repository:

```bash
git clone https://github.com/yourusername/ar-navigation-assistant.git
cd ar-navigation-assistant
```

## Usage

Run the AR Navigation Assistant:

```bash
python ar_navigation_assistant.py
```

### Command Line Arguments

- `--model`: Path to the YOLOv8 model (default: None for mock detection)
- `--camera`: Camera device ID (default: 0)
- `--width`: Frame width (default: 640)
- `--height`: Frame height (default: 480)
- `--confidence`: Detection confidence threshold (default: 0.4)
- `--mock`: Use mock detection instead of YOLOv8

Example:

```bash
# Run with mock detection (no model required)
python ar_navigation_assistant.py --mock

# Run with a trained YOLOv8 model
python ar_navigation_assistant.py --model path/to/model.pt
```

### Controls

- Press 'n' to start navigation
- Press 's' to stop navigation
- Press 'q' to quit

### Voice Commands

- Say "navigate" or "start" to start navigation
- Say "stop" to stop navigation
- Say "quit" or "exit" to exit the application

## How It Works

1. **Door Detection**: The system uses YOLOv8 to detect doors in the camera feed.
2. **Distance Estimation**: When a door is detected, the system estimates the distance to the door based on its apparent size.
3. **Path Planning**: The system plans a path from the user to the door, avoiding obstacles.
4. **Voice Guidance**: The system provides voice guidance to help the user navigate to the door.
5. **Obstacle Detection**: The system detects obstacles using motion detection and warns the user.

## Components

- **DoorDetector**: Detects doors using YOLOv8
- **DistanceEstimator**: Estimates distance to doors
- **PathPlanner**: Plans paths to doors using A* algorithm
- **ObstacleDetector**: Detects obstacles using motion detection
- **VoiceAssistant**: Provides voice guidance and processes voice commands
- **ARNavigationAssistant**: Main class that coordinates all components

## Training Your Own Model

To train your own YOLOv8 model for door detection:

1. Collect and annotate door images
2. Train a YOLOv8 model using the Ultralytics framework:

```bash
# Install Ultralytics
pip install ultralytics

# Train a model
yolo train data=path/to/data.yaml model=yolov8n.pt epochs=100
```

3. Use the trained model with the AR Navigation Assistant:

```bash
python ar_navigation_assistant.py --model runs/train/exp/weights/best.pt
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Ultralytics for the YOLOv8 framework
- OpenCV for computer vision capabilities
- PyTorch for deep learning
