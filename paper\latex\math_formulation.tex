% Mathematical formulation for the Vision Guard depth estimation algorithm
% This file can be included in the main paper using \input{math_formulation.tex}

\subsection{Mathematical Formulation of Depth Estimation}

Our depth estimation approach combines monocular depth prediction with geometric constraints specific to doors. The monocular depth estimation network $f_\theta$ predicts a depth map $D$ from a single RGB image $I$:

\begin{equation}
D = f_\theta(I)
\end{equation}

where $\theta$ represents the network parameters learned during training. The network architecture follows an encoder-decoder structure with skip connections, where the encoder $E$ extracts features at multiple scales, and the decoder $G$ reconstructs the depth map:

\begin{equation}
D = G(E(I))
\end{equation}

The network is trained to minimize a combination of reconstruction loss $\mathcal{L}_{rec}$ and smoothness loss $\mathcal{L}_{smooth}$:

\begin{equation}
\mathcal{L} = \mathcal{L}_{rec} + \lambda \mathcal{L}_{smooth}
\end{equation}

where $\lambda$ is a weighting factor. The reconstruction loss is defined as:

\begin{equation}
\mathcal{L}_{rec} = \frac{1}{N} \sum_{i=1}^{N} \left( \alpha \cdot \frac{|D_i - D_i^*|}{D_i^*} + (1-\alpha) \cdot \frac{|D_i - D_i^*|^2}{D_i^*} \right)
\end{equation}

where $D_i$ is the predicted depth at pixel $i$, $D_i^*$ is the ground truth depth, $N$ is the number of pixels, and $\alpha$ is a parameter that balances the scale-invariant and scale-dependent terms.

For door detection specifically, we refine the depth estimate using geometric constraints. Given a detected door with bounding box width $w_{px}$ in pixels, and assuming a standard door width $W_{real}$ (typically 0.85m), we can estimate the distance $Z$ using the pinhole camera model:

\begin{equation}
Z = \frac{f \cdot W_{real}}{w_{px}}
\end{equation}

where $f$ is the camera's focal length in pixels. This geometric estimate $Z_{geo}$ is combined with the monocular depth prediction $Z_{mono}$ using a weighted average:

\begin{equation}
Z_{final} = \beta \cdot Z_{geo} + (1-\beta) \cdot Z_{mono}
\end{equation}

where $\beta$ is a confidence factor that depends on the door detection confidence score $c$:

\begin{equation}
\beta = \min(1, \max(0, \gamma \cdot c - \delta))
\end{equation}

with $\gamma$ and $\delta$ being hyperparameters that control the influence of the detection confidence on the weighting.

To handle potential outliers in the depth map, we apply statistical filtering to the depth values within the door bounding box. Let $\mathcal{D}_{door}$ be the set of depth values within the door region. We compute the median $\tilde{D}$ and median absolute deviation (MAD):

\begin{equation}
\text{MAD} = \text{median}(|D_i - \tilde{D}|) \quad \forall D_i \in \mathcal{D}_{door}
\end{equation}

We then filter out values that deviate significantly from the median:

\begin{equation}
\mathcal{D}_{filtered} = \{D_i \in \mathcal{D}_{door} : |D_i - \tilde{D}| < k \cdot \text{MAD}\}
\end{equation}

where $k$ is a threshold parameter (typically set to 2.5). The final monocular depth estimate $Z_{mono}$ is computed as the mean of the filtered values:

\begin{equation}
Z_{mono} = \frac{1}{|\mathcal{D}_{filtered}|} \sum_{D_i \in \mathcal{D}_{filtered}} D_i
\end{equation}

This combined approach leverages both the learned depth features from the neural network and the known geometric properties of doors, resulting in more accurate distance estimates for navigation assistance.
