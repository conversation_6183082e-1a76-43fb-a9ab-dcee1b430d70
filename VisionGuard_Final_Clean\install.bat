@echo off
echo ========================================
echo VisionGuard Installation Script
echo ========================================
echo.

echo Installing Python dependencies...
pip install -r requirements.txt

echo.
echo Checking for required models...
if not exist "runs\train\yolov8_door_detection\weights\best.pt" (
    echo ERROR: Door detection model not found!
    echo Please ensure the runs folder is properly copied.
    pause
    exit /b 1
)

if not exist "yolov8n.pt" (
    echo ERROR: Obstacle detection model not found!
    echo Please ensure yolov8n.pt is in the current directory.
    pause
    exit /b 1
)

if not exist "door_models\best_door_classifier.pkl" (
    echo ERROR: Door classifier model not found!
    echo Please ensure the door_models folder is properly copied.
    pause
    exit /b 1
)

echo.
echo ========================================
echo Installation completed successfully!
echo ========================================
echo.
echo To run VisionGuard:
echo python vision_guard_clean.py
echo.
echo Controls:
echo N - Start navigation
echo S - Stop navigation  
echo V - Toggle voice commands
echo Q - Quit
echo.
pause
